{"version": 3, "sources": ["../src/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z, type Action } from '@genkit-ai/core';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\n\nexport const LlmStatsSchema = z.object({\n  latencyMs: z.number().optional(),\n  inputTokenCount: z.number().optional(),\n  outputTokenCount: z.number().optional(),\n});\n\nexport type LlmStats = z.infer<typeof LlmStatsSchema>;\n\nexport const ToolSchema = z.object({\n  name: z.string(),\n  description: z.string().optional(),\n  schema: z.any(),\n});\n\nexport type Tool = z.infer<typeof ToolSchema>;\n\nexport const ToolCallSchema = z.object({\n  toolName: z.string(),\n  arguments: z.any(),\n});\n\nexport type ToolCall = z.infer<typeof ToolCallSchema>;\n\nexport const LlmResponseSchema = z.object({\n  completion: z.string(),\n  toolCalls: z.array(ToolCallSchema).optional(),\n  stats: LlmStatsSchema,\n});\n\nexport type LlmResponse = z.infer<typeof LlmResponseSchema>;\n\n/**\n * Converts actions to tool definition sent to model inputs.\n */\nexport function toToolWireFormat(\n  actions?: Action<any, any>[]\n): z.infer<typeof ToolSchema>[] | undefined {\n  if (!actions) return undefined;\n  return actions.map((a) => {\n    return {\n      name: a.__action.name,\n      description: a.__action.description,\n      schema: {\n        input: toJsonSchema({\n          schema: a.__action.inputSchema,\n          jsonSchema: a.__action.inputJsonSchema,\n        }),\n        output: toJsonSchema({\n          schema: a.__action.outputSchema,\n          jsonSchema: a.__action.outputJsonSchema,\n        }),\n      },\n    };\n  });\n}\n"], "mappings": "AAgBA,SAAS,SAAsB;AAC/B,SAAS,oBAAoB;AAEtB,MAAM,iBAAiB,EAAE,OAAO;AAAA,EACrC,WAAW,EAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,iBAAiB,EAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,EAAE,OAAO,EAAE,SAAS;AACxC,CAAC;AAIM,MAAM,aAAa,EAAE,OAAO;AAAA,EACjC,MAAM,EAAE,OAAO;AAAA,EACf,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,QAAQ,EAAE,IAAI;AAChB,CAAC;AAIM,MAAM,iBAAiB,EAAE,OAAO;AAAA,EACrC,UAAU,EAAE,OAAO;AAAA,EACnB,WAAW,EAAE,IAAI;AACnB,CAAC;AAIM,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,YAAY,EAAE,OAAO;AAAA,EACrB,WAAW,EAAE,MAAM,cAAc,EAAE,SAAS;AAAA,EAC5C,OAAO;AACT,CAAC;AAOM,SAAS,iBACd,SAC0C;AAC1C,MAAI,CAAC,QAAS,QAAO;AACrB,SAAO,QAAQ,IAAI,CAAC,MAAM;AACxB,WAAO;AAAA,MACL,MAAM,EAAE,SAAS;AAAA,MACjB,aAAa,EAAE,SAAS;AAAA,MACxB,QAAQ;AAAA,QACN,OAAO,aAAa;AAAA,UAClB,QAAQ,EAAE,SAAS;AAAA,UACnB,YAAY,EAAE,SAAS;AAAA,QACzB,CAAC;AAAA,QACD,QAAQ,aAAa;AAAA,UACnB,QAAQ,EAAE,SAAS;AAAA,UACnB,YAAY,EAAE,SAAS;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;", "names": []}