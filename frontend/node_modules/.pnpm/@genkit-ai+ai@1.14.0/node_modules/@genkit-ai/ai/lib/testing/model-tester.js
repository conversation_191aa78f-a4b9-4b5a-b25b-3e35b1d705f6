"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var model_tester_exports = {};
__export(model_tester_exports, {
  testModels: () => testModels
});
module.exports = __toCommonJS(model_tester_exports);
var import_core = require("@genkit-ai/core");
var import_tracing = require("@genkit-ai/core/tracing");
var assert = __toESM(require("assert"));
var import_generate = require("../generate");
var import_tool = require("../tool");
const tests = {
  "basic hi": async (registry, model) => {
    const response = await (0, import_generate.generate)(registry, {
      model,
      prompt: 'just say "Hi", literally'
    });
    const got = response.text.trim();
    assert.match(got, /Hi/i);
  },
  multimodal: async (registry, model) => {
    const resolvedModel = await registry.lookupAction(
      `/model/${model}`
    );
    if (!resolvedModel.__action.metadata?.model.supports?.media) {
      skip();
    }
    const response = await (0, import_generate.generate)(registry, {
      model,
      prompt: [
        {
          media: {
            url: "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kT1Iw0AcxV9TpSoVETOIOGSoulgQFXHUKhShQqgVWnUwufRDaNKQtLg4Cq4FBz8Wqw4uzro6uAqC4AeIs4OToouU+L+k0CLGg+N+vLv3uHsHCLUi0+22MUA3ylYyHpPSmRUp9IpOhCCiFyMKs81ZWU7Ad3zdI8DXuyjP8j/35+jWsjYDAhLxDDOtMvE68dRm2eS8TyyygqIRnxOPWnRB4keuqx6/cc67LPBM0Uol54hFYinfwmoLs4KlE08SRzTdoHwh7bHGeYuzXqywxj35C8NZY3mJ6zQHEccCFiFDgooKNlBEGVFaDVJsJGk/5uMfcP0yuVRybYCRYx4l6FBcP/gf/O7Wzk2Me0nhGND+4jgfQ0BoF6hXHef72HHqJ0DwGbgymv5SDZj+JL3a1CJHQM82cHHd1NQ94HIH6H8yFUtxpSBNIZcD3s/omzJA3y3Qter11tjH6QOQoq4SN8DBITCcp+w1n3d3tPb275lGfz9aC3Kd0jYiSQAAAAlwSFlzAAAuIwAALiMBeKU/dgAAAAd0SU1FB+gJBxQRO1/5qB8AAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAsUlEQVQoz61SMQqEMBDcO5SYToUE/IBPyRMCftAH+INUviApUwYjNkKCVcTiQK7IHSw45czODrMswCOQUkopEQZjzDiOWemdZfu+b5oGYYgx1nWNMPwB2vACAK01Y4wQ8qGqqirL8jzPlNI9t64r55wQUgBA27be+xDCfaJhGJxzSqnv3UKIn7ne+2VZEB2stZRSRLN93+d5RiRs28Y5RySEEI7jyEpFlp2mqeu6Zx75ApQwPdsIcq0ZAAAAAElFTkSuQmCC"
          }
        },
        {
          text: "what math operation is this? plus, minus, multiply or divide?"
        }
      ]
    });
    const want = /plus/i;
    const got = response.text.trim();
    assert.match(got, want);
  },
  history: async (registry, model) => {
    const resolvedModel = await registry.lookupAction(
      `/model/${model}`
    );
    if (!resolvedModel.__action.metadata?.model.supports?.multiturn) {
      skip();
    }
    const response1 = await (0, import_generate.generate)(registry, {
      model,
      prompt: "My name is Glorb"
    });
    const response = await (0, import_generate.generate)(registry, {
      model,
      prompt: "What's my name?",
      messages: response1.messages
    });
    const got = response.text.trim();
    assert.match(got, /Glorb/);
  },
  "system prompt": async (registry, model) => {
    const { text } = await (0, import_generate.generate)(registry, {
      model,
      prompt: "Hi",
      messages: [
        {
          role: "system",
          content: [
            {
              text: 'If the user says "Hi", just say "Bye" '
            }
          ]
        }
      ]
    });
    const want = "Bye";
    const got = text.trim();
    assert.equal(got, want);
  },
  "structured output": async (registry, model) => {
    const response = await (0, import_generate.generate)(registry, {
      model,
      prompt: "extract data as json from: Jack was a Lumberjack",
      output: {
        format: "json",
        schema: import_core.z.object({
          name: import_core.z.string(),
          occupation: import_core.z.string()
        })
      }
    });
    const want = {
      name: "Jack",
      occupation: "Lumberjack"
    };
    const got = response.output;
    assert.deepEqual(want, got);
  },
  "tool calling": async (registry, model) => {
    const resolvedModel = await registry.lookupAction(
      `/model/${model}`
    );
    if (!resolvedModel.__action.metadata?.model.supports?.tools) {
      skip();
    }
    const { text } = await (0, import_generate.generate)(registry, {
      model,
      prompt: "what is a gablorken of 2? use provided tool",
      tools: ["gablorkenTool"]
    });
    const got = text.trim();
    assert.match(got, /9.407/);
  }
};
async function testModels(registry, models) {
  (0, import_tool.defineTool)(
    registry,
    {
      name: "gablorkenTool",
      description: "use when need to calculate a gablorken",
      inputSchema: import_core.z.object({
        value: import_core.z.number()
      }),
      outputSchema: import_core.z.number()
    },
    async (input) => {
      return Math.pow(input.value, 3) + 1.407;
    }
  );
  return await (0, import_tracing.runInNewSpan)(
    registry,
    { metadata: { name: "testModels" } },
    async () => {
      const report = [];
      for (const test of Object.keys(tests)) {
        await (0, import_tracing.runInNewSpan)(registry, { metadata: { name: test } }, async () => {
          report.push({
            description: test,
            models: []
          });
          const caseReport = report[report.length - 1];
          for (const model of models) {
            caseReport.models.push({
              name: model,
              passed: true
              // optimistically
            });
            const modelReport = caseReport.models[caseReport.models.length - 1];
            try {
              await tests[test](registry, model);
            } catch (e) {
              modelReport.passed = false;
              if (e instanceof SkipTestError) {
                modelReport.skipped = true;
              } else if (e instanceof Error) {
                modelReport.error = {
                  message: e.message,
                  stack: e.stack
                };
              } else {
                modelReport.error = {
                  message: `${e}`
                };
              }
            }
          }
        });
      }
      return report;
    }
  );
}
class SkipTestError extends Error {
}
function skip() {
  throw new SkipTestError();
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  testModels
});
//# sourceMappingURL=model-tester.js.map