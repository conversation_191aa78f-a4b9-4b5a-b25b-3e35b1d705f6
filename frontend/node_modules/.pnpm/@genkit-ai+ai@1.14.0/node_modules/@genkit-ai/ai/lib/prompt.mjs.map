{"version": 3, "sources": ["../src/prompt.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenkitError,\n  defineActionAsync,\n  getContext,\n  stripUndefinedProps,\n  type Action,\n  type ActionAsyncParams,\n  type ActionContext,\n  type JSONSchema7,\n  type z,\n} from '@genkit-ai/core';\nimport { lazy } from '@genkit-ai/core/async';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from '@genkit-ai/core/tracing';\nimport { Message as DpMessage, PromptFunction } from 'dotprompt';\nimport { existsSync, readFileSync, readdirSync } from 'fs';\nimport { basename, join, resolve } from 'path';\nimport type { DocumentData } from './document.js';\nimport {\n  generate,\n  generateStream,\n  toGenerateActionOptions,\n  toGenerateRequest,\n  type GenerateOptions,\n  type GenerateResponse,\n  type GenerateStreamResponse,\n  type OutputOptions,\n  type ToolChoice,\n} from './generate.js';\nimport { Message } from './message.js';\nimport {\n  GenerateActionOptionsSchema,\n  type GenerateActionOptions,\n  type GenerateRequest,\n  type GenerateRequestSchema,\n  type GenerateResponseChunkSchema,\n  type GenerateResponseSchema,\n  type MessageData,\n  type ModelAction,\n  type ModelArgument,\n  type ModelMiddleware,\n  type ModelReference,\n  type Part,\n} from './model.js';\nimport { getCurrentSession, type Session } from './session.js';\nimport type { ToolAction, ToolArgument } from './tool.js';\n\n/**\n * Prompt action.\n */\nexport type PromptAction<I extends z.ZodTypeAny = z.ZodTypeAny> = Action<\n  I,\n  typeof GenerateRequestSchema,\n  z.ZodNever\n> & {\n  __action: {\n    metadata: {\n      type: 'prompt';\n    };\n  };\n  __executablePrompt: ExecutablePrompt<I>;\n};\n\nexport function isPromptAction(action: Action): action is PromptAction {\n  return action.__action.metadata?.type === 'prompt';\n}\n\n/**\n * Prompt action.\n */\nexport type ExecutablePromptAction<I extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<\n    I,\n    typeof GenerateResponseSchema,\n    typeof GenerateResponseChunkSchema\n  > & {\n    __action: {\n      metadata: {\n        type: 'executablePrompt';\n      };\n    };\n    __executablePrompt: ExecutablePrompt<I>;\n  };\n\n/**\n * Configuration for a prompt action.\n */\nexport interface PromptConfig<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  name: string;\n  variant?: string;\n  model?: ModelArgument<CustomOptions>;\n  config?: z.infer<CustomOptions>;\n  description?: string;\n  input?: {\n    schema?: I;\n    jsonSchema?: JSONSchema7;\n  };\n  system?: string | Part | Part[] | PartsResolver<z.infer<I>>;\n  prompt?: string | Part | Part[] | PartsResolver<z.infer<I>>;\n  messages?: string | MessageData[] | MessagesResolver<z.infer<I>>;\n  docs?: DocumentData[] | DocsResolver<z.infer<I>>;\n  output?: OutputOptions<O>;\n  maxTurns?: number;\n  returnToolRequests?: boolean;\n  metadata?: Record<string, any>;\n  tools?: ToolArgument[];\n  toolChoice?: ToolChoice;\n  use?: ModelMiddleware[];\n  context?: ActionContext;\n}\n\n/**\n * Generate options of a prompt.\n */\nexport type PromptGenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = Omit<GenerateOptions<O, CustomOptions>, 'prompt' | 'system'>;\n\n/**\n * A prompt that can be executed as a function.\n */\nexport interface ExecutablePrompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /**\n   * Generates a response by rendering the prompt template with given user input and then calling the model.\n   *\n   * @param input Prompt inputs.\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns the model response as a promise of `GenerateStreamResponse`.\n   */\n  (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateResponse<z.infer<O>>>;\n\n  /**\n   * Generates a response by rendering the prompt template with given user input and then calling the model.\n   * @param input Prompt inputs.\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns the model response as a promise of `GenerateStreamResponse`.\n   */\n  stream(\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): GenerateStreamResponse<z.infer<O>>;\n\n  /**\n   * Renders the prompt template based on user input.\n   *\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns a `GenerateOptions` object to be used with the `generate()` function from @genkit-ai/ai.\n   */\n  render(\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateOptions<O, CustomOptions>>;\n\n  /**\n   * Returns the prompt usable as a tool.\n   */\n  asTool(): Promise<ToolAction>;\n}\n\nexport type PartsResolver<I, S = any> = (\n  input: I,\n  options: {\n    state?: S;\n    context: ActionContext;\n  }\n) => Part[] | Promise<string | Part | Part[]>;\n\nexport type MessagesResolver<I, S = any> = (\n  input: I,\n  options: {\n    history?: MessageData[];\n    state?: S;\n    context: ActionContext;\n  }\n) => MessageData[] | Promise<MessageData[]>;\n\nexport type DocsResolver<I, S = any> = (\n  input: I,\n  options: {\n    context: ActionContext;\n    state?: S;\n  }\n) => DocumentData[] | Promise<DocumentData[]>;\n\ninterface PromptCache {\n  userPrompt?: PromptFunction;\n  system?: PromptFunction;\n  messages?: PromptFunction;\n}\n\n/**\n * Defines a prompt which can be used to generate content or render a request.\n *\n * @returns The new `ExecutablePrompt`.\n */\nexport function definePrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: PromptConfig<I, O, CustomOptions>\n): ExecutablePrompt<z.infer<I>, O, CustomOptions> {\n  return definePromptAsync(\n    registry,\n    `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n    Promise.resolve(options)\n  );\n}\n\nfunction definePromptAsync<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  optionsPromise: PromiseLike<PromptConfig<I, O, CustomOptions>>\n): ExecutablePrompt<z.infer<I>, O, CustomOptions> {\n  const promptCache = {} as PromptCache;\n\n  const renderOptionsFn = async (\n    input: z.infer<I>,\n    renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n  ): Promise<GenerateOptions> => {\n    return await runInNewSpan(\n      registry,\n      {\n        metadata: {\n          name: 'render',\n          input,\n        },\n        labels: {\n          [SPAN_TYPE_ATTR]: 'promptTemplate',\n        },\n      },\n      async (metadata) => {\n        const messages: MessageData[] = [];\n        renderOptions = { ...renderOptions }; // make a copy, we will be trimming\n        const session = getCurrentSession(registry);\n        const resolvedOptions = await optionsPromise;\n\n        // order of these matters:\n        await renderSystemPrompt(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          promptCache,\n          renderOptions\n        );\n        await renderMessages(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          renderOptions,\n          promptCache\n        );\n        await renderUserPrompt(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          promptCache,\n          renderOptions\n        );\n\n        let docs: DocumentData[] | undefined;\n        if (typeof resolvedOptions.docs === 'function') {\n          docs = await resolvedOptions.docs(input, {\n            state: session?.state,\n            context: renderOptions?.context || getContext(registry) || {},\n          });\n        } else {\n          docs = resolvedOptions.docs;\n        }\n\n        const opts: GenerateOptions = stripUndefinedProps({\n          model: resolvedOptions.model,\n          maxTurns: resolvedOptions.maxTurns,\n          messages,\n          docs,\n          tools: resolvedOptions.tools,\n          returnToolRequests: resolvedOptions.returnToolRequests,\n          toolChoice: resolvedOptions.toolChoice,\n          context: resolvedOptions.context,\n          output: resolvedOptions.output,\n          use: resolvedOptions.use,\n          ...stripUndefinedProps(renderOptions),\n          config: {\n            ...resolvedOptions?.config,\n            ...renderOptions?.config,\n          },\n        });\n        // if config is empty and it was not explicitly passed in, we delete it, don't want {}\n        if (Object.keys(opts.config).length === 0 && !renderOptions?.config) {\n          delete opts.config;\n        }\n        metadata.output = opts;\n        return opts;\n      }\n    );\n  };\n  const rendererActionConfig = lazy(() =>\n    optionsPromise.then((options: PromptConfig<I, O, CustomOptions>) => {\n      const metadata = promptMetadata(options);\n      return {\n        name: `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n        inputJsonSchema: options.input?.jsonSchema,\n        inputSchema: options.input?.schema,\n        description: options.description,\n        actionType: 'prompt',\n        metadata,\n        fn: async (\n          input: z.infer<I>\n        ): Promise<GenerateRequest<z.ZodTypeAny>> => {\n          return toGenerateRequest(\n            registry,\n            await renderOptionsFn(input, undefined)\n          );\n        },\n      } as ActionAsyncParams<any, any, any>;\n    })\n  );\n  const rendererAction = defineActionAsync(\n    registry,\n    'prompt',\n    name,\n    rendererActionConfig,\n    (action) => {\n      (action as PromptAction<I>).__executablePrompt =\n        executablePrompt as never as ExecutablePrompt<z.infer<I>>;\n    }\n  ) as Promise<PromptAction<I>>;\n\n  const executablePromptActionConfig = lazy(() =>\n    optionsPromise.then((options: PromptConfig<I, O, CustomOptions>) => {\n      const metadata = promptMetadata(options);\n      return {\n        name: `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n        inputJsonSchema: options.input?.jsonSchema,\n        inputSchema: options.input?.schema,\n        outputSchema: GenerateActionOptionsSchema,\n        description: options.description,\n        actionType: 'executable-prompt',\n        metadata,\n        fn: async (input: z.infer<I>): Promise<GenerateActionOptions> => {\n          return await toGenerateActionOptions(\n            registry,\n            await renderOptionsFn(input, undefined)\n          );\n        },\n      } as ActionAsyncParams<any, any, any>;\n    })\n  );\n\n  defineActionAsync(\n    registry,\n    'executable-prompt',\n    name,\n    executablePromptActionConfig,\n    (action) => {\n      (action as ExecutablePromptAction<I>).__executablePrompt =\n        executablePrompt as never as ExecutablePrompt<z.infer<I>>;\n    }\n  ) as Promise<ExecutablePromptAction<I>>;\n\n  const executablePrompt = wrapInExecutablePrompt(\n    registry,\n    renderOptionsFn,\n    rendererAction\n  );\n\n  return executablePrompt;\n}\n\nfunction promptMetadata(options: PromptConfig<any, any, any>) {\n  const metadata = {\n    ...options.metadata,\n    prompt: {\n      ...options.metadata?.prompt,\n      config: options.config,\n      input: {\n        schema: options.input ? toJsonSchema(options.input) : undefined,\n      },\n      name: options.name.includes('.')\n        ? options.name.split('.')[0]\n        : options.name,\n      model: modelName(options.model),\n    },\n    type: 'prompt',\n  };\n\n  if (options.variant) {\n    metadata.prompt.variant = options.variant;\n  }\n\n  return metadata;\n}\n\nfunction wrapInExecutablePrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  renderOptionsFn: (\n    input: z.infer<I>,\n    renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n  ) => Promise<GenerateOptions>,\n  rendererAction: Promise<PromptAction<I>>\n) {\n  const executablePrompt = (async (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateResponse<z.infer<O>>> => {\n    return await runInNewSpan(\n      registry,\n      {\n        metadata: {\n          name: (await rendererAction).__action.name,\n          input,\n        },\n        labels: {\n          [SPAN_TYPE_ATTR]: 'dotprompt',\n        },\n      },\n      async (metadata) => {\n        const output = await generate(registry, {\n          ...(await renderOptionsFn(input, opts)),\n        });\n        metadata.output = output;\n        return output;\n      }\n    );\n  }) as ExecutablePrompt<z.infer<I>, O, CustomOptions>;\n\n  executablePrompt.render = async (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateOptions<O, CustomOptions>> => {\n    return {\n      ...(await renderOptionsFn(input, opts)),\n    } as GenerateOptions<O, CustomOptions>;\n  };\n\n  executablePrompt.stream = (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): GenerateStreamResponse<z.infer<O>> => {\n    return generateStream(registry, renderOptionsFn(input, opts));\n  };\n\n  executablePrompt.asTool = async (): Promise<ToolAction<I, O>> => {\n    return (await rendererAction) as unknown as ToolAction<I, O>;\n  };\n  return executablePrompt;\n}\n\nasync function renderSystemPrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  promptCache: PromptCache,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n) {\n  if (typeof options.system === 'function') {\n    messages.push({\n      role: 'system',\n      content: normalizeParts(\n        await options.system(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n        })\n      ),\n    });\n  } else if (typeof options.system === 'string') {\n    // memoize compiled prompt\n    if (!promptCache.system) {\n      promptCache.system = await registry.dotprompt.compile(options.system);\n    }\n    messages.push({\n      role: 'system',\n      content: await renderDotpromptToParts(\n        registry,\n        promptCache.system,\n        input,\n        session,\n        options,\n        renderOptions\n      ),\n    });\n  } else if (options.system) {\n    messages.push({\n      role: 'system',\n      content: normalizeParts(options.system),\n    });\n  }\n}\n\nasync function renderMessages<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  renderOptions: PromptGenerateOptions<O, CustomOptions>,\n  promptCache: PromptCache\n) {\n  if (options.messages) {\n    if (typeof options.messages === 'function') {\n      messages.push(\n        ...(await options.messages(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n          history: renderOptions?.messages,\n        }))\n      );\n    } else if (typeof options.messages === 'string') {\n      // memoize compiled prompt\n      if (!promptCache.messages) {\n        promptCache.messages = await registry.dotprompt.compile(\n          options.messages\n        );\n      }\n      const rendered = await promptCache.messages({\n        input,\n        context: {\n          ...(renderOptions?.context || getContext(registry)),\n          state: session?.state,\n        },\n        messages: renderOptions?.messages?.map((m) =>\n          Message.parseData(m)\n        ) as DpMessage[],\n      });\n      messages.push(...(rendered.messages as MessageData[]));\n    } else {\n      messages.push(...options.messages);\n    }\n  } else {\n    if (renderOptions.messages) {\n      messages.push(...renderOptions.messages);\n    }\n  }\n  if (renderOptions?.messages) {\n    // delete messages from opts so that we don't override messages downstream\n    delete renderOptions.messages;\n  }\n}\n\nasync function renderUserPrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  promptCache: PromptCache,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n) {\n  if (typeof options.prompt === 'function') {\n    messages.push({\n      role: 'user',\n      content: normalizeParts(\n        await options.prompt(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n        })\n      ),\n    });\n  } else if (typeof options.prompt === 'string') {\n    // memoize compiled prompt\n    if (!promptCache.userPrompt) {\n      promptCache.userPrompt = await registry.dotprompt.compile(options.prompt);\n    }\n    messages.push({\n      role: 'user',\n      content: await renderDotpromptToParts(\n        registry,\n        promptCache.userPrompt,\n        input,\n        session,\n        options,\n        renderOptions\n      ),\n    });\n  } else if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: normalizeParts(options.prompt),\n    });\n  }\n}\n\nfunction modelName(\n  modelArg: ModelArgument<any> | undefined\n): string | undefined {\n  if (modelArg === undefined) {\n    return undefined;\n  }\n  if (typeof modelArg === 'string') {\n    return modelArg;\n  }\n  if ((modelArg as ModelReference<any>).name) {\n    return (modelArg as ModelReference<any>).name;\n  }\n  return (modelArg as ModelAction).__action.name;\n}\n\nfunction normalizeParts(parts: string | Part | Part[]): Part[] {\n  if (Array.isArray(parts)) return parts;\n  if (typeof parts === 'string') {\n    return [\n      {\n        text: parts,\n      },\n    ];\n  }\n  return [parts as Part];\n}\n\nasync function renderDotpromptToParts<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  promptFn: PromptFunction,\n  input: any,\n  session: Session | undefined,\n  options: PromptConfig<I, O, CustomOptions>,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n): Promise<Part[]> {\n  const renderred = await promptFn({\n    input,\n    context: {\n      ...(renderOptions?.context || getContext(registry)),\n      state: session?.state,\n    },\n  });\n  if (renderred.messages.length !== 1) {\n    throw new Error('parts tempate must produce only one message');\n  }\n  return renderred.messages[0].content;\n}\n\n/**\n * Checks whether the provided object is an executable prompt.\n */\nexport function isExecutablePrompt(obj: any): obj is ExecutablePrompt {\n  return (\n    !!(obj as ExecutablePrompt)?.render &&\n    !!(obj as ExecutablePrompt)?.asTool &&\n    !!(obj as ExecutablePrompt)?.stream\n  );\n}\n\nexport function loadPromptFolder(\n  registry: Registry,\n  dir = './prompts',\n  ns: string\n): void {\n  const promptsPath = resolve(dir);\n  if (existsSync(promptsPath)) {\n    loadPromptFolderRecursively(registry, dir, ns, '');\n  }\n}\nexport function loadPromptFolderRecursively(\n  registry: Registry,\n  dir: string,\n  ns: string,\n  subDir: string\n): void {\n  const promptsPath = resolve(dir);\n  const dirEnts = readdirSync(join(promptsPath, subDir), {\n    withFileTypes: true,\n  });\n  for (const dirEnt of dirEnts) {\n    const parentPath = join(promptsPath, subDir);\n    const fileName = dirEnt.name;\n    if (dirEnt.isFile() && fileName.endsWith('.prompt')) {\n      if (fileName.startsWith('_')) {\n        const partialName = fileName.substring(1, fileName.length - 7);\n        definePartial(\n          registry,\n          partialName,\n          readFileSync(join(parentPath, fileName), {\n            encoding: 'utf8',\n          })\n        );\n        logger.debug(\n          `Registered Dotprompt partial \"${partialName}\" from \"${join(parentPath, fileName)}\"`\n        );\n      } else {\n        // If this prompt is in a subdirectory, we need to include that\n        // in the namespace to prevent naming conflicts.\n        loadPrompt(\n          registry,\n          promptsPath,\n          fileName,\n          subDir ? `${subDir}/` : '',\n          ns\n        );\n      }\n    } else if (dirEnt.isDirectory()) {\n      loadPromptFolderRecursively(registry, dir, ns, join(subDir, fileName));\n    }\n  }\n}\n\nexport function definePartial(\n  registry: Registry,\n  name: string,\n  source: string\n) {\n  registry.dotprompt.definePartial(name, source);\n}\n\nexport function defineHelper(\n  registry: Registry,\n  name: string,\n  fn: Handlebars.HelperDelegate\n) {\n  registry.dotprompt.defineHelper(name, fn);\n}\n\nfunction loadPrompt(\n  registry: Registry,\n  path: string,\n  filename: string,\n  prefix = '',\n  ns = 'dotprompt'\n): void {\n  let name = `${prefix ?? ''}${basename(filename, '.prompt')}`;\n  let variant: string | null = null;\n  if (name.includes('.')) {\n    const parts = name.split('.');\n    name = parts[0];\n    variant = parts[1];\n  }\n  const source = readFileSync(join(path, prefix ?? '', filename), 'utf8');\n  const parsedPrompt = registry.dotprompt.parse(source);\n  definePromptAsync(\n    registry,\n    registryDefinitionKey(name, variant ?? undefined, ns),\n    // We use a lazy promise here because we only want prompt loaded when it's first used.\n    // This is important because otherwise the loading may happen before the user has configured\n    // all the schemas, etc., which will result in dotprompt.renderMetadata errors.\n    lazy(async () => {\n      const promptMetadata =\n        await registry.dotprompt.renderMetadata(parsedPrompt);\n      if (variant) {\n        promptMetadata.variant = variant;\n      }\n\n      // dotprompt can set null description on the schema, which can confuse downstream schema consumers\n      if (promptMetadata.output?.schema?.description === null) {\n        delete promptMetadata.output.schema.description;\n      }\n      if (promptMetadata.input?.schema?.description === null) {\n        delete promptMetadata.input.schema.description;\n      }\n\n      return {\n        name: registryDefinitionKey(name, variant ?? undefined, ns),\n        model: promptMetadata.model,\n        config: promptMetadata.config,\n        tools: promptMetadata.tools,\n        description: promptMetadata.description,\n        output: {\n          jsonSchema: promptMetadata.output?.schema,\n          format: promptMetadata.output?.format,\n        },\n        input: {\n          jsonSchema: promptMetadata.input?.schema,\n        },\n        metadata: {\n          ...promptMetadata.metadata,\n          type: 'prompt',\n          prompt: {\n            ...promptMetadata,\n            template: parsedPrompt.template,\n          },\n        },\n        maxTurns: promptMetadata.raw?.['maxTurns'],\n        toolChoice: promptMetadata.raw?.['toolChoice'],\n        returnToolRequests: promptMetadata.raw?.['returnToolRequests'],\n        messages: parsedPrompt.template,\n      };\n    })\n  );\n}\n\nexport async function prompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  options?: { variant?: string; dir?: string }\n): Promise<ExecutablePrompt<I, O, CustomOptions>> {\n  return await lookupPrompt<I, O, CustomOptions>(\n    registry,\n    name,\n    options?.variant\n  );\n}\n\nfunction registryLookupKey(name: string, variant?: string, ns?: string) {\n  return `/prompt/${registryDefinitionKey(name, variant, ns)}`;\n}\n\nasync function lookupPrompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  variant?: string\n): Promise<ExecutablePrompt<I, O, CustomOptions>> {\n  const registryPrompt = await registry.lookupAction(\n    registryLookupKey(name, variant)\n  );\n  if (registryPrompt) {\n    return (registryPrompt as PromptAction)\n      .__executablePrompt as never as ExecutablePrompt<I, O, CustomOptions>;\n  }\n  throw new GenkitError({\n    status: 'NOT_FOUND',\n    message: `Prompt ${name + (variant ? ` (variant ${variant})` : '')} not found`,\n  });\n}\n\nfunction registryDefinitionKey(name: string, variant?: string, ns?: string) {\n  // \"ns/prompt.variant\" where ns and variant are optional\n  return `${ns ? `${ns}/` : ''}${name}${variant ? `.${variant}` : ''}`;\n}\n"], "mappings": "AAgBA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAMK;AACP,SAAS,YAAY;AACrB,SAAS,cAAc;AAEvB,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB,oBAAoB;AAE7C,SAAS,YAAY,cAAc,mBAAmB;AACtD,SAAS,UAAU,MAAM,eAAe;AAExC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAMK;AACP,SAAS,eAAe;AACxB;AAAA,EACE;AAAA,OAYK;AACP,SAAS,yBAAuC;AAmBzC,SAAS,eAAe,QAAwC;AACrE,SAAO,OAAO,SAAS,UAAU,SAAS;AAC5C;AA8IO,SAAS,aAKd,UACA,SACgD;AAChD,SAAO;AAAA,IACL;AAAA,IACA,GAAG,QAAQ,IAAI,GAAG,QAAQ,UAAU,IAAI,QAAQ,OAAO,KAAK,EAAE;AAAA,IAC9D,QAAQ,QAAQ,OAAO;AAAA,EACzB;AACF;AAEA,SAAS,kBAKP,UACA,MACA,gBACgD;AAChD,QAAM,cAAc,CAAC;AAErB,QAAM,kBAAkB,OACtB,OACA,kBAC6B;AAC7B,WAAO,MAAM;AAAA,MACX;AAAA,MACA;AAAA,QACE,UAAU;AAAA,UACR,MAAM;AAAA,UACN;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,cAAc,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,MACA,OAAO,aAAa;AAClB,cAAM,WAA0B,CAAC;AACjC,wBAAgB,EAAE,GAAG,cAAc;AACnC,cAAM,UAAU,kBAAkB,QAAQ;AAC1C,cAAM,kBAAkB,MAAM;AAG9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,YAAI;AACJ,YAAI,OAAO,gBAAgB,SAAS,YAAY;AAC9C,iBAAO,MAAM,gBAAgB,KAAK,OAAO;AAAA,YACvC,OAAO,SAAS;AAAA,YAChB,SAAS,eAAe,WAAW,WAAW,QAAQ,KAAK,CAAC;AAAA,UAC9D,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,gBAAgB;AAAA,QACzB;AAEA,cAAM,OAAwB,oBAAoB;AAAA,UAChD,OAAO,gBAAgB;AAAA,UACvB,UAAU,gBAAgB;AAAA,UAC1B;AAAA,UACA;AAAA,UACA,OAAO,gBAAgB;AAAA,UACvB,oBAAoB,gBAAgB;AAAA,UACpC,YAAY,gBAAgB;AAAA,UAC5B,SAAS,gBAAgB;AAAA,UACzB,QAAQ,gBAAgB;AAAA,UACxB,KAAK,gBAAgB;AAAA,UACrB,GAAG,oBAAoB,aAAa;AAAA,UACpC,QAAQ;AAAA,YACN,GAAG,iBAAiB;AAAA,YACpB,GAAG,eAAe;AAAA,UACpB;AAAA,QACF,CAAC;AAED,YAAI,OAAO,KAAK,KAAK,MAAM,EAAE,WAAW,KAAK,CAAC,eAAe,QAAQ;AACnE,iBAAO,KAAK;AAAA,QACd;AACA,iBAAS,SAAS;AAClB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,uBAAuB;AAAA,IAAK,MAChC,eAAe,KAAK,CAAC,YAA+C;AAClE,YAAM,WAAW,eAAe,OAAO;AACvC,aAAO;AAAA,QACL,MAAM,GAAG,QAAQ,IAAI,GAAG,QAAQ,UAAU,IAAI,QAAQ,OAAO,KAAK,EAAE;AAAA,QACpE,iBAAiB,QAAQ,OAAO;AAAA,QAChC,aAAa,QAAQ,OAAO;AAAA,QAC5B,aAAa,QAAQ;AAAA,QACrB,YAAY;AAAA,QACZ;AAAA,QACA,IAAI,OACF,UAC2C;AAC3C,iBAAO;AAAA,YACL;AAAA,YACA,MAAM,gBAAgB,OAAO,MAAS;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,WAAW;AACV,MAAC,OAA2B,qBAC1B;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,+BAA+B;AAAA,IAAK,MACxC,eAAe,KAAK,CAAC,YAA+C;AAClE,YAAM,WAAW,eAAe,OAAO;AACvC,aAAO;AAAA,QACL,MAAM,GAAG,QAAQ,IAAI,GAAG,QAAQ,UAAU,IAAI,QAAQ,OAAO,KAAK,EAAE;AAAA,QACpE,iBAAiB,QAAQ,OAAO;AAAA,QAChC,aAAa,QAAQ,OAAO;AAAA,QAC5B,cAAc;AAAA,QACd,aAAa,QAAQ;AAAA,QACrB,YAAY;AAAA,QACZ;AAAA,QACA,IAAI,OAAO,UAAsD;AAC/D,iBAAO,MAAM;AAAA,YACX;AAAA,YACA,MAAM,gBAAgB,OAAO,MAAS;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,WAAW;AACV,MAAC,OAAqC,qBACpC;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,SAAsC;AAC5D,QAAM,WAAW;AAAA,IACf,GAAG,QAAQ;AAAA,IACX,QAAQ;AAAA,MACN,GAAG,QAAQ,UAAU;AAAA,MACrB,QAAQ,QAAQ;AAAA,MAChB,OAAO;AAAA,QACL,QAAQ,QAAQ,QAAQ,aAAa,QAAQ,KAAK,IAAI;AAAA,MACxD;AAAA,MACA,MAAM,QAAQ,KAAK,SAAS,GAAG,IAC3B,QAAQ,KAAK,MAAM,GAAG,EAAE,CAAC,IACzB,QAAQ;AAAA,MACZ,OAAO,UAAU,QAAQ,KAAK;AAAA,IAChC;AAAA,IACA,MAAM;AAAA,EACR;AAEA,MAAI,QAAQ,SAAS;AACnB,aAAS,OAAO,UAAU,QAAQ;AAAA,EACpC;AAEA,SAAO;AACT;AAEA,SAAS,uBAKP,UACA,iBAIA,gBACA;AACA,QAAM,mBAAoB,OACxB,OACA,SAC0C;AAC1C,WAAO,MAAM;AAAA,MACX;AAAA,MACA;AAAA,QACE,UAAU;AAAA,UACR,OAAO,MAAM,gBAAgB,SAAS;AAAA,UACtC;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,cAAc,GAAG;AAAA,QACpB;AAAA,MACF;AAAA,MACA,OAAO,aAAa;AAClB,cAAM,SAAS,MAAM,SAAS,UAAU;AAAA,UACtC,GAAI,MAAM,gBAAgB,OAAO,IAAI;AAAA,QACvC,CAAC;AACD,iBAAS,SAAS;AAClB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,mBAAiB,SAAS,OACxB,OACA,SAC+C;AAC/C,WAAO;AAAA,MACL,GAAI,MAAM,gBAAgB,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAEA,mBAAiB,SAAS,CACxB,OACA,SACuC;AACvC,WAAO,eAAe,UAAU,gBAAgB,OAAO,IAAI,CAAC;AAAA,EAC9D;AAEA,mBAAiB,SAAS,YAAuC;AAC/D,WAAQ,MAAM;AAAA,EAChB;AACA,SAAO;AACT;AAEA,eAAe,mBAKb,UACA,SACA,OACA,UACA,SACA,aACA,eACA;AACA,MAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,QACP,MAAM,QAAQ,OAAO,OAAO;AAAA,UAC1B,OAAO,SAAS;AAAA,UAChB,SAAS,eAAe,WAAW,WAAW,QAAQ,KAAK,CAAC;AAAA,QAC9D,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,WAAW,OAAO,QAAQ,WAAW,UAAU;AAE7C,QAAI,CAAC,YAAY,QAAQ;AACvB,kBAAY,SAAS,MAAM,SAAS,UAAU,QAAQ,QAAQ,MAAM;AAAA,IACtE;AACA,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,QACb;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,WAAW,QAAQ,QAAQ;AACzB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,eAAe,QAAQ,MAAM;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAEA,eAAe,eAKb,UACA,SACA,OACA,UACA,SACA,eACA,aACA;AACA,MAAI,QAAQ,UAAU;AACpB,QAAI,OAAO,QAAQ,aAAa,YAAY;AAC1C,eAAS;AAAA,QACP,GAAI,MAAM,QAAQ,SAAS,OAAO;AAAA,UAChC,OAAO,SAAS;AAAA,UAChB,SAAS,eAAe,WAAW,WAAW,QAAQ,KAAK,CAAC;AAAA,UAC5D,SAAS,eAAe;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,IACF,WAAW,OAAO,QAAQ,aAAa,UAAU;AAE/C,UAAI,CAAC,YAAY,UAAU;AACzB,oBAAY,WAAW,MAAM,SAAS,UAAU;AAAA,UAC9C,QAAQ;AAAA,QACV;AAAA,MACF;AACA,YAAM,WAAW,MAAM,YAAY,SAAS;AAAA,QAC1C;AAAA,QACA,SAAS;AAAA,UACP,GAAI,eAAe,WAAW,WAAW,QAAQ;AAAA,UACjD,OAAO,SAAS;AAAA,QAClB;AAAA,QACA,UAAU,eAAe,UAAU;AAAA,UAAI,CAAC,MACtC,QAAQ,UAAU,CAAC;AAAA,QACrB;AAAA,MACF,CAAC;AACD,eAAS,KAAK,GAAI,SAAS,QAA0B;AAAA,IACvD,OAAO;AACL,eAAS,KAAK,GAAG,QAAQ,QAAQ;AAAA,IACnC;AAAA,EACF,OAAO;AACL,QAAI,cAAc,UAAU;AAC1B,eAAS,KAAK,GAAG,cAAc,QAAQ;AAAA,IACzC;AAAA,EACF;AACA,MAAI,eAAe,UAAU;AAE3B,WAAO,cAAc;AAAA,EACvB;AACF;AAEA,eAAe,iBAKb,UACA,SACA,OACA,UACA,SACA,aACA,eACA;AACA,MAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,QACP,MAAM,QAAQ,OAAO,OAAO;AAAA,UAC1B,OAAO,SAAS;AAAA,UAChB,SAAS,eAAe,WAAW,WAAW,QAAQ,KAAK,CAAC;AAAA,QAC9D,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,WAAW,OAAO,QAAQ,WAAW,UAAU;AAE7C,QAAI,CAAC,YAAY,YAAY;AAC3B,kBAAY,aAAa,MAAM,SAAS,UAAU,QAAQ,QAAQ,MAAM;AAAA,IAC1E;AACA,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,QACb;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,WAAW,QAAQ,QAAQ;AACzB,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,eAAe,QAAQ,MAAM;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAEA,SAAS,UACP,UACoB;AACpB,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO;AAAA,EACT;AACA,MAAK,SAAiC,MAAM;AAC1C,WAAQ,SAAiC;AAAA,EAC3C;AACA,SAAQ,SAAyB,SAAS;AAC5C;AAEA,SAAS,eAAe,OAAuC;AAC7D,MAAI,MAAM,QAAQ,KAAK,EAAG,QAAO;AACjC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL;AAAA,QACE,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,KAAa;AACvB;AAEA,eAAe,uBAKb,UACA,UACA,OACA,SACA,SACA,eACiB;AACjB,QAAM,YAAY,MAAM,SAAS;AAAA,IAC/B;AAAA,IACA,SAAS;AAAA,MACP,GAAI,eAAe,WAAW,WAAW,QAAQ;AAAA,MACjD,OAAO,SAAS;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI,UAAU,SAAS,WAAW,GAAG;AACnC,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AACA,SAAO,UAAU,SAAS,CAAC,EAAE;AAC/B;AAKO,SAAS,mBAAmB,KAAmC;AACpE,SACE,CAAC,CAAE,KAA0B,UAC7B,CAAC,CAAE,KAA0B,UAC7B,CAAC,CAAE,KAA0B;AAEjC;AAEO,SAAS,iBACd,UACA,MAAM,aACN,IACM;AACN,QAAM,cAAc,QAAQ,GAAG;AAC/B,MAAI,WAAW,WAAW,GAAG;AAC3B,gCAA4B,UAAU,KAAK,IAAI,EAAE;AAAA,EACnD;AACF;AACO,SAAS,4BACd,UACA,KACA,IACA,QACM;AACN,QAAM,cAAc,QAAQ,GAAG;AAC/B,QAAM,UAAU,YAAY,KAAK,aAAa,MAAM,GAAG;AAAA,IACrD,eAAe;AAAA,EACjB,CAAC;AACD,aAAW,UAAU,SAAS;AAC5B,UAAM,aAAa,KAAK,aAAa,MAAM;AAC3C,UAAM,WAAW,OAAO;AACxB,QAAI,OAAO,OAAO,KAAK,SAAS,SAAS,SAAS,GAAG;AACnD,UAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,cAAM,cAAc,SAAS,UAAU,GAAG,SAAS,SAAS,CAAC;AAC7D;AAAA,UACE;AAAA,UACA;AAAA,UACA,aAAa,KAAK,YAAY,QAAQ,GAAG;AAAA,YACvC,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,eAAO;AAAA,UACL,iCAAiC,WAAW,WAAW,KAAK,YAAY,QAAQ,CAAC;AAAA,QACnF;AAAA,MACF,OAAO;AAGL;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,MAAM,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,OAAO,YAAY,GAAG;AAC/B,kCAA4B,UAAU,KAAK,IAAI,KAAK,QAAQ,QAAQ,CAAC;AAAA,IACvE;AAAA,EACF;AACF;AAEO,SAAS,cACd,UACA,MACA,QACA;AACA,WAAS,UAAU,cAAc,MAAM,MAAM;AAC/C;AAEO,SAAS,aACd,UACA,MACA,IACA;AACA,WAAS,UAAU,aAAa,MAAM,EAAE;AAC1C;AAEA,SAAS,WACP,UACA,MACA,UACA,SAAS,IACT,KAAK,aACC;AACN,MAAI,OAAO,GAAG,UAAU,EAAE,GAAG,SAAS,UAAU,SAAS,CAAC;AAC1D,MAAI,UAAyB;AAC7B,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,UAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,WAAO,MAAM,CAAC;AACd,cAAU,MAAM,CAAC;AAAA,EACnB;AACA,QAAM,SAAS,aAAa,KAAK,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM;AACtE,QAAM,eAAe,SAAS,UAAU,MAAM,MAAM;AACpD;AAAA,IACE;AAAA,IACA,sBAAsB,MAAM,WAAW,QAAW,EAAE;AAAA;AAAA;AAAA;AAAA,IAIpD,KAAK,YAAY;AACf,YAAMA,kBACJ,MAAM,SAAS,UAAU,eAAe,YAAY;AACtD,UAAI,SAAS;AACX,QAAAA,gBAAe,UAAU;AAAA,MAC3B;AAGA,UAAIA,gBAAe,QAAQ,QAAQ,gBAAgB,MAAM;AACvD,eAAOA,gBAAe,OAAO,OAAO;AAAA,MACtC;AACA,UAAIA,gBAAe,OAAO,QAAQ,gBAAgB,MAAM;AACtD,eAAOA,gBAAe,MAAM,OAAO;AAAA,MACrC;AAEA,aAAO;AAAA,QACL,MAAM,sBAAsB,MAAM,WAAW,QAAW,EAAE;AAAA,QAC1D,OAAOA,gBAAe;AAAA,QACtB,QAAQA,gBAAe;AAAA,QACvB,OAAOA,gBAAe;AAAA,QACtB,aAAaA,gBAAe;AAAA,QAC5B,QAAQ;AAAA,UACN,YAAYA,gBAAe,QAAQ;AAAA,UACnC,QAAQA,gBAAe,QAAQ;AAAA,QACjC;AAAA,QACA,OAAO;AAAA,UACL,YAAYA,gBAAe,OAAO;AAAA,QACpC;AAAA,QACA,UAAU;AAAA,UACR,GAAGA,gBAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,GAAGA;AAAA,YACH,UAAU,aAAa;AAAA,UACzB;AAAA,QACF;AAAA,QACA,UAAUA,gBAAe,MAAM,UAAU;AAAA,QACzC,YAAYA,gBAAe,MAAM,YAAY;AAAA,QAC7C,oBAAoBA,gBAAe,MAAM,oBAAoB;AAAA,QAC7D,UAAU,aAAa;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,eAAsB,OAKpB,UACA,MACA,SACgD;AAChD,SAAO,MAAM;AAAA,IACX;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX;AACF;AAEA,SAAS,kBAAkB,MAAc,SAAkB,IAAa;AACtE,SAAO,WAAW,sBAAsB,MAAM,SAAS,EAAE,CAAC;AAC5D;AAEA,eAAe,aAKb,UACA,MACA,SACgD;AAChD,QAAM,iBAAiB,MAAM,SAAS;AAAA,IACpC,kBAAkB,MAAM,OAAO;AAAA,EACjC;AACA,MAAI,gBAAgB;AAClB,WAAQ,eACL;AAAA,EACL;AACA,QAAM,IAAI,YAAY;AAAA,IACpB,QAAQ;AAAA,IACR,SAAS,UAAU,QAAQ,UAAU,aAAa,OAAO,MAAM,GAAG;AAAA,EACpE,CAAC;AACH;AAEA,SAAS,sBAAsB,MAAc,SAAkB,IAAa;AAE1E,SAAO,GAAG,KAAK,GAAG,EAAE,MAAM,EAAE,GAAG,IAAI,GAAG,UAAU,IAAI,OAAO,KAAK,EAAE;AACpE;", "names": ["promptMetadata"]}