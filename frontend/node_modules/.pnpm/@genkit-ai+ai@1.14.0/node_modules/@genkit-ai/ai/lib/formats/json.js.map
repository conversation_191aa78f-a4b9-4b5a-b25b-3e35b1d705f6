{"version": 3, "sources": ["../../src/formats/json.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { extractJson } from '../extract';\nimport type { Formatter } from './types';\n\nexport const jsonFormatter: Formatter<unknown, unknown> = {\n  name: 'json',\n  config: {\n    format: 'json',\n    contentType: 'application/json',\n    constrained: true,\n    defaultInstructions: false,\n  },\n  handler: (schema) => {\n    let instructions: string | undefined;\n\n    if (schema) {\n      instructions = `Output should be in JSON format and conform to the following schema:\n\n\\`\\`\\`\n${JSON.stringify(schema)}\n\\`\\`\\`\n`;\n    }\n    return {\n      parseChunk: (chunk) => {\n        return extractJson(chunk.accumulatedText);\n      },\n\n      parseMessage: (message) => {\n        return extractJson(message.text);\n      },\n\n      instructions,\n    };\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,qBAA4B;AAGrB,MAAM,gBAA6C;AAAA,EACxD,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AAAA,EACA,SAAS,CAAC,WAAW;AACnB,QAAI;AAEJ,QAAI,QAAQ;AACV,qBAAe;AAAA;AAAA;AAAA,EAGnB,KAAK,UAAU,MAAM,CAAC;AAAA;AAAA;AAAA,IAGpB;AACA,WAAO;AAAA,MACL,YAAY,CAAC,UAAU;AACrB,mBAAO,4BAAY,MAAM,eAAe;AAAA,MAC1C;AAAA,MAEA,cAAc,CAAC,YAAY;AACzB,mBAAO,4BAAY,QAAQ,IAAI;AAAA,MACjC;AAAA,MAEA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}