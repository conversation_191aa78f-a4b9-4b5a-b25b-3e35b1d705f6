"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var resource_exports = {};
__export(resource_exports, {
  ResourceInputSchema: () => ResourceInputSchema,
  ResourceOutputSchema: () => ResourceOutputSchema,
  defineResource: () => defineResource,
  findMatchingResource: () => findMatchingResource
});
module.exports = __toCommonJS(resource_exports);
var import_core = require("@genkit-ai/core");
var import_uri_templates = __toESM(require("uri-templates"));
var import_model_types = require("./model-types.js");
const ResourceInputSchema = import_core.z.object({
  uri: import_core.z.string()
});
const ResourceOutputSchema = import_core.z.object({
  content: import_core.z.array(import_model_types.PartSchema)
});
function defineResource(registry, opts, fn) {
  const uri = opts.uri ?? opts.template;
  if (!uri) {
    throw new import_core.GenkitError({
      status: "INVALID_ARGUMENT",
      message: `must specify either url or template options`
    });
  }
  const template = opts.template ? (0, import_uri_templates.default)(opts.template) : void 0;
  const matcher = opts.uri ? (
    // TODO: normalize resource URI during comparisons
    // foo://bar?baz=1&qux=2 and foo://bar?qux=2&baz=1 are equivalent URIs but would not match.
    (input) => input === opts.uri ? {} : void 0
  ) : (input) => {
    return template.fromUri(input);
  };
  const action = (0, import_core.defineAction)(
    registry,
    {
      actionType: "resource",
      name: opts.name ?? uri,
      description: opts.description,
      inputSchema: ResourceInputSchema,
      outputSchema: ResourceOutputSchema,
      metadata: {
        resource: {
          uri: opts.uri,
          template: opts.template
        },
        ...opts.metadata
      }
    },
    async (input, ctx) => {
      const templateMatch = matcher(input.uri);
      if (!templateMatch) {
        throw new import_core.GenkitError({
          status: "INVALID_ARGUMENT",
          message: `input ${input} did not match template ${uri}`
        });
      }
      const parts = await fn(input, ctx);
      parts.content.map((p) => {
        if (!p.metadata) {
          p.metadata = {};
        }
        if (p.metadata?.resource) {
          if (!p.metadata.resource.parent) {
            p.metadata.resource.parent = {
              uri: input.uri
            };
            if (opts.template) {
              p.metadata.resource.parent.template = opts.template;
            }
          }
        } else {
          p.metadata.resource = {
            uri: input.uri
          };
          if (opts.template) {
            p.metadata.resource.template = opts.template;
          }
        }
        return p;
      });
      return parts;
    }
  );
  action.matches = (input) => matcher(input.uri) !== void 0;
  return action;
}
async function findMatchingResource(registry, input) {
  for (const actKeys of Object.keys(await registry.listResolvableActions())) {
    if (actKeys.startsWith("/resource/")) {
      const resource = await registry.lookupAction(actKeys);
      if (resource.matches(input)) {
        return resource;
      }
    }
  }
  return void 0;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ResourceInputSchema,
  ResourceOutputSchema,
  defineResource,
  findMatchingResource
});
//# sourceMappingURL=resource.js.map