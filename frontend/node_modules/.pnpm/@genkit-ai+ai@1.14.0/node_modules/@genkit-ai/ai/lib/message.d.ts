import { m as ToolResponsePart, l as ToolRequestPart } from './document-DPYGNwPg.js';
import { MessageData, Part } from './model-types.js';
import '@genkit-ai/core';
import '@genkit-ai/core/registry';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

type MessageParser<T = unknown> = (message: Message) => T;
/**
 * Message represents a single role's contribution to a generation. Each message
 * can contain multiple parts (for example text and an image), and each generation
 * can contain multiple messages.
 */
declare class Message<T = unknown> implements MessageData {
    role: MessageData['role'];
    content: Part[];
    metadata?: Record<string, any>;
    parser?: MessageParser<T>;
    static parseData(lenientMessage: string | (MessageData & {
        content: string | Part | Part[];
        role: string;
    }) | MessageData, defaultRole?: MessageData['role']): MessageData;
    static parse(lenientMessage: string | (MessageData & {
        content: string;
    }) | MessageData): Message;
    static parseContent(lenientPart: string | Part | (string | Part)[]): Part[];
    constructor(message: MessageData, options?: {
        parser?: MessageParser<T>;
    });
    /**
     * Attempts to parse the content of the message according to the supplied
     * output parser. Without a parser, returns `data` contained in the message or
     * tries to parse JSON from the text of the message.
     *
     * @returns The structured output contained in the message.
     */
    get output(): T;
    toolResponseParts(): ToolResponsePart[];
    /**
     * Concatenates all `text` parts present in the message with no delimiter.
     * @returns A string of all concatenated text parts.
     */
    get text(): string;
    /**
     * Concatenates all `reasoning` parts present in the message with no delimiter.
     * @returns A string of all concatenated reasoning parts.
     */
    get reasoning(): string;
    /**
     * Returns the first media part detected in the message. Useful for extracting
     * (for example) an image from a generation expected to create one.
     * @returns The first detected `media` part in the message.
     */
    get media(): {
        url: string;
        contentType?: string;
    } | null;
    /**
     * Returns the first detected `data` part of a message.
     * @returns The first `data` part detected in the message (if any).
     */
    get data(): T | null;
    /**
     * Returns all tool request found in this message.
     * @returns Array of all tool request found in this message.
     */
    get toolRequests(): ToolRequestPart[];
    /**
     * Returns all tool requests annotated with interrupt metadata.
     * @returns Array of all interrupt tool requests.
     */
    get interrupts(): ToolRequestPart[];
    /**
     * Converts the Message to a plain JS object.
     * @returns Plain JS object representing the data contained in the message.
     */
    toJSON(): MessageData;
}

export { Message, type MessageParser };
