{"version": 3, "sources": ["../src/check-operation.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError, Operation } from '@genkit-ai/core';\nimport { Registry } from '@genkit-ai/core/registry';\n\nexport async function checkOperation<T = unknown>(\n  registry: Registry,\n  operation: Operation<T>\n): Promise<Operation<T>> {\n  if (!operation.action) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Provided operation is missing original request information',\n    });\n  }\n  const backgroundAction = await registry.lookupBackgroundAction(\n    operation.action\n  );\n  if (!backgroundAction) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `Failed to resolve background action from original request: ${operation.action}`,\n    });\n  }\n  return await backgroundAction.check(operation);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,kBAAuC;AAGvC,eAAsB,eACpB,UACA,WACuB;AACvB,MAAI,CAAC,UAAU,QAAQ;AACrB,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,MAAM,SAAS;AAAA,IACtC,UAAU;AAAA,EACZ;AACA,MAAI,CAAC,kBAAkB;AACrB,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS,8DAA8D,UAAU,MAAM;AAAA,IACzF,CAAC;AAAA,EACH;AACA,SAAO,MAAM,iBAAiB,MAAM,SAAS;AAC/C;", "names": []}