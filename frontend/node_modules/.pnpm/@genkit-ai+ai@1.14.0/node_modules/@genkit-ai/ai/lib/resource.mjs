import {
  defineAction,
  GenkitError,
  z
} from "@genkit-ai/core";
import uriTemplate from "uri-templates";
import { PartSchema } from "./model-types.js";
const ResourceInputSchema = z.object({
  uri: z.string()
});
const ResourceOutputSchema = z.object({
  content: z.array(PartSchema)
});
function defineResource(registry, opts, fn) {
  const uri = opts.uri ?? opts.template;
  if (!uri) {
    throw new GenkitError({
      status: "INVALID_ARGUMENT",
      message: `must specify either url or template options`
    });
  }
  const template = opts.template ? uriTemplate(opts.template) : void 0;
  const matcher = opts.uri ? (
    // TODO: normalize resource URI during comparisons
    // foo://bar?baz=1&qux=2 and foo://bar?qux=2&baz=1 are equivalent URIs but would not match.
    (input) => input === opts.uri ? {} : void 0
  ) : (input) => {
    return template.fromUri(input);
  };
  const action = defineAction(
    registry,
    {
      actionType: "resource",
      name: opts.name ?? uri,
      description: opts.description,
      inputSchema: ResourceInputSchema,
      outputSchema: ResourceOutputSchema,
      metadata: {
        resource: {
          uri: opts.uri,
          template: opts.template
        },
        ...opts.metadata
      }
    },
    async (input, ctx) => {
      const templateMatch = matcher(input.uri);
      if (!templateMatch) {
        throw new GenkitError({
          status: "INVALID_ARGUMENT",
          message: `input ${input} did not match template ${uri}`
        });
      }
      const parts = await fn(input, ctx);
      parts.content.map((p) => {
        if (!p.metadata) {
          p.metadata = {};
        }
        if (p.metadata?.resource) {
          if (!p.metadata.resource.parent) {
            p.metadata.resource.parent = {
              uri: input.uri
            };
            if (opts.template) {
              p.metadata.resource.parent.template = opts.template;
            }
          }
        } else {
          p.metadata.resource = {
            uri: input.uri
          };
          if (opts.template) {
            p.metadata.resource.template = opts.template;
          }
        }
        return p;
      });
      return parts;
    }
  );
  action.matches = (input) => matcher(input.uri) !== void 0;
  return action;
}
async function findMatchingResource(registry, input) {
  for (const actKeys of Object.keys(await registry.listResolvableActions())) {
    if (actKeys.startsWith("/resource/")) {
      const resource = await registry.lookupAction(actKeys);
      if (resource.matches(input)) {
        return resource;
      }
    }
  }
  return void 0;
}
export {
  ResourceInputSchema,
  ResourceOutputSchema,
  defineResource,
  findMatchingResource
};
//# sourceMappingURL=resource.mjs.map