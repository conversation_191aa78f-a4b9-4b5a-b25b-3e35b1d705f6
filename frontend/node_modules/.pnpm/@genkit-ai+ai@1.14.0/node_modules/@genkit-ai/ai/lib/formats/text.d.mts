import { Formatter } from './types.mjs';
import '@genkit-ai/core';
import '../generate/chunk.mjs';
import '../document-DPYGNwPg.mjs';
import '@genkit-ai/core/registry';
import '../model-types.mjs';
import '../message.mjs';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

declare const textFormatter: Formatter<string, string>;

export { textFormatter };
