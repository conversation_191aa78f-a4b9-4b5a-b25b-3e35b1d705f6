{"version": 3, "sources": ["../src/resource.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Action,\n  ActionContext,\n  defineAction,\n  GenkitError,\n  z,\n} from '@genkit-ai/core';\nimport { Registry } from '@genkit-ai/core/registry';\nimport uriTemplate from 'uri-templates';\nimport { PartSchema } from './model-types.js';\n\n/**\n * Options for defining a resource.\n */\nexport interface ResourceOptions {\n  /**\n   * Resource name. If not specified, uri or template will be used as name.\n   */\n  name?: string;\n\n  /**\n   * The URI of the resource. Can contain template variables.\n   */\n  uri?: string;\n\n  /**\n   * The URI template (ex. `my://resource/{id}`). See RFC6570 for specification.\n   */\n  template?: string;\n\n  /**\n   * A description of the resource.\n   */\n  description?: string;\n\n  /**\n   * Resource metadata.\n   */\n  metadata?: Record<string, any>;\n}\n\nexport const ResourceInputSchema = z.object({\n  uri: z.string(),\n});\n\nexport type ResourceInput = z.infer<typeof ResourceInputSchema>;\n\nexport const ResourceOutputSchema = z.object({\n  content: z.array(PartSchema),\n});\n\nexport type ResourceOutput = z.infer<typeof ResourceOutputSchema>;\n\n/**\n * A function that returns parts for a given resource.\n */\nexport type ResourceFn = (\n  input: ResourceInput,\n  ctx: ActionContext\n) => ResourceOutput | Promise<ResourceOutput>;\n\n/**\n * A resource action.\n */\nexport interface ResourceAction\n  extends Action<typeof ResourceInputSchema, typeof ResourceOutputSchema> {\n  matches(input: ResourceInput): boolean;\n}\n\n/**\n * Defines a resource.\n *\n * @param registry The registry to register the resource with.\n * @param opts The resource options.\n * @param fn The resource function.\n * @returns The resource action.\n */\nexport function defineResource(\n  registry: Registry,\n  opts: ResourceOptions,\n  fn: ResourceFn\n): ResourceAction {\n  const uri = opts.uri ?? opts.template;\n  if (!uri) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `must specify either url or template options`,\n    });\n  }\n  const template = opts.template ? uriTemplate(opts.template) : undefined;\n  const matcher = opts.uri\n    ? // TODO: normalize resource URI during comparisons\n      // foo://bar?baz=1&qux=2 and foo://bar?qux=2&baz=1 are equivalent URIs but would not match.\n      (input: string) => (input === opts.uri ? {} : undefined)\n    : (input: string) => {\n        return template!.fromUri(input);\n      };\n\n  const action = defineAction(\n    registry,\n    {\n      actionType: 'resource',\n      name: opts.name ?? uri,\n      description: opts.description,\n      inputSchema: ResourceInputSchema,\n      outputSchema: ResourceOutputSchema,\n      metadata: {\n        resource: {\n          uri: opts.uri,\n          template: opts.template,\n        },\n        ...opts.metadata,\n      },\n    },\n    async (input, ctx) => {\n      const templateMatch = matcher(input.uri);\n      if (!templateMatch) {\n        throw new GenkitError({\n          status: 'INVALID_ARGUMENT',\n          message: `input ${input} did not match template ${uri}`,\n        });\n      }\n      const parts = await fn(input, ctx);\n      parts.content.map((p) => {\n        if (!p.metadata) {\n          p.metadata = {};\n        }\n        if (p.metadata?.resource) {\n          if (!(p.metadata as any).resource.parent) {\n            (p.metadata as any).resource.parent = {\n              uri: input.uri,\n            };\n            if (opts.template) {\n              (p.metadata as any).resource.parent.template = opts.template;\n            }\n          }\n        } else {\n          (p.metadata as any).resource = {\n            uri: input.uri,\n          };\n          if (opts.template) {\n            (p.metadata as any).resource.template = opts.template;\n          }\n        }\n        return p;\n      });\n      return parts;\n    }\n  ) as ResourceAction;\n\n  action.matches = (input: ResourceInput) => matcher(input.uri) !== undefined;\n\n  return action;\n}\n\n/**\n * Finds a matching resource in the registry. If not found returns undefined.\n */\nexport async function findMatchingResource(\n  registry: Registry,\n  input: ResourceInput\n): Promise<ResourceAction | undefined> {\n  for (const actKeys of Object.keys(await registry.listResolvableActions())) {\n    if (actKeys.startsWith('/resource/')) {\n      const resource = (await registry.lookupAction(actKeys)) as ResourceAction;\n      if (resource.matches(input)) {\n        return resource;\n      }\n    }\n  }\n  return undefined;\n}\n"], "mappings": "AAgBA;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,OAAO,iBAAiB;AACxB,SAAS,kBAAkB;AAgCpB,MAAM,sBAAsB,EAAE,OAAO;AAAA,EAC1C,KAAK,EAAE,OAAO;AAChB,CAAC;AAIM,MAAM,uBAAuB,EAAE,OAAO;AAAA,EAC3C,SAAS,EAAE,MAAM,UAAU;AAC7B,CAAC;AA4BM,SAAS,eACd,UACA,MACA,IACgB;AAChB,QAAM,MAAM,KAAK,OAAO,KAAK;AAC7B,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,YAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,QAAM,WAAW,KAAK,WAAW,YAAY,KAAK,QAAQ,IAAI;AAC9D,QAAM,UAAU,KAAK;AAAA;AAAA;AAAA,IAGjB,CAAC,UAAmB,UAAU,KAAK,MAAM,CAAC,IAAI;AAAA,MAC9C,CAAC,UAAkB;AACjB,WAAO,SAAU,QAAQ,KAAK;AAAA,EAChC;AAEJ,QAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,MAAM,KAAK,QAAQ;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR,UAAU;AAAA,UACR,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,QACjB;AAAA,QACA,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,IACA,OAAO,OAAO,QAAQ;AACpB,YAAM,gBAAgB,QAAQ,MAAM,GAAG;AACvC,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,YAAY;AAAA,UACpB,QAAQ;AAAA,UACR,SAAS,SAAS,KAAK,2BAA2B,GAAG;AAAA,QACvD,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,MAAM,GAAG,OAAO,GAAG;AACjC,YAAM,QAAQ,IAAI,CAAC,MAAM;AACvB,YAAI,CAAC,EAAE,UAAU;AACf,YAAE,WAAW,CAAC;AAAA,QAChB;AACA,YAAI,EAAE,UAAU,UAAU;AACxB,cAAI,CAAE,EAAE,SAAiB,SAAS,QAAQ;AACxC,YAAC,EAAE,SAAiB,SAAS,SAAS;AAAA,cACpC,KAAK,MAAM;AAAA,YACb;AACA,gBAAI,KAAK,UAAU;AACjB,cAAC,EAAE,SAAiB,SAAS,OAAO,WAAW,KAAK;AAAA,YACtD;AAAA,UACF;AAAA,QACF,OAAO;AACL,UAAC,EAAE,SAAiB,WAAW;AAAA,YAC7B,KAAK,MAAM;AAAA,UACb;AACA,cAAI,KAAK,UAAU;AACjB,YAAC,EAAE,SAAiB,SAAS,WAAW,KAAK;AAAA,UAC/C;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,UAAU,CAAC,UAAyB,QAAQ,MAAM,GAAG,MAAM;AAElE,SAAO;AACT;AAKA,eAAsB,qBACpB,UACA,OACqC;AACrC,aAAW,WAAW,OAAO,KAAK,MAAM,SAAS,sBAAsB,CAAC,GAAG;AACzE,QAAI,QAAQ,WAAW,YAAY,GAAG;AACpC,YAAM,WAAY,MAAM,SAAS,aAAa,OAAO;AACrD,UAAI,SAAS,QAAQ,KAAK,GAAG;AAC3B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;", "names": []}