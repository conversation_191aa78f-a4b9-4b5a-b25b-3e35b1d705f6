{"version": 3, "sources": ["../../src/formats/enum.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError } from '@genkit-ai/core';\nimport type { Formatter } from './types';\n\nexport const enumFormatter: Formatter<string, string> = {\n  name: 'enum',\n  config: {\n    contentType: 'text/enum',\n    constrained: true,\n  },\n  handler: (schema) => {\n    if (schema && schema.type !== 'string' && schema.type !== 'enum') {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `Must supply a 'string' or 'enum' schema type when using the enum parser format.`,\n      });\n    }\n\n    let instructions: string | undefined;\n    if (schema?.enum) {\n      instructions = `Output should be ONLY one of the following enum values. Do not output any additional information or add quotes.\\n\\n${schema.enum.map((v) => v.toString()).join('\\n')}`;\n    }\n\n    return {\n      parseMessage: (message) => {\n        return message.text.replace(/['\"]/g, '').trim();\n      },\n      instructions,\n    };\n  },\n};\n"], "mappings": "AAgBA,SAAS,mBAAmB;AAGrB,MAAM,gBAA2C;AAAA,EACtD,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,aAAa;AAAA,IACb,aAAa;AAAA,EACf;AAAA,EACA,SAAS,CAAC,WAAW;AACnB,QAAI,UAAU,OAAO,SAAS,YAAY,OAAO,SAAS,QAAQ;AAChE,YAAM,IAAI,YAAY;AAAA,QACpB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI;AACJ,QAAI,QAAQ,MAAM;AAChB,qBAAe;AAAA;AAAA,EAAsH,OAAO,KAAK,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,IACtL;AAEA,WAAO;AAAA,MACL,cAAc,CAAC,YAAY;AACzB,eAAO,QAAQ,KAAK,QAAQ,SAAS,EAAE,EAAE,KAAK;AAAA,MAChD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}