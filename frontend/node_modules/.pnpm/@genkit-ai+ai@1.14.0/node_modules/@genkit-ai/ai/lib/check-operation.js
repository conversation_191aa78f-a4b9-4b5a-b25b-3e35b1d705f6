"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var check_operation_exports = {};
__export(check_operation_exports, {
  checkOperation: () => checkOperation
});
module.exports = __toCommonJS(check_operation_exports);
var import_core = require("@genkit-ai/core");
async function checkOperation(registry, operation) {
  if (!operation.action) {
    throw new import_core.GenkitError({
      status: "INVALID_ARGUMENT",
      message: "Provided operation is missing original request information"
    });
  }
  const backgroundAction = await registry.lookupBackgroundAction(
    operation.action
  );
  if (!backgroundAction) {
    throw new import_core.GenkitError({
      status: "INVALID_ARGUMENT",
      message: `Failed to resolve background action from original request: ${operation.action}`
    });
  }
  return await backgroundAction.check(operation);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  checkOperation
});
//# sourceMappingURL=check-operation.js.map