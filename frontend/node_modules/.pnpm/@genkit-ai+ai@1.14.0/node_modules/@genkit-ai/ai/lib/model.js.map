{"version": 3, "sources": ["../src/model.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ActionFnArg,\n  BackgroundAction,\n  GenkitError,\n  Operation,\n  OperationSchema,\n  defineAction,\n  defineBackgroundAction,\n  getStreamingCallback,\n  z,\n  type Action,\n  type ActionMetadata,\n  type SimpleMiddleware,\n  type StreamingCallback,\n} from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { performance } from 'node:perf_hooks';\nimport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n} from './document.js';\nimport {\n  CandidateData,\n  GenerateRequest,\n  GenerateRequestSchema,\n  GenerateResponseChunkData,\n  GenerateResponseChunkSchema,\n  GenerateResponseData,\n  GenerateResponseSchema,\n  GenerationUsage,\n  MessageData,\n  ModelInfo,\n  Part,\n} from './model-types.js';\nimport {\n  augmentWithContext,\n  simulateConstrainedGeneration,\n  validateSupport,\n} from './model/middleware.js';\nexport { defineGenerateAction } from './generate/action.js';\nexport * from './model-types.js';\nexport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  simulateConstrainedGeneration,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n};\n\nexport type ModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema,\n  typeof GenerateResponseChunkSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type BackgroundModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = BackgroundAction<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type ModelMiddleware = SimpleMiddleware<\n  z.infer<typeof GenerateRequestSchema>,\n  z.infer<typeof GenerateResponseSchema>\n>;\n\nexport type DefineModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name: string;\n  /** Known version names for this model, e.g. `gemini-1.0-pro-001`. */\n  versions?: string[];\n  /** Capabilities this model supports. */\n  supports?: ModelInfo['supports'];\n  /** Custom options schema for this model. */\n  configSchema?: CustomOptionsSchema;\n  /** Descriptive name for this model e.g. 'Google AI - Gemini Pro'. */\n  label?: string;\n  /** Middleware to be used with this model. */\n  use?: ModelMiddleware[];\n};\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    apiVersion: 'v2';\n  } & DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: ActionFnArg<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    streamingCallback?: StreamingCallback<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: any,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: any\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineAction(\n    registry,\n    {\n      actionType: 'model',\n      name: options.name,\n      description: label,\n      inputSchema: GenerateRequestSchema,\n      outputSchema: GenerateResponseSchema,\n      metadata: {\n        model: {\n          label,\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n          versions: options.versions,\n          supports: options.supports,\n        },\n      },\n      use: middleware,\n    },\n    (input, ctx) => {\n      const startTimeMs = performance.now();\n      const secondParam =\n        options.apiVersion === 'v2'\n          ? ctx\n          : getStreamingCallback(registry) ||\n            (ctx.streamingRequested && ctx.sendChunk) ||\n            undefined;\n      return runner(input, secondParam).then((response) => {\n        const timedResponse = {\n          ...response,\n          latencyMs: performance.now() - startTimeMs,\n        };\n        return timedResponse;\n      });\n    }\n  );\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act as ModelAction<CustomOptionsSchema>;\n}\n\nexport type DefineBackgroundModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = DefineModelOptions<CustomOptionsSchema> & {\n  start: (\n    request: GenerateRequest<CustomOptionsSchema>\n  ) => Promise<Operation<GenerateResponseData>>;\n  check: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n  cancel?: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n};\n\n/**\n * Defines a new model that runs in the background.\n */\nexport function defineBackgroundModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineBackgroundModelOptions<CustomOptionsSchema>\n): BackgroundModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineBackgroundAction(registry, {\n    actionType: 'background-model',\n    name: options.name,\n    description: label,\n    inputSchema: GenerateRequestSchema,\n    outputSchema: GenerateResponseSchema,\n    metadata: {\n      model: {\n        label,\n        customOptions: options.configSchema\n          ? toJsonSchema({ schema: options.configSchema })\n          : undefined,\n        versions: options.versions,\n        supports: options.supports,\n      },\n    },\n    use: middleware,\n    async start(request) {\n      const startTimeMs = performance.now();\n      const response = await options.start(request);\n      Object.assign(response, {\n        latencyMs: performance.now() - startTimeMs,\n      });\n      return response;\n    },\n    async check(op) {\n      return options.check(op);\n    },\n    cancel: options.cancel\n      ? async (op) => {\n          if (!options.cancel) {\n            throw new GenkitError({\n              status: 'UNIMPLEMENTED',\n              message: 'cancel not implemented',\n            });\n          }\n          return options.cancel(op);\n        }\n      : undefined,\n  }) as BackgroundModelAction<CustomOptionsSchema>;\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act;\n}\n\nfunction getModelMiddleware(options: {\n  use?: ModelMiddleware[];\n  name: string;\n  supports?: ModelInfo['supports'];\n}) {\n  const middleware: ModelMiddleware[] = [\n    ...(options.use || []),\n    validateSupport(options),\n  ];\n  if (!options?.supports?.context) middleware.push(augmentWithContext());\n  const constratedSimulator = simulateConstrainedGeneration();\n  middleware.push((req, next) => {\n    if (\n      !options?.supports?.constrained ||\n      options?.supports?.constrained === 'none' ||\n      (options?.supports?.constrained === 'no-tools' &&\n        (req.tools?.length ?? 0) > 0)\n    ) {\n      return constratedSimulator(req, next);\n    }\n    return next(req);\n  });\n\n  return middleware;\n}\n\nexport interface ModelReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: ModelInfo;\n  version?: string;\n  config?: z.infer<CustomOptions>;\n\n  withConfig(cfg: z.infer<CustomOptions>): ModelReference<CustomOptions>;\n  withVersion(version: string): ModelReference<CustomOptions>;\n}\n\n/**\n * Packages model information into ActionMetadata object.\n */\nexport function modelActionMetadata({\n  name,\n  info,\n  configSchema,\n  background,\n}: {\n  name: string;\n  info?: ModelInfo;\n  configSchema?: z.ZodTypeAny;\n  background?: boolean;\n}): ActionMetadata {\n  return {\n    actionType: background ? 'background-model' : 'model',\n    name: name,\n    inputJsonSchema: toJsonSchema({ schema: GenerateRequestSchema }),\n    outputJsonSchema: background\n      ? toJsonSchema({ schema: OperationSchema })\n      : toJsonSchema({ schema: GenerateResponseSchema }),\n    metadata: {\n      model: {\n        ...info,\n        customOptions: configSchema\n          ? toJsonSchema({ schema: configSchema })\n          : undefined,\n      },\n    },\n  } as ActionMetadata;\n}\n\n/** Cretes a model reference. */\nexport function modelRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: Omit<\n    ModelReference<CustomOptionsSchema>,\n    'withConfig' | 'withVersion'\n  >\n): ModelReference<CustomOptionsSchema> {\n  const ref: Partial<ModelReference<CustomOptionsSchema>> = { ...options };\n  ref.withConfig = (\n    cfg: z.infer<CustomOptionsSchema>\n  ): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      config: cfg,\n    });\n  };\n  ref.withVersion = (version: string): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      version,\n    });\n  };\n  return ref as ModelReference<CustomOptionsSchema>;\n}\n\n/** Container for counting usage stats for a single input/output {Part} */\ntype PartCounts = {\n  characters: number;\n  images: number;\n  videos: number;\n  audio: number;\n};\n\n/**\n * Calculates basic usage statistics from the given model inputs and outputs.\n */\nexport function getBasicUsageStats(\n  input: MessageData[],\n  response: MessageData | CandidateData[]\n): GenerationUsage {\n  const inputCounts = getPartCounts(input.flatMap((md) => md.content));\n  const outputCounts = getPartCounts(\n    Array.isArray(response)\n      ? response.flatMap((c) => c.message.content)\n      : response.content\n  );\n  return {\n    inputCharacters: inputCounts.characters,\n    inputImages: inputCounts.images,\n    inputVideos: inputCounts.videos,\n    inputAudioFiles: inputCounts.audio,\n    outputCharacters: outputCounts.characters,\n    outputImages: outputCounts.images,\n    outputVideos: outputCounts.videos,\n    outputAudioFiles: outputCounts.audio,\n  };\n}\n\nfunction getPartCounts(parts: Part[]): PartCounts {\n  return parts.reduce(\n    (counts, part) => {\n      const isImage =\n        part.media?.contentType?.startsWith('image') ||\n        part.media?.url?.startsWith('data:image');\n      const isVideo =\n        part.media?.contentType?.startsWith('video') ||\n        part.media?.url?.startsWith('data:video');\n      const isAudio =\n        part.media?.contentType?.startsWith('audio') ||\n        part.media?.url?.startsWith('data:audio');\n      return {\n        characters: counts.characters + (part.text?.length || 0),\n        images: counts.images + (isImage ? 1 : 0),\n        videos: counts.videos + (isVideo ? 1 : 0),\n        audio: counts.audio + (isAudio ? 1 : 0),\n      };\n    },\n    { characters: 0, images: 0, videos: 0, audio: 0 }\n  );\n}\n\nexport type ModelArgument<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  | ModelAction<CustomOptions>\n  | ModelReference<CustomOptions>\n  | string;\n\nexport interface ResolvedModel<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  modelAction: ModelAction;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\nexport async function resolveModel<C extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  model: ModelArgument<C> | undefined,\n  options?: { warnDeprecated?: boolean }\n): Promise<ResolvedModel<C>> {\n  let out: ResolvedModel<C>;\n  let modelId: string;\n\n  if (!model) {\n    model = await registry.lookupValue('defaultModel', 'defaultModel');\n  }\n  if (!model) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Must supply a `model` to `generate()` calls.',\n    });\n  }\n  if (typeof model === 'string') {\n    modelId = model;\n    out = { modelAction: await lookupModel(registry, model) };\n  } else if (model.hasOwnProperty('__action')) {\n    modelId = (model as ModelAction).__action.name;\n    out = { modelAction: model as ModelAction };\n  } else {\n    const ref = model as ModelReference<any>;\n    modelId = ref.name;\n    out = {\n      modelAction: await lookupModel(registry, ref.name),\n      config: {\n        ...ref.config,\n      },\n      version: ref.version,\n    };\n  }\n\n  if (!out.modelAction) {\n    throw new GenkitError({\n      status: 'NOT_FOUND',\n      message: `Model '${modelId}' not found`,\n    });\n  }\n\n  if (\n    options?.warnDeprecated &&\n    out.modelAction.__action.metadata?.model?.stage === 'deprecated'\n  ) {\n    logger.warn(\n      `Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`\n    );\n  }\n\n  return out;\n}\n\nasync function lookupModel(\n  registry: Registry,\n  model: string\n): Promise<ModelAction> {\n  return (\n    (await registry.lookupAction(`/model/${model}`)) ||\n    (await registry.lookupAction(`/background-model/${model}`))\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,kBAcO;AACP,qBAAuB;AAEvB,oBAA6B;AAC7B,6BAA4B;AAC5B,sBAaO;AACP,yBAYO;AACP,wBAIO;AACP,oBAAqC;AACrC,0BAAc,6BApEd;AA2JO,SAAS,YAGd,UACA,SACA,QAIkC;AAClC,QAAM,QAAQ,QAAQ,SAAS,QAAQ;AACvC,QAAM,aAAa,mBAAmB,OAAO;AAC7C,QAAM,UAAM;AAAA,IACV;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,MAAM,QAAQ;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR,OAAO;AAAA,UACL;AAAA,UACA,eAAe,QAAQ,mBACnB,4BAAa,EAAE,QAAQ,QAAQ,aAAa,CAAC,IAC7C;AAAA,UACJ,UAAU,QAAQ;AAAA,UAClB,UAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,MACA,KAAK;AAAA,IACP;AAAA,IACA,CAAC,OAAO,QAAQ;AACd,YAAM,cAAc,mCAAY,IAAI;AACpC,YAAM,cACJ,QAAQ,eAAe,OACnB,UACA,kCAAqB,QAAQ,KAC5B,IAAI,sBAAsB,IAAI,aAC/B;AACN,aAAO,OAAO,OAAO,WAAW,EAAE,KAAK,CAAC,aAAa;AACnD,cAAM,gBAAgB;AAAA,UACpB,GAAG;AAAA,UACH,WAAW,mCAAY,IAAI,IAAI;AAAA,QACjC;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,OAAO,KAAK;AAAA,IACjB,gBAAgB,QAAQ,gBAAgB,cAAE,QAAQ;AAAA,EACpD,CAAC;AACD,SAAO;AACT;AAmBO,SAAS,sBAGd,UACA,SAC4C;AAC5C,QAAM,QAAQ,QAAQ,SAAS,QAAQ;AACvC,QAAM,aAAa,mBAAmB,OAAO;AAC7C,QAAM,UAAM,oCAAuB,UAAU;AAAA,IAC3C,YAAY;AAAA,IACZ,MAAM,QAAQ;AAAA,IACd,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,UAAU;AAAA,MACR,OAAO;AAAA,QACL;AAAA,QACA,eAAe,QAAQ,mBACnB,4BAAa,EAAE,QAAQ,QAAQ,aAAa,CAAC,IAC7C;AAAA,QACJ,UAAU,QAAQ;AAAA,QAClB,UAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,MAAM,MAAM,SAAS;AACnB,YAAM,cAAc,mCAAY,IAAI;AACpC,YAAM,WAAW,MAAM,QAAQ,MAAM,OAAO;AAC5C,aAAO,OAAO,UAAU;AAAA,QACtB,WAAW,mCAAY,IAAI,IAAI;AAAA,MACjC,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,MAAM,MAAM,IAAI;AACd,aAAO,QAAQ,MAAM,EAAE;AAAA,IACzB;AAAA,IACA,QAAQ,QAAQ,SACZ,OAAO,OAAO;AACZ,UAAI,CAAC,QAAQ,QAAQ;AACnB,cAAM,IAAI,wBAAY;AAAA,UACpB,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,OAAO,EAAE;AAAA,IAC1B,IACA;AAAA,EACN,CAAC;AACD,SAAO,OAAO,KAAK;AAAA,IACjB,gBAAgB,QAAQ,gBAAgB,cAAE,QAAQ;AAAA,EACpD,CAAC;AACD,SAAO;AACT;AAEA,SAAS,mBAAmB,SAIzB;AACD,QAAM,aAAgC;AAAA,IACpC,GAAI,QAAQ,OAAO,CAAC;AAAA,QACpB,mCAAgB,OAAO;AAAA,EACzB;AACA,MAAI,CAAC,SAAS,UAAU,QAAS,YAAW,SAAK,sCAAmB,CAAC;AACrE,QAAM,0BAAsB,iDAA8B;AAC1D,aAAW,KAAK,CAAC,KAAK,SAAS;AAC7B,QACE,CAAC,SAAS,UAAU,eACpB,SAAS,UAAU,gBAAgB,UAClC,SAAS,UAAU,gBAAgB,eACjC,IAAI,OAAO,UAAU,KAAK,GAC7B;AACA,aAAO,oBAAoB,KAAK,IAAI;AAAA,IACtC;AACA,WAAO,KAAK,GAAG;AAAA,EACjB,CAAC;AAED,SAAO;AACT;AAgBO,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKmB;AACjB,SAAO;AAAA,IACL,YAAY,aAAa,qBAAqB;AAAA,IAC9C;AAAA,IACA,qBAAiB,4BAAa,EAAE,QAAQ,yCAAsB,CAAC;AAAA,IAC/D,kBAAkB,iBACd,4BAAa,EAAE,QAAQ,4BAAgB,CAAC,QACxC,4BAAa,EAAE,QAAQ,0CAAuB,CAAC;AAAA,IACnD,UAAU;AAAA,MACR,OAAO;AAAA,QACL,GAAG;AAAA,QACH,eAAe,mBACX,4BAAa,EAAE,QAAQ,aAAa,CAAC,IACrC;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAGO,SAAS,SAGd,SAIqC;AACrC,QAAM,MAAoD,EAAE,GAAG,QAAQ;AACvE,MAAI,aAAa,CACf,QACwC;AACxC,WAAO,SAAS;AAAA,MACd,GAAG;AAAA,MACH,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,MAAI,cAAc,CAAC,YAAyD;AAC1E,WAAO,SAAS;AAAA,MACd,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAaO,SAAS,mBACd,OACA,UACiB;AACjB,QAAM,cAAc,cAAc,MAAM,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;AACnE,QAAM,eAAe;AAAA,IACnB,MAAM,QAAQ,QAAQ,IAClB,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,OAAO,IACzC,SAAS;AAAA,EACf;AACA,SAAO;AAAA,IACL,iBAAiB,YAAY;AAAA,IAC7B,aAAa,YAAY;AAAA,IACzB,aAAa,YAAY;AAAA,IACzB,iBAAiB,YAAY;AAAA,IAC7B,kBAAkB,aAAa;AAAA,IAC/B,cAAc,aAAa;AAAA,IAC3B,cAAc,aAAa;AAAA,IAC3B,kBAAkB,aAAa;AAAA,EACjC;AACF;AAEA,SAAS,cAAc,OAA2B;AAChD,SAAO,MAAM;AAAA,IACX,CAAC,QAAQ,SAAS;AAChB,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,YAAM,UACJ,KAAK,OAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,OAAO,KAAK,WAAW,YAAY;AAC1C,aAAO;AAAA,QACL,YAAY,OAAO,cAAc,KAAK,MAAM,UAAU;AAAA,QACtD,QAAQ,OAAO,UAAU,UAAU,IAAI;AAAA,QACvC,QAAQ,OAAO,UAAU,UAAU,IAAI;AAAA,QACvC,OAAO,OAAO,SAAS,UAAU,IAAI;AAAA,MACvC;AAAA,IACF;AAAA,IACA,EAAE,YAAY,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,EAAE;AAAA,EAClD;AACF;AAeA,eAAsB,aACpB,UACA,OACA,SAC2B;AAC3B,MAAI;AACJ,MAAI;AAEJ,MAAI,CAAC,OAAO;AACV,YAAQ,MAAM,SAAS,YAAY,gBAAgB,cAAc;AAAA,EACnE;AACA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,cAAU;AACV,UAAM,EAAE,aAAa,MAAM,YAAY,UAAU,KAAK,EAAE;AAAA,EAC1D,WAAW,MAAM,eAAe,UAAU,GAAG;AAC3C,cAAW,MAAsB,SAAS;AAC1C,UAAM,EAAE,aAAa,MAAqB;AAAA,EAC5C,OAAO;AACL,UAAM,MAAM;AACZ,cAAU,IAAI;AACd,UAAM;AAAA,MACJ,aAAa,MAAM,YAAY,UAAU,IAAI,IAAI;AAAA,MACjD,QAAQ;AAAA,QACN,GAAG,IAAI;AAAA,MACT;AAAA,MACA,SAAS,IAAI;AAAA,IACf;AAAA,EACF;AAEA,MAAI,CAAC,IAAI,aAAa;AACpB,UAAM,IAAI,wBAAY;AAAA,MACpB,QAAQ;AAAA,MACR,SAAS,UAAU,OAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AAEA,MACE,SAAS,kBACT,IAAI,YAAY,SAAS,UAAU,OAAO,UAAU,cACpD;AACA,0BAAO;AAAA,MACL,UAAU,IAAI,YAAY,SAAS,IAAI;AAAA,IACzC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,eAAe,YACb,UACA,OACsB;AACtB,SACG,MAAM,SAAS,aAAa,UAAU,KAAK,EAAE,KAC7C,MAAM,SAAS,aAAa,qBAAqB,KAAK,EAAE;AAE7D;", "names": []}