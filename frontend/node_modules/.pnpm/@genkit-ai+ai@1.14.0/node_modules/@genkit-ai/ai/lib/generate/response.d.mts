import { Operation } from '@genkit-ai/core';
import { Message, MessageParser } from '../message.mjs';
import { l as ToolRequestPart } from '../document-DPYGNwPg.mjs';
import { ModelResponseData, GenerationUsage, GenerateRequest, GenerateResponseData, MessageData } from '../model-types.mjs';
import '@genkit-ai/core/registry';

/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * GenerateResponse is the result from a `generate()` call and contains one or
 * more generated candidate messages.
 */
declare class GenerateResponse<O = unknown> implements ModelResponseData {
    /** The generated message. */
    message?: Message<O>;
    /** The reason generation stopped for this request. */
    finishReason: ModelResponseData['finishReason'];
    /** Additional information about why the model stopped generating, if any. */
    finishMessage?: string;
    /** Usage information. */
    usage: GenerationUsage;
    /** Provider-specific response data. */
    custom: unknown;
    /** Provider-specific response data. */
    raw: unknown;
    /** The request that generated this response. */
    request?: GenerateRequest;
    /** Model generation long running operation. */
    operation?: Operation<GenerateResponseData>;
    /** Name of the model used. */
    model?: string;
    /** The parser for output parsing of this response. */
    parser?: MessageParser<O>;
    constructor(response: GenerateResponseData, options?: {
        request?: GenerateRequest;
        parser?: MessageParser<O>;
    });
    /**
     * Throws an error if the response does not contain valid output.
     */
    assertValid(): void;
    /**
     * Throws an error if the response does not conform to expected schema.
     */
    assertValidSchema(request?: GenerateRequest): void;
    isValid(request?: GenerateRequest): boolean;
    /**
     * If the generated message contains a `data` part, it is returned. Otherwise,
     * the `output()` method extracts the first valid JSON object or array from the text
     * contained in the selected candidate's message and returns it.
     *
     * @returns The structured output contained in the selected candidate.
     */
    get output(): O | null;
    /**
     * Concatenates all `text` parts present in the generated message with no delimiter.
     * @returns A string of all concatenated text parts.
     */
    get text(): string;
    /**
     * Concatenates all `reasoning` parts present in the generated message with no delimiter.
     * @returns A string of all concatenated reasoning parts.
     */
    get reasoning(): string;
    /**
     * Returns the first detected media part in the generated message. Useful for
     * extracting (for example) an image from a generation expected to create one.
     * @returns The first detected `media` part in the candidate.
     */
    get media(): {
        url: string;
        contentType?: string;
    } | null;
    /**
     * Returns the first detected `data` part of the generated message.
     * @returns The first `data` part detected in the candidate (if any).
     */
    get data(): O | null;
    /**
     * Returns all tool request found in the generated message.
     * @returns Array of all tool request found in the candidate.
     */
    get toolRequests(): ToolRequestPart[];
    /**
     * Returns all tool requests annotated as interrupts found in the generated message.
     * @returns A list of ToolRequestParts.
     */
    get interrupts(): ToolRequestPart[];
    /**
     * Returns the message history for the request by concatenating the model
     * response to the list of messages from the request. The result of this
     * method can be safely serialized to JSON for persistence in a database.
     * @returns A serializable list of messages compatible with `generate({history})`.
     */
    get messages(): MessageData[];
    toJSON(): ModelResponseData;
}

export { GenerateResponse };
