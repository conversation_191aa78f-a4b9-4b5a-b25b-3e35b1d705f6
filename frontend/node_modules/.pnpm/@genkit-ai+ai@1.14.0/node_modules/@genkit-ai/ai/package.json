{"name": "@genkit-ai/ai", "description": "Genkit AI framework generative AI APIs.", "keywords": ["genkit", "ai", "genai", "generative-ai"], "version": "1.14.0", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/firebase/genkit.git", "directory": "js/ai"}, "author": "genkit", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api": "^1.9.0", "@types/node": "^20.11.19", "colorette": "^2.0.20", "dotprompt": "^1.1.1", "json5": "^2.2.3", "node-fetch": "^3.3.2", "partial-json": "^0.1.7", "uri-templates": "^0.2.0", "uuid": "^10.0.0", "@genkit-ai/core": "1.14.0"}, "devDependencies": {"@types/uuid": "^9.0.6", "@types/uri-templates": "^0.1.34", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^4.9.0", "yaml": "^2.7.0"}, "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.js", "import": "./lib/index.mjs", "default": "./lib/index.js"}, "./retriever": {"types": "./lib/retriever.d.ts", "require": "./lib/retriever.js", "import": "./lib/retriever.mjs", "default": "./lib/retriever.js"}, "./embedder": {"types": "./lib/embedder.d.ts", "require": "./lib/embedder.js", "import": "./lib/embedder.mjs", "default": "./lib/embedder.js"}, "./evaluator": {"types": "./lib/evaluator.d.ts", "require": "./lib/evaluator.js", "import": "./lib/evaluator.mjs", "default": "./lib/evaluator.js"}, "./model": {"types": "./lib/model.d.ts", "require": "./lib/model.js", "import": "./lib/model.mjs", "default": "./lib/model.js"}, "./model/middleware": {"types": "./lib/model/middleware.d.ts", "require": "./lib/model/middleware.js", "import": "./lib/model/middleware.mjs", "default": "./lib/model/middleware.js"}, "./extract": {"types": "./lib/extract.d.ts", "require": "./lib/extract.js", "import": "./lib/extract.mjs", "default": "./lib/extract.js"}, "./tool": {"types": "./lib/tool.d.ts", "require": "./lib/tool.js", "import": "./lib/tool.mjs", "default": "./lib/tool.js"}, "./testing": {"types": "./lib/testing/index.d.ts", "require": "./lib/testing/index.js", "import": "./lib/testing/index.mjs", "default": "./lib/testing/index.js"}, "./reranker": {"types": "./lib/reranker.d.ts", "require": "./lib/reranker.js", "import": "./lib/reranker.mjs", "default": "./lib/reranker.js"}, "./chat": {"types": "./lib/chat.d.ts", "require": "./lib/chat.js", "import": "./lib/chat.mjs", "default": "./lib/chat.js"}, "./session": {"types": "./lib/session.d.ts", "require": "./lib/session.js", "import": "./lib/session.mjs", "default": "./lib/session.js"}, "./formats": {"types": "./lib/formats/index.d.ts", "require": "./lib/formats/index.js", "import": "./lib/formats/index.mjs", "default": "./lib/formats/index.js"}}, "typesVersions": {"*": {"retriever": ["lib/retriever"], "embedder": ["lib/embedder"], "evaluator": ["lib/evaluator"], "model": ["lib/model"], "model/middleware": ["lib/model/middleware"], "extract": ["lib/extract"], "tool": ["lib/tool"], "reranker": ["lib/reranker"], "chat": ["lib/chat"], "session": ["lib/session"], "formats": ["lib/formats"]}}, "scripts": {"check": "tsc", "compile": "tsup-node", "build:clean": "rimraf ./lib", "build": "npm-run-all build:clean check compile", "build:watch": "tsup-node --watch", "test": "node --import tsx --test ./tests/**/*_test.ts ./tests/*_test.ts", "test:watch": "node --watch --import tsx --test ./tests/**/*_test.ts ./tests/*_test.ts", "test:single": "node --import tsx --test"}}