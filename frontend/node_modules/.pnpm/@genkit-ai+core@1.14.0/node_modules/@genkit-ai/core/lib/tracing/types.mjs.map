{"version": 3, "sources": ["../../src/tracing/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'zod';\n\n// NOTE: Keep this file in sync with genkit-tools/common/src/types/trace.ts!\n// Eventually tools will be source of truth for these types (by generating a\n// JSON schema) but until then this file must be manually kept in sync\n\nexport const PathMetadataSchema = z.object({\n  path: z.string(),\n  status: z.string(),\n  error: z.string().optional(),\n  latency: z.number(),\n});\nexport type PathMetadata = z.infer<typeof PathMetadataSchema>;\n\nexport const TraceMetadataSchema = z.object({\n  featureName: z.string().optional(),\n  paths: z.set(PathMetadataSchema).optional(),\n  timestamp: z.number(),\n});\nexport type TraceMetadata = z.infer<typeof TraceMetadataSchema>;\n\nexport const SpanMetadataSchema = z.object({\n  name: z.string(),\n  state: z.enum(['success', 'error']).optional(),\n  input: z.any().optional(),\n  output: z.any().optional(),\n  isRoot: z.boolean().optional(),\n  metadata: z.record(z.string(), z.string()).optional(),\n  path: z.string().optional(),\n  // Indicates a \"leaf\" span that is the source of a failure.\n  isFailureSource: z.boolean().optional(),\n});\nexport type SpanMetadata = z.infer<typeof SpanMetadataSchema>;\n\nexport const SpanStatusSchema = z.object({\n  code: z.number(),\n  message: z.string().optional(),\n});\n\nexport const TimeEventSchema = z.object({\n  time: z.number(),\n  annotation: z.object({\n    attributes: z.record(z.string(), z.any()),\n    description: z.string(),\n  }),\n});\n\nexport const SpanContextSchema = z.object({\n  traceId: z.string(),\n  spanId: z.string(),\n  isRemote: z.boolean().optional(),\n  traceFlags: z.number(),\n});\n\nexport const LinkSchema = z.object({\n  context: SpanContextSchema.optional(),\n  attributes: z.record(z.string(), z.any()).optional(),\n  droppedAttributesCount: z.number().optional(),\n});\n\nexport const InstrumentationLibrarySchema = z.object({\n  name: z.string().readonly(),\n  version: z.string().optional().readonly(),\n  schemaUrl: z.string().optional().readonly(),\n});\n\nexport const SpanDataSchema = z.object({\n  spanId: z.string(),\n  traceId: z.string(),\n  parentSpanId: z.string().optional(),\n  startTime: z.number(),\n  endTime: z.number(),\n  attributes: z.record(z.string(), z.any()),\n  displayName: z.string(),\n  links: z.array(LinkSchema).optional(),\n  instrumentationLibrary: InstrumentationLibrarySchema,\n  spanKind: z.string(),\n  sameProcessAsParentSpan: z.object({ value: z.boolean() }).optional(),\n  status: SpanStatusSchema.optional(),\n  timeEvents: z\n    .object({\n      timeEvent: z.array(TimeEventSchema),\n    })\n    .optional(),\n  truncated: z.boolean().optional(),\n});\nexport type SpanData = z.infer<typeof SpanDataSchema>;\n\nexport const TraceDataSchema = z.object({\n  traceId: z.string(),\n  displayName: z.string().optional(),\n  startTime: z\n    .number()\n    .optional()\n    .describe('trace start time in milliseconds since the epoch'),\n  endTime: z\n    .number()\n    .optional()\n    .describe('end time in milliseconds since the epoch'),\n  spans: z.record(z.string(), SpanDataSchema),\n});\n\nexport type TraceData = z.infer<typeof TraceDataSchema>;\n"], "mappings": "AAgBA,SAAS,SAAS;AAMX,MAAM,qBAAqB,EAAE,OAAO;AAAA,EACzC,MAAM,EAAE,OAAO;AAAA,EACf,QAAQ,EAAE,OAAO;AAAA,EACjB,OAAO,EAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,SAAS,EAAE,OAAO;AACpB,CAAC;AAGM,MAAM,sBAAsB,EAAE,OAAO;AAAA,EAC1C,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,OAAO,EAAE,IAAI,kBAAkB,EAAE,SAAS;AAAA,EAC1C,WAAW,EAAE,OAAO;AACtB,CAAC;AAGM,MAAM,qBAAqB,EAAE,OAAO;AAAA,EACzC,MAAM,EAAE,OAAO;AAAA,EACf,OAAO,EAAE,KAAK,CAAC,WAAW,OAAO,CAAC,EAAE,SAAS;AAAA,EAC7C,OAAO,EAAE,IAAI,EAAE,SAAS;AAAA,EACxB,QAAQ,EAAE,IAAI,EAAE,SAAS;AAAA,EACzB,QAAQ,EAAE,QAAQ,EAAE,SAAS;AAAA,EAC7B,UAAU,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACpD,MAAM,EAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAE1B,iBAAiB,EAAE,QAAQ,EAAE,SAAS;AACxC,CAAC;AAGM,MAAM,mBAAmB,EAAE,OAAO;AAAA,EACvC,MAAM,EAAE,OAAO;AAAA,EACf,SAAS,EAAE,OAAO,EAAE,SAAS;AAC/B,CAAC;AAEM,MAAM,kBAAkB,EAAE,OAAO;AAAA,EACtC,MAAM,EAAE,OAAO;AAAA,EACf,YAAY,EAAE,OAAO;AAAA,IACnB,YAAY,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,IAAI,CAAC;AAAA,IACxC,aAAa,EAAE,OAAO;AAAA,EACxB,CAAC;AACH,CAAC;AAEM,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,SAAS,EAAE,OAAO;AAAA,EAClB,QAAQ,EAAE,OAAO;AAAA,EACjB,UAAU,EAAE,QAAQ,EAAE,SAAS;AAAA,EAC/B,YAAY,EAAE,OAAO;AACvB,CAAC;AAEM,MAAM,aAAa,EAAE,OAAO;AAAA,EACjC,SAAS,kBAAkB,SAAS;AAAA,EACpC,YAAY,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EACnD,wBAAwB,EAAE,OAAO,EAAE,SAAS;AAC9C,CAAC;AAEM,MAAM,+BAA+B,EAAE,OAAO;AAAA,EACnD,MAAM,EAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EACxC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAC5C,CAAC;AAEM,MAAM,iBAAiB,EAAE,OAAO;AAAA,EACrC,QAAQ,EAAE,OAAO;AAAA,EACjB,SAAS,EAAE,OAAO;AAAA,EAClB,cAAc,EAAE,OAAO,EAAE,SAAS;AAAA,EAClC,WAAW,EAAE,OAAO;AAAA,EACpB,SAAS,EAAE,OAAO;AAAA,EAClB,YAAY,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,IAAI,CAAC;AAAA,EACxC,aAAa,EAAE,OAAO;AAAA,EACtB,OAAO,EAAE,MAAM,UAAU,EAAE,SAAS;AAAA,EACpC,wBAAwB;AAAA,EACxB,UAAU,EAAE,OAAO;AAAA,EACnB,yBAAyB,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS;AAAA,EACnE,QAAQ,iBAAiB,SAAS;AAAA,EAClC,YAAY,EACT,OAAO;AAAA,IACN,WAAW,EAAE,MAAM,eAAe;AAAA,EACpC,CAAC,EACA,SAAS;AAAA,EACZ,WAAW,EAAE,QAAQ,EAAE,SAAS;AAClC,CAAC;AAGM,MAAM,kBAAkB,EAAE,OAAO;AAAA,EACtC,SAAS,EAAE,OAAO;AAAA,EAClB,aAAa,EAAE,OAAO,EAAE,SAAS;AAAA,EACjC,WAAW,EACR,OAAO,EACP,SAAS,EACT,SAAS,kDAAkD;AAAA,EAC9D,SAAS,EACN,OAAO,EACP,SAAS,EACT,SAAS,0CAA0C;AAAA,EACtD,OAAO,EAAE,OAAO,EAAE,OAAO,GAAG,cAAc;AAC5C,CAAC;", "names": []}