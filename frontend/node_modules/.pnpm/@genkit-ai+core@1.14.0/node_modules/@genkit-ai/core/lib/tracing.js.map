{"version": 3, "sources": ["../src/tracing.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NodeSDK } from '@opentelemetry/sdk-node';\nimport {\n  BatchSpanProcessor,\n  SimpleSpanProcessor,\n  type SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport { logger } from './logging.js';\nimport type { TelemetryConfig } from './telemetryTypes.js';\nimport {\n  TraceServerExporter,\n  setTelemetryServerUrl,\n} from './tracing/exporter.js';\nimport { isDevEnv } from './utils.js';\n\nexport * from './tracing/exporter.js';\nexport * from './tracing/instrumentation.js';\nexport * from './tracing/processor.js';\nexport * from './tracing/types.js';\n\nlet telemetrySDK: NodeSDK | null = null;\nlet nodeOtelConfig: TelemetryConfig | null = null;\n\nconst instrumentationKey = '__GENKIT_TELEMETRY_INSTRUMENTED';\n\n/**\n * @hidden\n */\nexport async function ensureBasicTelemetryInstrumentation() {\n  await checkFirebaseMonitoringAutoInit();\n\n  if (global[instrumentationKey]) {\n    return await global[instrumentationKey];\n  }\n\n  await enableTelemetry({});\n}\n\n/**\n * Checks to see if the customer is using Firebase Genkit Monitoring\n * auto initialization via environment variable by attempting to resolve\n * the firebase plugin.\n *\n * Enables Firebase Genkit Monitoring if the plugin is installed and warns\n * if it hasn't been installed.\n */\nasync function checkFirebaseMonitoringAutoInit() {\n  if (\n    !global[instrumentationKey] &&\n    process.env.ENABLE_FIREBASE_MONITORING === 'true'\n  ) {\n    try {\n      const firebaseModule = await require('@genkit-ai/firebase');\n      firebaseModule.enableFirebaseTelemetry();\n    } catch (e) {\n      logger.warn(\n        \"It looks like you're trying to enable firebase monitoring, but \" +\n          \"haven't installed the firebase plugin. Please run \" +\n          '`npm i --save @genkit-ai/firebase` and redeploy.'\n      );\n    }\n  }\n}\n\n/**\n * Enables tracing and metrics open telemetry configuration.\n */\nexport async function enableTelemetry(\n  telemetryConfig: TelemetryConfig | Promise<TelemetryConfig>\n) {\n  if (process.env.GENKIT_TELEMETRY_SERVER) {\n    setTelemetryServerUrl(process.env.GENKIT_TELEMETRY_SERVER);\n  }\n  global[instrumentationKey] =\n    telemetryConfig instanceof Promise ? telemetryConfig : Promise.resolve();\n\n  telemetryConfig =\n    telemetryConfig instanceof Promise\n      ? await telemetryConfig\n      : telemetryConfig;\n\n  nodeOtelConfig = telemetryConfig || {};\n\n  const processors: SpanProcessor[] = [createTelemetryServerProcessor()];\n  if (nodeOtelConfig.traceExporter) {\n    throw new Error('Please specify spanProcessors instead.');\n  }\n  if (nodeOtelConfig.spanProcessors) {\n    processors.push(...nodeOtelConfig.spanProcessors);\n  }\n  if (nodeOtelConfig.spanProcessor) {\n    processors.push(nodeOtelConfig.spanProcessor);\n    delete nodeOtelConfig.spanProcessor;\n  }\n  nodeOtelConfig.spanProcessors = processors;\n  telemetrySDK = new NodeSDK(nodeOtelConfig);\n  telemetrySDK.start();\n  process.on('SIGTERM', async () => await cleanUpTracing());\n}\n\nexport async function cleanUpTracing(): Promise<void> {\n  if (!telemetrySDK) {\n    return;\n  }\n\n  // Metrics are not flushed as part of the shutdown operation. If metrics\n  // are enabled, we need to manually flush them *before* the reader\n  // receives shutdown order.\n  await maybeFlushMetrics();\n  await telemetrySDK.shutdown();\n  logger.debug('OpenTelemetry SDK shut down.');\n  telemetrySDK = null;\n}\n\n/**\n * Creates a new SpanProcessor for exporting data to the telemetry server.\n */\nfunction createTelemetryServerProcessor(): SpanProcessor {\n  const exporter = new TraceServerExporter();\n  return isDevEnv()\n    ? new SimpleSpanProcessor(exporter)\n    : new BatchSpanProcessor(exporter);\n}\n\n/** Flush metrics if present. */\nfunction maybeFlushMetrics(): Promise<void> {\n  if (nodeOtelConfig?.metricReader) {\n    return nodeOtelConfig.metricReader.forceFlush();\n  }\n  return Promise.resolve();\n}\n\n/**\n * Flushes all configured span processors.\n *\n * @hidden\n */\nexport async function flushTracing() {\n  if (nodeOtelConfig?.spanProcessors) {\n    await Promise.all(nodeOtelConfig.spanProcessors.map((p) => p.forceFlush()));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,sBAAwB;AACxB,4BAIO;AACP,qBAAuB;AAEvB,sBAGO;AACP,mBAAyB;AAEzB,4BAAc,kCA9Bd;AA+BA,4BAAc,yCA/Bd;AAgCA,4BAAc,mCAhCd;AAiCA,4BAAc,+BAjCd;AAmCA,IAAI,eAA+B;AACnC,IAAI,iBAAyC;AAE7C,MAAM,qBAAqB;AAK3B,eAAsB,sCAAsC;AAC1D,QAAM,gCAAgC;AAEtC,MAAI,OAAO,kBAAkB,GAAG;AAC9B,WAAO,MAAM,OAAO,kBAAkB;AAAA,EACxC;AAEA,QAAM,gBAAgB,CAAC,CAAC;AAC1B;AAUA,eAAe,kCAAkC;AAC/C,MACE,CAAC,OAAO,kBAAkB,KAC1B,QAAQ,IAAI,+BAA+B,QAC3C;AACA,QAAI;AACF,YAAM,iBAAiB,MAAM,QAAQ,qBAAqB;AAC1D,qBAAe,wBAAwB;AAAA,IACzC,SAAS,GAAG;AACV,4BAAO;AAAA,QACL;AAAA,MAGF;AAAA,IACF;AAAA,EACF;AACF;AAKA,eAAsB,gBACpB,iBACA;AACA,MAAI,QAAQ,IAAI,yBAAyB;AACvC,+CAAsB,QAAQ,IAAI,uBAAuB;AAAA,EAC3D;AACA,SAAO,kBAAkB,IACvB,2BAA2B,UAAU,kBAAkB,QAAQ,QAAQ;AAEzE,oBACE,2BAA2B,UACvB,MAAM,kBACN;AAEN,mBAAiB,mBAAmB,CAAC;AAErC,QAAM,aAA8B,CAAC,+BAA+B,CAAC;AACrE,MAAI,eAAe,eAAe;AAChC,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AACA,MAAI,eAAe,gBAAgB;AACjC,eAAW,KAAK,GAAG,eAAe,cAAc;AAAA,EAClD;AACA,MAAI,eAAe,eAAe;AAChC,eAAW,KAAK,eAAe,aAAa;AAC5C,WAAO,eAAe;AAAA,EACxB;AACA,iBAAe,iBAAiB;AAChC,iBAAe,IAAI,wBAAQ,cAAc;AACzC,eAAa,MAAM;AACnB,UAAQ,GAAG,WAAW,YAAY,MAAM,eAAe,CAAC;AAC1D;AAEA,eAAsB,iBAAgC;AACpD,MAAI,CAAC,cAAc;AACjB;AAAA,EACF;AAKA,QAAM,kBAAkB;AACxB,QAAM,aAAa,SAAS;AAC5B,wBAAO,MAAM,8BAA8B;AAC3C,iBAAe;AACjB;AAKA,SAAS,iCAAgD;AACvD,QAAM,WAAW,IAAI,oCAAoB;AACzC,aAAO,uBAAS,IACZ,IAAI,0CAAoB,QAAQ,IAChC,IAAI,yCAAmB,QAAQ;AACrC;AAGA,SAAS,oBAAmC;AAC1C,MAAI,gBAAgB,cAAc;AAChC,WAAO,eAAe,aAAa,WAAW;AAAA,EAChD;AACA,SAAO,QAAQ,QAAQ;AACzB;AAOA,eAAsB,eAAe;AACnC,MAAI,gBAAgB,gBAAgB;AAClC,UAAM,QAAQ,IAAI,eAAe,eAAe,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAAA,EAC5E;AACF;", "names": []}