{"version": 3, "sources": ["../../src/tracing/multiSpanProcessor.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Context } from '@opentelemetry/api';\nimport type {\n  ReadableSpan,\n  Span,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\n\n/**\n * A {SpanProcessor} wrapper that supports exporting to multiple {SpanProcessor}s.\n */\nexport class MultiSpanProcessor implements SpanProcessor {\n  constructor(private processors: SpanProcessor[]) {}\n\n  forceFlush(): Promise<void> {\n    return Promise.all(this.processors.map((p) => p.forceFlush())).then();\n  }\n\n  onStart(span: Span, parentContext: Context): void {\n    this.processors.map((p) => p.onStart(span, parentContext));\n  }\n\n  onEnd(span: ReadableSpan): void {\n    this.processors.map((p) => p.onEnd(span));\n  }\n\n  async shutdown(): Promise<void> {\n    return Promise.all(this.processors.map((p) => p.shutdown())).then();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BO,MAAM,mBAA4C;AAAA,EACvD,YAAoB,YAA6B;AAA7B;AAAA,EAA8B;AAAA,EAElD,aAA4B;AAC1B,WAAO,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,EAAE,KAAK;AAAA,EACtE;AAAA,EAEA,QAAQ,MAAY,eAA8B;AAChD,SAAK,WAAW,IAAI,CAAC,MAAM,EAAE,QAAQ,MAAM,aAAa,CAAC;AAAA,EAC3D;AAAA,EAEA,MAAM,MAA0B;AAC9B,SAAK,WAAW,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC;AAAA,EAC1C;AAAA,EAEA,MAAM,WAA0B;AAC9B,WAAO,QAAQ,IAAI,KAAK,WAAW,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,KAAK;AAAA,EACpE;AACF;", "names": []}