# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

openapi: 3.0.0
info:
  version: 0.0.1
  title: Genkit Reflection API
  description: >-
    A control API that allows clients to inspect app code to view actions, run
    them, and view the results.
components:
  schemas:
    SingularAny:
      anyOf:
        - type: string
        - type: number
        - type: integer
          format: int64
        - type: boolean
        - type: object
          properties: {}
    CustomAny:
      anyOf:
        - type: string
        - type: number
        - type: integer
          format: int64
        - type: boolean
        - type: object
          properties: {}
        - type: array
          items:
            $ref: '#/components/schemas/SingularAny'
    JSONSchema7:
      type: object
      nullable: true
      properties: {}
      description: A JSON Schema Draft 7 (http://json-schema.org/draft-07/schema) object.
    Action:
      type: object
      properties:
        key:
          type: string
          description: Action key consisting of action type and ID.
        name:
          type: string
          description: Action name.
        description:
          type: string
          nullable: true
          description: A description of what the action does.
        inputSchema:
          $ref: '#/components/schemas/JSONSchema7'
        outputSchema:
          $ref: '#/components/schemas/JSONSchema7'
        metadata:
          type: object
          nullable: true
          additionalProperties:
            $ref: '#/components/schemas/CustomAny'
          description: Metadata about the action (e.g. supported model features).
      required:
        - key
        - name
    FlowState:
      type: object
      properties:
        name:
          type: string
        flowId:
          type: string
        input:
          nullable: true
        startTime:
          type: number
        cache:
          type: object
          additionalProperties:
            type: object
            properties:
              value:
                nullable: true
              empty:
                type: boolean
                enum:
                  - true
        eventsTriggered:
          type: object
          additionalProperties:
            nullable: true
        blockedOnStep:
          type: object
          nullable: true
          properties:
            name:
              type: string
            schema:
              type: string
          required:
            - name
        operation:
          type: object
          properties:
            name:
              type: string
              description: >-
                server-assigned name, which is only unique within the same
                service that originally returns it.
            metadata:
              nullable: true
              description: >-
                Service-specific metadata associated with the operation. It
                typically contains progress information and common metadata such
                as create time.
            done:
              type: boolean
              default: false
              description: >-
                If the value is false, it means the operation is still in
                progress. If true, the operation is completed, and either error
                or response is available.
            result:
              allOf:
                - type: object
                  properties:
                    response:
                      nullable: true
                - type: object
                  properties:
                    error:
                      type: string
                    stacktrace:
                      type: string
          required:
            - name
        traceContext:
          type: string
        executions:
          type: array
          items:
            type: object
            properties:
              startTime:
                type: number
              endTime:
                type: number
              traceIds:
                type: array
                items:
                  type: string
            required:
              - traceIds
      required:
        - flowId
        - startTime
        - cache
        - eventsTriggered
        - blockedOnStep
        - operation
        - executions
    SpanData:
      type: object
      properties:
        spanId:
          type: string
        traceId:
          type: string
        parentSpanId:
          type: string
        startTime:
          type: number
        endTime:
          type: number
        attributes:
          type: object
          additionalProperties:
            nullable: true
        displayName:
          type: string
        links:
          type: array
          items:
            type: object
            properties:
              context:
                type: object
                properties:
                  traceId:
                    type: string
                  spanId:
                    type: string
                  isRemote:
                    type: boolean
                  traceFlags:
                    type: number
                required:
                  - traceId
                  - spanId
                  - traceFlags
              attributes:
                type: object
                additionalProperties:
                  nullable: true
              droppedAttributesCount:
                type: number
        instrumentationLibrary:
          type: object
          properties:
            name:
              type: string
            version:
              type: string
            schemaUrl:
              type: string
          required:
            - name
        spanKind:
          type: string
        sameProcessAsParentSpan:
          type: object
          properties:
            value:
              type: boolean
          required:
            - value
        status:
          type: object
          properties:
            code:
              type: number
            message:
              type: string
          required:
            - code
        timeEvents:
          type: object
          properties:
            timeEvent:
              type: array
              items:
                type: object
                properties:
                  time:
                    type: number
                  annotation:
                    type: object
                    properties:
                      attributes:
                        type: object
                        additionalProperties:
                          nullable: true
                      description:
                        type: string
                    required:
                      - attributes
                      - description
                required:
                  - time
                  - annotation
          required:
            - timeEvent
      required:
        - spanId
        - traceId
        - startTime
        - endTime
        - attributes
        - displayName
        - instrumentationLibrary
        - spanKind
    TraceData:
      type: object
      properties:
        displayName:
          type: string
        startTime:
          type: number
        endTime:
          type: number
        spans:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SpanData'
      required:
        - spans
  parameters: {}
paths:
  /api/actions:
    get:
      summary: Retrieves all runnable actions.
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  $ref: '#/components/schemas/Action'
  /api/runAction:
    post:
      summary: Runs an action and returns the result.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                key:
                  type: string
                  description: Action key that consists of the action type and ID.
                input:
                  nullable: true
                  description: An input with the type that this action expects.
              required:
                - key
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CustomAny'
                  - description: An output with the type that this action returns.
  /api/envs/{env}/traces:
    get:
      summary: Retrieves all traces for a given environment (e.g. dev or prod).
      parameters:
        - schema:
            type: string
            enum: &ref_0
              - dev
              - prod
            description: Supported environments in the runtime.
          required: false
          description: Supported environments in the runtime.
          name: env
          in: path
        - schema:
            type: number
          required: false
          name: limit
          in: path
        - schema:
            type: string
          required: false
          name: continuationToken
          in: path
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TraceData'
  /api/envs/{env}/traces/{traceId}:
    get:
      summary: Retrieves traces for the given environment.
      parameters:
        - schema:
            type: string
            enum: *ref_0
            description: Supported environments in the runtime.
          required: true
          description: Supported environments in the runtime.
          name: env
          in: path
        - schema:
            type: string
            description: ID of the trace.
          required: true
          description: ID of the trace.
          name: traceId
          in: path
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TraceData'
  /api/envs/{env}/flowStates:
    get:
      summary: Retrieves all flow states for a given environment (e.g. dev or prod).
      parameters:
        - schema:
            type: string
            enum: *ref_0
            description: Supported environments in the runtime.
          required: false
          description: Supported environments in the runtime.
          name: env
          in: path
        - schema:
            type: number
          required: false
          name: limit
          in: path
        - schema:
            type: string
          required: false
          name: continuationToken
          in: path
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FlowState'
  /api/envs/{env}/flowStates/{flowId}:
    get:
      summary: Retrieves a flow state for the given ID.
      parameters:
        - schema:
            type: string
            enum: *ref_0
            description: Supported environments in the runtime.
          required: true
          description: Supported environments in the runtime.
          name: env
          in: path
        - schema:
            type: string
            description: ID of the flow state.
          required: true
          description: ID of the flow state.
          name: flowId
          in: path
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowState'
