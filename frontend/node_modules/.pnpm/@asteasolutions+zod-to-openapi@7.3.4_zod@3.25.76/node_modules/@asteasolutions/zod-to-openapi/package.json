{"name": "@asteasolutions/zod-to-openapi", "version": "7.3.4", "description": "Builds OpenAPI schemas from Zod schemas", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist", "package.json", "LICENSE", "README.md"], "keywords": ["typescript", "schema", "type", "openapi", "zod"], "repository": {"type": "git", "url": "https://github.com/asteasolutions/zod-to-openapi"}, "homepage": "https://github.com/asteasolutions/zod-to-openapi", "scripts": {"build": "rollup -c", "prepare": "npm run build", "test": "jest", "prettier": "prettier --write .", "lint": "prettier --check .", "prepublishOnly": "npm run build"}, "dependencies": {"openapi3-ts": "^4.1.2"}, "peerDependencies": {"zod": "^3.20.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@types/jest": "^29.2.5", "jest": "^29.3.1", "prettier": "^2.7.1", "rollup": "^4.13.2", "rollup-plugin-typescript2": "^0.36.0", "ts-jest": "^29.0.3", "typescript": "^5.2.2", "yaml": "^2.2.2", "zod": "^3.22.0"}, "author": "Astea Solutions <<EMAIL>>", "license": "MIT"}