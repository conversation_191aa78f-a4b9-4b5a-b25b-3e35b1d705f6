import type { z } from 'zod';
export type ZodTypes = {
    ZodAny: z.<PERSON>;
    ZodArray: z.<PERSON><any>;
    ZodBigInt: z.ZodBigInt;
    ZodBoolean: z.ZodBoolean;
    ZodBranded: z.<PERSON><any, any>;
    ZodDefault: z.<PERSON><any>;
    ZodEffects: z.Z<PERSON><any>;
    ZodEnum: z.ZodEnum<any>;
    ZodIntersection: z.ZodIntersection<any, any>;
    ZodLiteral: z.<PERSON><any>;
    ZodNativeEnum: z.ZodNativeEnum<any>;
    ZodNever: z.ZodNever;
    ZodNull: z.ZodNull;
    ZodNullable: z.Zod<PERSON>ullable<any>;
    ZodNumber: z.ZodNumber;
    ZodObject: z.AnyZodObject;
    ZodOptional: z.ZodOptional<any>;
    ZodPipeline: z.Zod<PERSON>ipeline<any, any>;
    ZodReadonly: z.<PERSON><PERSON>only<any>;
    ZodRecord: z.<PERSON>od<PERSON>;
    ZodSchema: z.Z<PERSON>chema;
    ZodString: z.ZodString;
    ZodTuple: z.<PERSON>uple;
    ZodType: z.ZodType;
    ZodTypeAny: z.ZodTypeAny;
    ZodUnion: z.ZodUnion<any>;
    ZodDiscriminatedUnion: z.ZodDiscriminatedUnion<any, any>;
    ZodUnknown: z.ZodUnknown;
    ZodVoid: z.ZodVoid;
    ZodDate: z.ZodDate;
};
export declare function isZodType<TypeName extends keyof ZodTypes>(schema: object, typeName: TypeName): schema is ZodTypes[TypeName];
export declare function isAnyZodType(schema: object): schema is z.ZodType;
