{"version": 3, "sources": ["../src/common.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport process from 'process';\n\nexport function getApiKeyFromEnvVar(): string | undefined {\n  return (\n    process.env.GEMINI_API_KEY ||\n    process.env.GOOGLE_API_KEY ||\n    process.env.GOOGLE_GENAI_API_KEY\n  );\n}\n"], "mappings": "AAgBA,OAAO,aAAa;AAEb,SAAS,sBAA0C;AACxD,SACE,QAAQ,IAAI,kBACZ,QAAQ,IAAI,kBACZ,QAAQ,IAAI;AAEhB;", "names": []}