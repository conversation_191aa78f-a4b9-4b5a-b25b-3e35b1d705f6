#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/dist/bin/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/dist/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/dist/bin/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/dist/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules/genkit-cli/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/genkit-cli@1.14.0/node_modules:/home/<USER>/Working/Projects/NovelEditor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../genkit-cli/dist/bin/genkit.js" "$@"
else
  exec node  "$basedir/../genkit-cli/dist/bin/genkit.js" "$@"
fi
