{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,oWAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,oWAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,oWAAC,wXAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,wXAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/logo.tsx"], "sourcesContent": ["import { Feather } from 'lucide-react';\n\nexport default function Logo() {\n  return (\n    <div className=\"flex items-center gap-2 font-headline text-lg font-bold text-foreground\">\n      <div className=\"bg-primary text-primary-foreground p-2 rounded-md\">\n        <Feather className=\"h-5 w-5\" />\n      </div>\n      <span>WriteFlow</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,oWAAC;QAAI,WAAU;;0BACb,oWAAC;gBAAI,WAAU;0BACb,cAAA,oWAAC,+RAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAErB,oWAAC;0BAAK;;;;;;;;;;;;AAGZ;KATwB", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/register/page.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useTransition } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport Link from 'next/link';\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { useAuth } from '@/hooks/use-auth';\nimport { useRouter } from 'next/navigation';\nimport { Loader } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport Logo from '@/components/logo';\n\nconst registerSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\ntype RegisterFormValues = z.infer<typeof registerSchema>;\n\nexport default function RegisterPage() {\n  const [isPending, startTransition] = useTransition();\n  const { register: signup } = useAuth();\n  const router = useRouter();\n  const { toast } = useToast();\n\n  const { register, handleSubmit, formState: { errors } } = useForm<RegisterFormValues>({\n    resolver: zodResolver(registerSchema),\n  });\n\n  const onSubmit = (data: RegisterFormValues) => {\n    startTransition(async () => {\n      const result = await signup(data);\n      if (result.success) {\n        toast({ title: 'Registration Successful', description: 'Welcome to WriteFlow!' });\n        router.push('/');\n      } else {\n        toast({\n          variant: 'destructive',\n          title: 'Registration Failed',\n          description: result.error || 'An error occurred. Please try again.',\n        });\n      }\n    });\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-background\">\n      <Card className=\"mx-auto max-w-sm w-full\">\n         <CardHeader className=\"text-center\">\n            <div className=\"flex justify-center mb-4\">\n               <Logo />\n            </div>\n          <CardTitle className=\"text-2xl\">Sign Up</CardTitle>\n          <CardDescription>\n            Enter your information to create an account\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit(onSubmit)} className=\"grid gap-4\">\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"name\">Name</Label>\n              <Input id=\"name\" placeholder=\"Max Robinson\" {...register('name')} />\n              {errors.name && <p className=\"text-xs text-destructive\">{errors.name.message}</p>}\n            </div>\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"<EMAIL>\"\n                {...register('email')}\n              />\n              {errors.email && <p className=\"text-xs text-destructive\">{errors.email.message}</p>}\n            </div>\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input id=\"password\" type=\"password\" {...register('password')} />\n              {errors.password && <p className=\"text-xs text-destructive\">{errors.password.message}</p>}\n            </div>\n            <Button type=\"submit\" className=\"w-full\" disabled={isPending}>\n              {isPending && <Loader className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Create an account\n            </Button>\n          </form>\n          <div className=\"mt-4 text-center text-sm\">\n            Already have an account?{' '}\n            <Link href=\"/login\" className=\"underline\">\n              Login\n            </Link>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;AAuBA,MAAM,iBAAiB,uNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,EAAE,UAAU,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAsB;QACpF,UAAU,CAAA,GAAA,kSAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,CAAC;QAChB,gBAAgB;YACd,MAAM,SAAS,MAAM,OAAO;YAC5B,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBAAE,OAAO;oBAA2B,aAAa;gBAAwB;gBAC/E,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF;IACF;IAEA,qBACE,oWAAC;QAAI,WAAU;kBACb,cAAA,oWAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACb,oWAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACnB,oWAAC;4BAAI,WAAU;sCACZ,cAAA,oWAAC,6HAAA,CAAA,UAAI;;;;;;;;;;sCAEV,oWAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAW;;;;;;sCAChC,oWAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,oWAAC,mIAAA,CAAA,cAAW;;sCACV,oWAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAChD,oWAAC;oCAAI,WAAU;;sDACb,oWAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,oWAAC,oIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAO,aAAY;4CAAgB,GAAG,SAAS,OAAO;;;;;;wCAC/D,OAAO,IAAI,kBAAI,oWAAC;4CAAE,WAAU;sDAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAE9E,oWAAC;oCAAI,WAAU;;sDACb,oWAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,oWAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACX,GAAG,SAAS,QAAQ;;;;;;wCAEtB,OAAO,KAAK,kBAAI,oWAAC;4CAAE,WAAU;sDAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAEhF,oWAAC;oCAAI,WAAU;;sDACb,oWAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,oWAAC,oIAAA,CAAA,QAAK;4CAAC,IAAG;4CAAW,MAAK;4CAAY,GAAG,SAAS,WAAW;;;;;;wCAC5D,OAAO,QAAQ,kBAAI,oWAAC;4CAAE,WAAU;sDAA4B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAEtF,oWAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,WAAU;oCAAS,UAAU;;wCAChD,2BAAa,oWAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;;sCAInE,oWAAC;4BAAI,WAAU;;gCAA2B;gCACf;8CACzB,oWAAC,sUAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GA3EwB;;QACe,oUAAA,CAAA,gBAAa;QACrB,+HAAA,CAAA,UAAO;QACrB,4SAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;QAEgC,0PAAA,CAAA,UAAO;;;KAN3C", "debugId": null}}]}