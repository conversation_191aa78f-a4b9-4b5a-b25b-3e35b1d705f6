self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"006c95642497d0ef7b443912f13eb64d1d84fe07ff\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/register/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/register/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/register/page\": \"action-browser\"\n      }\n    },\n    \"40145d5f48e9b2cd3ad23766b6412323fb6073c40e\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/register/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/register/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/register/page\": \"action-browser\"\n      }\n    },\n    \"004ae6244db5a42653133b3c5adbe531b9296f9472\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/register/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/register/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/register/page\": \"action-browser\"\n      }\n    },\n    \"40609dfbd242bb3d3cc09f0325f2192c5f2939a0c8\": {\n      \"workers\": {\n        \"app/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/register/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/register/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/login/page\": \"action-browser\",\n        \"app/page\": \"action-browser\",\n        \"app/register/page\": \"action-browser\"\n      }\n    },\n    \"408c527087202fce424335aeb9e3711638e310c439\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"407f83a3932dac03386503b2d6a9b54a9721edcab7\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"00b0c1e0031427d905a7d807d3319bef4ae5e52651\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"ZQKO4g1jCvdBnVojnjLX30DCCBvogO9ULJVcicc4k08=\"\n}"