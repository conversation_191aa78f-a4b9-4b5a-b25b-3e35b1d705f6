{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/check-operation.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError, Operation } from '@genkit-ai/core';\nimport { Registry } from '@genkit-ai/core/registry';\n\nexport async function checkOperation<T = unknown>(\n  registry: Registry,\n  operation: Operation<T>\n): Promise<Operation<T>> {\n  if (!operation.action) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Provided operation is missing original request information',\n    });\n  }\n  const backgroundAction = await registry.lookupBackgroundAction(\n    operation.action\n  );\n  if (!backgroundAction) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `Failed to resolve background action from original request: ${operation.action}`,\n    });\n  }\n  return await backgroundAction.check(operation);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,0BAAA,CAAA;AAAA,SAAA,yBAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAAuC;AAGvC,eAAsB,eACpB,QAAA,EACA,SAAA,EACuB;IACvB,IAAI,CAAC,UAAU,MAAA,EAAQ;QACrB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;IACH;IACA,MAAM,mBAAmB,MAAM,SAAS,sBAAA,CACtC,UAAU,MAAA;IAEZ,IAAI,CAAC,kBAAkB;QACrB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,2DAAA,EAA8D,UAAU,MAAM,EAAA;QACzF,CAAC;IACH;IACA,OAAO,MAAM,iBAAiB,KAAA,CAAM,SAAS;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/document.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from '@genkit-ai/core';\nimport type { Embedding } from './embedder';\n\nconst EmptyPartSchema = z.object({\n  text: z.never().optional(),\n  media: z.never().optional(),\n  toolRequest: z.never().optional(),\n  toolResponse: z.never().optional(),\n  data: z.unknown().optional(),\n  metadata: z.record(z.unknown()).optional(),\n  custom: z.record(z.unknown()).optional(),\n  reasoning: z.never().optional(),\n  resource: z.never().optional(),\n});\n\n/**\n * Zod schema for a text part.\n */\nexport const TextPartSchema = EmptyPartSchema.extend({\n  /** The text of the message. */\n  text: z.string(),\n});\n\n/**\n * Zod schema for a reasoning part.\n */\nexport const ReasoningPartSchema = EmptyPartSchema.extend({\n  /** The reasoning text of the message. */\n  reasoning: z.string(),\n});\n\n/**\n * Text part.\n */\nexport type TextPart = z.infer<typeof TextPartSchema>;\n\n/**\n * Zod schema of media.\n */\nexport const MediaSchema = z.object({\n  /** The media content type. Inferred from data uri if not provided. */\n  contentType: z.string().optional(),\n  /** A `data:` or `https:` uri containing the media content.  */\n  url: z.string(),\n});\n\n/**\n * Zod schema of a media part.\n */\nexport const MediaPartSchema = EmptyPartSchema.extend({\n  media: MediaSchema,\n});\n\n/**\n * Media part.\n */\nexport type MediaPart = z.infer<typeof MediaPartSchema>;\n\n/**\n * Zod schema of a tool request.\n */\nexport const ToolRequestSchema = z.object({\n  /** The call id or reference for a specific request. */\n  ref: z.string().optional(),\n  /** The name of the tool to call. */\n  name: z.string(),\n  /** The input parameters for the tool, usually a JSON object. */\n  input: z.unknown().optional(),\n});\nexport type ToolRequest = z.infer<typeof ToolRequestSchema>;\n\n/**\n * Zod schema of a tool request part.\n */\nexport const ToolRequestPartSchema = EmptyPartSchema.extend({\n  /** A request for a tool to be executed, usually provided by a model. */\n  toolRequest: ToolRequestSchema,\n});\n\n/**\n * Tool part.\n */\nexport type ToolRequestPart = z.infer<typeof ToolRequestPartSchema>;\n\n/**\n * Zod schema of a tool response.\n */\nexport const ToolResponseSchema = z.object({\n  /** The call id or reference for a specific request. */\n  ref: z.string().optional(),\n  /** The name of the tool. */\n  name: z.string(),\n  /** The output data returned from the tool, usually a JSON object. */\n  output: z.unknown().optional(),\n});\nexport type ToolResponse = z.infer<typeof ToolResponseSchema>;\n\n/**\n * Zod schema of a tool response part.\n */\nexport const ToolResponsePartSchema = EmptyPartSchema.extend({\n  /** A provided response to a tool call. */\n  toolResponse: ToolResponseSchema,\n});\n\n/**\n * Tool response part.\n */\nexport type ToolResponsePart = z.infer<typeof ToolResponsePartSchema>;\n\n/**\n * Zod schema of a data part.\n */\nexport const DataPartSchema = EmptyPartSchema.extend({\n  data: z.unknown(),\n});\n\n/**\n * Data part.\n */\nexport type DataPart = z.infer<typeof DataPartSchema>;\n\n/**\n * Zod schema of a custom part.\n */\nexport const CustomPartSchema = EmptyPartSchema.extend({\n  custom: z.record(z.any()),\n});\n\n/**\n * Custom part.\n */\nexport type CustomPart = z.infer<typeof CustomPartSchema>;\n\n/**\n * Zod schema of a resource part.\n */\nexport const ResourcePartSchema = EmptyPartSchema.extend({\n  resource: z.object({\n    uri: z.string(),\n  }),\n});\n\n/**\n * Resource part.\n */\nexport type ResourcePart = z.infer<typeof ResourcePartSchema>;\n\nexport const PartSchema = z.union([TextPartSchema, MediaPartSchema]);\nexport type Part = z.infer<typeof PartSchema>;\n\n// We need both metadata and embedMetadata because they can\n// contain the same fields (e.g. video start/stop) with different values.\nexport const DocumentDataSchema = z.object({\n  content: z.array(PartSchema),\n  metadata: z.record(z.string(), z.any()).optional(),\n});\nexport type DocumentData = z.infer<typeof DocumentDataSchema>;\n\nfunction deepCopy<T>(value: T): T {\n  if (value === undefined) {\n    return value;\n  }\n  return JSON.parse(JSON.stringify(value)) as T;\n}\n\n/**\n * Document represents document content along with its metadata that can be embedded, indexed or\n * retrieved. Each document can contain multiple parts (for example text and an image)\n */\nexport class Document implements DocumentData {\n  content: Part[];\n  metadata?: Record<string, any>;\n\n  constructor(data: DocumentData) {\n    this.content = deepCopy(data.content);\n    this.metadata = deepCopy(data.metadata);\n  }\n\n  static fromText(text: string, metadata?: Record<string, any>) {\n    return new Document({\n      content: [{ text }],\n      metadata,\n    });\n  }\n\n  // Construct a Document from a single media item\n  static fromMedia(\n    url: string,\n    contentType?: string,\n    metadata?: Record<string, unknown>\n  ) {\n    return new Document({\n      content: [\n        {\n          media: {\n            contentType,\n            url,\n          },\n        },\n      ],\n      metadata,\n    });\n  }\n\n  // Construct a Document from content\n  static fromData(\n    data: string,\n    dataType?: string,\n    metadata?: Record<string, unknown>\n  ) {\n    if (dataType === 'text') {\n      return this.fromText(data, metadata);\n    }\n    return this.fromMedia(data, dataType, metadata);\n  }\n\n  /**\n   * Concatenates all `text` parts present in the document with no delimiter.\n   * @returns A string of all concatenated text parts.\n   */\n  get text(): string {\n    return this.content.map((part) => part.text || '').join('');\n  }\n\n  /**\n   * Media array getter.\n   * @returns the array of media parts.\n   */\n  get media(): { url: string; contentType?: string }[] {\n    return this.content\n      .filter((part) => part.media && !part.text)\n      .map((part) => part.media!);\n  }\n\n  /**\n   * Gets the first item in the document. Either text or media url.\n   */\n  get data(): string {\n    //\n    if (this.text) {\n      return this.text;\n    }\n    if (this.media) {\n      return this.media[0].url;\n    }\n    return '';\n  }\n\n  /**\n   * Gets the contentType of the data that is returned by data()\n   */\n  get dataType(): string | undefined {\n    if (this.text) {\n      return 'text';\n    }\n    if (this.media && this.media[0].contentType) {\n      return this.media[0].contentType;\n    }\n    return undefined;\n  }\n\n  toJSON(): DocumentData {\n    return {\n      content: deepCopy(this.content),\n      metadata: deepCopy(this.metadata),\n    } as DocumentData;\n  }\n\n  /**\n   * Embedders may return multiple embeddings for a single document.\n   * But storage still requires a 1:1 relationship. So we create an\n   * array of Documents from a single document - one per embedding.\n   * @param embeddings The embeddings to create the documents from.\n   * @returns an array of documents based on this document and the embeddings.\n   */\n  getEmbeddingDocuments(embeddings: Embedding[]): Document[] {\n    const documents: Document[] = [];\n    for (const embedding of embeddings) {\n      const jsonDoc = this.toJSON();\n      if (embedding.metadata) {\n        if (!jsonDoc.metadata) {\n          jsonDoc.metadata = {};\n        }\n        jsonDoc.metadata.embedMetadata = embedding.metadata;\n      }\n      documents.push(new Document(jsonDoc));\n    }\n    checkUniqueDocuments(documents);\n    return documents;\n  }\n}\n\n// Unique documents are important because we key\n// our vector storage on the Md5 hash of the JSON.stringify(document)\n// So if we have multiple duplicate documents with\n// different embeddings, we will either skip or overwrite\n// those entries and lose embedding information.\n// Export and boolean return value for testing only.\nexport function checkUniqueDocuments(documents: Document[]): boolean {\n  const seen = new Set();\n  for (const doc of documents) {\n    const serialized = JSON.stringify(doc);\n    if (seen.has(serialized)) {\n      console.warn(\n        'Warning: embedding documents are not unique. Are you missing embed metadata?'\n      );\n      return false;\n    }\n    seen.add(serialized);\n  }\n  return true;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,kBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,UAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,aAAA,IAAA;IAAA,YAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,wBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,sBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAAkB;AAGlB,MAAM,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC/B,MAAM,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;IACzB,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;IAC1B,aAAa,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;IAChC,cAAc,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;IACjC,MAAM,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC3B,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS;IACzC,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS;IACvC,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;IAC9B,UAAU,YAAA,CAAA,CAAE,KAAA,CAAM,EAAE,QAAA,CAAS;AAC/B,CAAC;AAKM,MAAM,iBAAiB,gBAAgB,MAAA,CAAO;IAAA,6BAAA,GAEnD,MAAM,YAAA,CAAA,CAAE,MAAA,CAAO;AACjB,CAAC;AAKM,MAAM,sBAAsB,gBAAgB,MAAA,CAAO;IAAA,uCAAA,GAExD,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO;AACtB,CAAC;AAUM,MAAM,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,oEAAA,GAElC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,6DAAA,GAEjC,KAAK,YAAA,CAAA,CAAE,MAAA,CAAO;AAChB,CAAC;AAKM,MAAM,kBAAkB,gBAAgB,MAAA,CAAO;IACpD,OAAO;AACT,CAAC;AAUM,MAAM,oBAAoB,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,qDAAA,GAExC,KAAK,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,kCAAA,GAEzB,MAAM,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,8DAAA,GAEf,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AAC9B,CAAC;AAMM,MAAM,wBAAwB,gBAAgB,MAAA,CAAO;IAAA,sEAAA,GAE1D,aAAa;AACf,CAAC;AAUM,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,qDAAA,GAEzC,KAAK,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,0BAAA,GAEzB,MAAM,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,mEAAA,GAEf,QAAQ,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AAC/B,CAAC;AAMM,MAAM,yBAAyB,gBAAgB,MAAA,CAAO;IAAA,wCAAA,GAE3D,cAAc;AAChB,CAAC;AAUM,MAAM,iBAAiB,gBAAgB,MAAA,CAAO;IACnD,MAAM,YAAA,CAAA,CAAE,OAAA,CAAQ;AAClB,CAAC;AAUM,MAAM,mBAAmB,gBAAgB,MAAA,CAAO;IACrD,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC;AAC1B,CAAC;AAUM,MAAM,qBAAqB,gBAAgB,MAAA,CAAO;IACvD,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO;QACjB,KAAK,YAAA,CAAA,CAAE,MAAA,CAAO;IAChB,CAAC;AACH,CAAC;AAOM,MAAM,aAAa,YAAA,CAAA,CAAE,KAAA,CAAM;IAAC;IAAgB,eAAe;CAAC;AAK5D,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IACzC,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,UAAU;IAC3B,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,MAAA,CAAO,GAAG,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;AACnD,CAAC;AAGD,SAAS,SAAY,KAAA,EAAa;IAChC,IAAI,UAAU,KAAA,GAAW;QACvB,OAAO;IACT;IACA,OAAO,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,KAAK,CAAC;AACzC;AAMO,MAAM,SAAiC;IAC5C,QAAA;IACA,SAAA;IAEA,YAAY,IAAA,CAAoB;QAC9B,IAAA,CAAK,OAAA,GAAU,SAAS,KAAK,OAAO;QACpC,IAAA,CAAK,QAAA,GAAW,SAAS,KAAK,QAAQ;IACxC;IAEA,OAAO,SAAS,IAAA,EAAc,QAAA,EAAgC;QAC5D,OAAO,IAAI,SAAS;YAClB,SAAS;gBAAC;oBAAE;gBAAK,CAAC;aAAA;YAClB;QACF,CAAC;IACH;IAAA,gDAAA;IAGA,OAAO,UACL,GAAA,EACA,WAAA,EACA,QAAA,EACA;QACA,OAAO,IAAI,SAAS;YAClB,SAAS;gBACP;oBACE,OAAO;wBACL;wBACA;oBACF;gBACF;aACF;YACA;QACF,CAAC;IACH;IAAA,oCAAA;IAGA,OAAO,SACL,IAAA,EACA,QAAA,EACA,QAAA,EACA;QACA,IAAI,aAAa,QAAQ;YACvB,OAAO,IAAA,CAAK,QAAA,CAAS,MAAM,QAAQ;QACrC;QACA,OAAO,IAAA,CAAK,SAAA,CAAU,MAAM,UAAU,QAAQ;IAChD;IAAA;;;GAAA,GAMA,IAAI,OAAe;QACjB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,IAAQ,EAAE,EAAE,IAAA,CAAK,EAAE;IAC5D;IAAA;;;GAAA,GAMA,IAAI,QAAiD;QACnD,OAAO,IAAA,CAAK,OAAA,CACT,MAAA,CAAO,CAAC,OAAS,KAAK,KAAA,IAAS,CAAC,KAAK,IAAI,EACzC,GAAA,CAAI,CAAC,OAAS,KAAK,KAAM;IAC9B;IAAA;;GAAA,GAKA,IAAI,OAAe;QAEjB,IAAI,IAAA,CAAK,IAAA,EAAM;YACb,OAAO,IAAA,CAAK,IAAA;QACd;QACA,IAAI,IAAA,CAAK,KAAA,EAAO;YACd,OAAO,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAE,GAAA;QACvB;QACA,OAAO;IACT;IAAA;;GAAA,GAKA,IAAI,WAA+B;QACjC,IAAI,IAAA,CAAK,IAAA,EAAM;YACb,OAAO;QACT;QACA,IAAI,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAE,WAAA,EAAa;YAC3C,OAAO,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAE,WAAA;QACvB;QACA,OAAO,KAAA;IACT;IAEA,SAAuB;QACrB,OAAO;YACL,SAAS,SAAS,IAAA,CAAK,OAAO;YAC9B,UAAU,SAAS,IAAA,CAAK,QAAQ;QAClC;IACF;IAAA;;;;;;GAAA,GASA,sBAAsB,UAAA,EAAqC;QACzD,MAAM,YAAwB,CAAC,CAAA;QAC/B,KAAA,MAAW,aAAa,WAAY;YAClC,MAAM,UAAU,IAAA,CAAK,MAAA,CAAO;YAC5B,IAAI,UAAU,QAAA,EAAU;gBACtB,IAAI,CAAC,QAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,GAAW,CAAC;gBACtB;gBACA,QAAQ,QAAA,CAAS,aAAA,GAAgB,UAAU,QAAA;YAC7C;YACA,UAAU,IAAA,CAAK,IAAI,SAAS,OAAO,CAAC;QACtC;QACA,qBAAqB,SAAS;QAC9B,OAAO;IACT;AACF;AAQO,SAAS,qBAAqB,SAAA,EAAgC;IACnE,MAAM,OAAO,aAAA,GAAA,IAAI,IAAI;IACrB,KAAA,MAAW,OAAO,UAAW;QAC3B,MAAM,aAAa,KAAK,SAAA,CAAU,GAAG;QACrC,IAAI,KAAK,GAAA,CAAI,UAAU,GAAG;YACxB,QAAQ,IAAA,CACN;YAEF,OAAO;QACT;QACA,KAAK,GAAA,CAAI,UAAU;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/embedder.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  defineAction,\n  z,\n  type Action,\n  type ActionMetadata,\n} from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { Document, DocumentDataSchema, type DocumentData } from './document.js';\n\n/**\n * A batch (array) of embeddings.\n */\nexport type EmbeddingBatch = { embedding: number[] }[];\n\n/**\n * EmbeddingSchema includes the embedding and also metadata so you know\n * which of multiple embeddings corresponds to which part of a document.\n */\nexport const EmbeddingSchema = z.object({\n  embedding: z.array(z.number()),\n  metadata: z.record(z.string(), z.unknown()).optional(),\n});\nexport type Embedding = z.infer<typeof EmbeddingSchema>;\n\n/**\n * A function used for embedder definition, encapsulates embedder implementation.\n */\nexport type EmbedderFn<EmbedderOptions extends z.ZodTypeAny> = (\n  input: Document[],\n  embedderOpts?: z.infer<EmbedderOptions>\n) => Promise<EmbedResponse>;\n\n/**\n * Zod schema of an embed request.\n */\nconst EmbedRequestSchema = z.object({\n  input: z.array(DocumentDataSchema),\n  options: z.any().optional(),\n});\n\n/**\n * Zod schema of an embed response.\n */\nconst EmbedResponseSchema = z.object({\n  embeddings: z.array(EmbeddingSchema),\n  // TODO: stats, etc.\n});\ntype EmbedResponse = z.infer<typeof EmbedResponseSchema>;\n\n/**\n * Embedder action -- a subtype of {@link Action} with input/output types for embedders.\n */\nexport type EmbedderAction<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<typeof EmbedRequestSchema, typeof EmbedResponseSchema> & {\n    __configSchema?: CustomOptions;\n  };\n\n/**\n * Options of an `embed` function.\n */\nexport interface EmbedderParams<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  embedder: EmbedderArgument<CustomOptions>;\n  content: string | DocumentData;\n  metadata?: Record<string, unknown>;\n  options?: z.infer<CustomOptions>;\n}\n\nfunction withMetadata<CustomOptions extends z.ZodTypeAny>(\n  embedder: Action<typeof EmbedRequestSchema, typeof EmbedResponseSchema>,\n  configSchema?: CustomOptions\n): EmbedderAction<CustomOptions> {\n  const withMeta = embedder as EmbedderAction<CustomOptions>;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\n/**\n * Creates embedder model for the provided {@link EmbedderFn} model implementation.\n */\nexport function defineEmbedder<\n  ConfigSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    name: string;\n    configSchema?: ConfigSchema;\n    info?: EmbedderInfo;\n  },\n  runner: EmbedderFn<ConfigSchema>\n) {\n  const embedder = defineAction(\n    registry,\n    {\n      actionType: 'embedder',\n      name: options.name,\n      inputSchema: options.configSchema\n        ? EmbedRequestSchema.extend({\n            options: options.configSchema.optional(),\n          })\n        : EmbedRequestSchema,\n      outputSchema: EmbedResponseSchema,\n      metadata: {\n        type: 'embedder',\n        info: options.info,\n        embedder: {\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n        },\n      },\n    },\n    (i) =>\n      runner(\n        i.input.map((dd) => new Document(dd)),\n        i.options\n      )\n  );\n  const ewm = withMetadata(\n    embedder as Action<typeof EmbedRequestSchema, typeof EmbedResponseSchema>,\n    options.configSchema\n  );\n  return ewm;\n}\n\n/**\n * A union type representing all the types that can refer to an embedder.\n */\nexport type EmbedderArgument<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = string | EmbedderAction<CustomOptions> | EmbedderReference<CustomOptions>;\n\n/**\n * A veneer for interacting with embedder models.\n */\nexport async function embed<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  params: EmbedderParams<CustomOptions>\n): Promise<Embedding[]> {\n  const embedder = await resolveEmbedder(registry, params);\n  if (!embedder.embedderAction) {\n    let embedderId: string;\n    if (typeof params.embedder === 'string') {\n      embedderId = params.embedder;\n    } else if ((params.embedder as EmbedderAction)?.__action?.name) {\n      embedderId = (params.embedder as EmbedderAction).__action.name;\n    } else {\n      embedderId = (params.embedder as EmbedderReference<any>).name;\n    }\n    throw new Error(`Unable to resolve embedder ${embedderId}`);\n  }\n  const response = await embedder.embedderAction({\n    input:\n      typeof params.content === 'string'\n        ? [Document.fromText(params.content, params.metadata)]\n        : [params.content],\n    options: {\n      version: embedder.version,\n      ...embedder.config,\n      ...params.options,\n    },\n  });\n  return response.embeddings;\n}\n\ninterface ResolvedEmbedder<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> {\n  embedderAction: EmbedderAction<CustomOptions>;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\nasync function resolveEmbedder<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  params: EmbedderParams<CustomOptions>\n): Promise<ResolvedEmbedder<CustomOptions>> {\n  if (typeof params.embedder === 'string') {\n    return {\n      embedderAction: await registry.lookupAction(\n        `/embedder/${params.embedder}`\n      ),\n    };\n  } else if (Object.hasOwnProperty.call(params.embedder, '__action')) {\n    return {\n      embedderAction: params.embedder as EmbedderAction<CustomOptions>,\n    };\n  } else if (Object.hasOwnProperty.call(params.embedder, 'name')) {\n    const ref = params.embedder as EmbedderReference<any>;\n    return {\n      embedderAction: await registry.lookupAction(\n        `/embedder/${(params.embedder as EmbedderReference).name}`\n      ),\n      config: {\n        ...ref.config,\n      },\n      version: ref.version,\n    };\n  }\n  throw new Error(`failed to resolve embedder ${params.embedder}`);\n}\n\n/**\n * A veneer for interacting with embedder models in bulk.\n */\nexport async function embedMany<\n  ConfigSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  params: {\n    embedder: EmbedderArgument<ConfigSchema>;\n    content: string[] | DocumentData[];\n    metadata?: Record<string, unknown>;\n    options?: z.infer<ConfigSchema>;\n  }\n): Promise<EmbeddingBatch> {\n  let embedder: EmbedderAction<ConfigSchema>;\n  if (typeof params.embedder === 'string') {\n    embedder = await registry.lookupAction(`/embedder/${params.embedder}`);\n  } else if (Object.hasOwnProperty.call(params.embedder, 'info')) {\n    embedder = await registry.lookupAction(\n      `/embedder/${(params.embedder as EmbedderReference).name}`\n    );\n  } else {\n    embedder = params.embedder as EmbedderAction<ConfigSchema>;\n  }\n  if (!embedder) {\n    throw new Error('Unable to utilize the provided embedder');\n  }\n  const response = await embedder({\n    input: params.content.map((i) =>\n      typeof i === 'string' ? Document.fromText(i, params.metadata) : i\n    ),\n    options: params.options,\n  });\n  return response.embeddings;\n}\n\n/**\n * Zod schema of embedder info object.\n */\nexport const EmbedderInfoSchema = z.object({\n  /** Friendly label for this model (e.g. \"Google AI - Gemini Pro\") */\n  label: z.string().optional(),\n  /** Supported model capabilities. */\n  supports: z\n    .object({\n      /** Model can input this type of data. */\n      input: z.array(z.enum(['text', 'image', 'video'])).optional(),\n      /** Model can support multiple languages */\n      multilingual: z.boolean().optional(),\n    })\n    .optional(),\n  /** Embedding dimension */\n  dimensions: z.number().optional(),\n});\nexport type EmbedderInfo = z.infer<typeof EmbedderInfoSchema>;\n\n/**\n * A reference object that can used to resolve an embedder instance. Include additional type information\n * about the specific embedder, e.g. custom config options schema.\n */\nexport interface EmbedderReference<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: EmbedderInfo;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\n/**\n * Helper method to configure a {@link EmbedderReference} to a plugin.\n */\nexport function embedderRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: EmbedderReference<CustomOptionsSchema>\n): EmbedderReference<CustomOptionsSchema> {\n  return { ...options };\n}\n\n/**\n * Packages embedder information into ActionMetadata object.\n */\nexport function embedderActionMetadata({\n  name,\n  info,\n  configSchema,\n}: {\n  name: string;\n  info?: EmbedderInfo;\n  configSchema?: z.ZodTypeAny;\n}): ActionMetadata {\n  return {\n    actionType: 'embedder',\n    name: name,\n    inputJsonSchema: toJsonSchema({ schema: EmbedRequestSchema }),\n    outputJsonSchema: toJsonSchema({ schema: EmbedResponseSchema }),\n    metadata: {\n      embedder: {\n        ...info,\n        customOptions: configSchema\n          ? toJsonSchema({ schema: configSchema })\n          : undefined,\n      },\n    },\n  } as ActionMetadata;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,oBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,OAAA,IAAA;IAAA,WAAA,IAAA;IAAA,wBAAA,IAAA;IAAA,aAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAKO;AAEP,IAAA,gBAA6B;AAC7B,IAAA,kBAAgE;AAWzD,MAAM,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO;IACtC,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC;IAC7B,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,MAAA,CAAO,GAAG,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS;AACvD,CAAC;AAcD,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IAClC,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,kBAAkB;IACjC,SAAS,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;AAC5B,CAAC;AAKD,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IACnC,YAAY,YAAA,CAAA,CAAE,KAAA,CAAM,eAAe;AAErC,CAAC;AAuBD,SAAS,aACP,QAAA,EACA,YAAA,EAC+B;IAC/B,MAAM,WAAW;IACjB,SAAS,cAAA,GAAiB;IAC1B,OAAO;AACT;AAKO,SAAS,eAGd,QAAA,EACA,OAAA,EAKA,MAAA,EACA;IACA,MAAM,WAAA,CAAA,GAAW,YAAA,YAAA,EACf,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa,QAAQ,YAAA,GACjB,mBAAmB,MAAA,CAAO;YACxB,SAAS,QAAQ,YAAA,CAAa,QAAA,CAAS;QACzC,CAAC,IACD;QACJ,cAAc;QACd,UAAU;YACR,MAAM;YACN,MAAM,QAAQ,IAAA;YACd,UAAU;gBACR,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;YACN;QACF;IACF,GACA,CAAC,IACC,OACE,EAAE,KAAA,CAAM,GAAA,CAAI,CAAC,KAAO,IAAI,gBAAA,QAAA,CAAS,EAAE,CAAC,GACpC,EAAE,OAAA;IAGR,MAAM,MAAM,aACV,UACA,QAAQ,YAAA;IAEV,OAAO;AACT;AAYA,eAAsB,MACpB,QAAA,EACA,MAAA,EACsB;IACtB,MAAM,WAAW,MAAM,gBAAgB,UAAU,MAAM;IACvD,IAAI,CAAC,SAAS,cAAA,EAAgB;QAC5B,IAAI;QACJ,IAAI,OAAO,OAAO,QAAA,KAAa,UAAU;YACvC,aAAa,OAAO,QAAA;QACtB,OAAA,IAAY,OAAO,QAAA,EAA6B,UAAU,MAAM;YAC9D,aAAc,OAAO,QAAA,CAA4B,QAAA,CAAS,IAAA;QAC5D,OAAO;YACL,aAAc,OAAO,QAAA,CAAoC,IAAA;QAC3D;QACA,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,UAAU,EAAE;IAC5D;IACA,MAAM,WAAW,MAAM,SAAS,cAAA,CAAe;QAC7C,OACE,OAAO,OAAO,OAAA,KAAY,WACtB;YAAC,gBAAA,QAAA,CAAS,QAAA,CAAS,OAAO,OAAA,EAAS,OAAO,QAAQ,CAAC;SAAA,GACnD;YAAC,OAAO,OAAO;SAAA;QACrB,SAAS;YACP,SAAS,SAAS,OAAA;YAClB,GAAG,SAAS,MAAA;YACZ,GAAG,OAAO,OAAA;QACZ;IACF,CAAC;IACD,OAAO,SAAS,UAAA;AAClB;AAQA,eAAe,gBAGb,QAAA,EACA,MAAA,EAC0C;IAC1C,IAAI,OAAO,OAAO,QAAA,KAAa,UAAU;QACvC,OAAO;YACL,gBAAgB,MAAM,SAAS,YAAA,CAC7B,CAAA,UAAA,EAAa,OAAO,QAAQ,EAAA;QAEhC;IACF,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,QAAA,EAAU,UAAU,GAAG;QAClE,OAAO;YACL,gBAAgB,OAAO,QAAA;QACzB;IACF,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,QAAA,EAAU,MAAM,GAAG;QAC9D,MAAM,MAAM,OAAO,QAAA;QACnB,OAAO;YACL,gBAAgB,MAAM,SAAS,YAAA,CAC7B,CAAA,UAAA,EAAc,OAAO,QAAA,CAA+B,IAAI,EAAA;YAE1D,QAAQ;gBACN,GAAG,IAAI,MAAA;YACT;YACA,SAAS,IAAI,OAAA;QACf;IACF;IACA,MAAM,IAAI,MAAM,CAAA,2BAAA,EAA8B,OAAO,QAAQ,EAAE;AACjE;AAKA,eAAsB,UAGpB,QAAA,EACA,MAAA,EAMyB;IACzB,IAAI;IACJ,IAAI,OAAO,OAAO,QAAA,KAAa,UAAU;QACvC,WAAW,MAAM,SAAS,YAAA,CAAa,CAAA,UAAA,EAAa,OAAO,QAAQ,EAAE;IACvE,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,QAAA,EAAU,MAAM,GAAG;QAC9D,WAAW,MAAM,SAAS,YAAA,CACxB,CAAA,UAAA,EAAc,OAAO,QAAA,CAA+B,IAAI,EAAA;IAE5D,OAAO;QACL,WAAW,OAAO,QAAA;IACpB;IACA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,yCAAyC;IAC3D;IACA,MAAM,WAAW,MAAM,SAAS;QAC9B,OAAO,OAAO,OAAA,CAAQ,GAAA,CAAI,CAAC,IACzB,OAAO,MAAM,WAAW,gBAAA,QAAA,CAAS,QAAA,CAAS,GAAG,OAAO,QAAQ,IAAI;QAElE,SAAS,OAAO,OAAA;IAClB,CAAC;IACD,OAAO,SAAS,UAAA;AAClB;AAKO,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,kEAAA,GAEzC,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,kCAAA,GAE3B,UAAU,YAAA,CAAA,CACP,MAAA,CAAO;QAAA,uCAAA,GAEN,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,IAAA,CAAK;YAAC;YAAQ;YAAS,OAAO;SAAC,CAAC,EAAE,QAAA,CAAS;QAAA,yCAAA,GAE5D,cAAc,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IACrC,CAAC,EACA,QAAA,CAAS;IAAA,wBAAA,GAEZ,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAClC,CAAC;AAoBM,SAAS,YAGd,OAAA,EACwC;IACxC,OAAO;QAAE,GAAG,OAAA;IAAQ;AACtB;AAKO,SAAS,uBAAuB,EACrC,IAAA,EACA,IAAA,EACA,YAAA,EACF,EAImB;IACjB,OAAO;QACL,YAAY;QACZ;QACA,iBAAA,CAAA,GAAiB,cAAA,YAAA,EAAa;YAAE,QAAQ;QAAmB,CAAC;QAC5D,kBAAA,CAAA,GAAkB,cAAA,YAAA,EAAa;YAAE,QAAQ;QAAoB,CAAC;QAC9D,UAAU;YACR,UAAU;gBACR,GAAG,IAAA;gBACH,eAAe,eAAA,CAAA,GACX,cAAA,YAAA,EAAa;oBAAE,QAAQ;gBAAa,CAAC,IACrC,KAAA;YACN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/evaluator.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { defineAction, z, type Action } from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from '@genkit-ai/core/tracing';\nimport { randomUUID } from 'crypto';\n\nexport const ATTR_PREFIX = 'genkit';\nexport const SPAN_STATE_ATTR = ATTR_PREFIX + ':state';\n\nexport const BaseDataPointSchema = z.object({\n  input: z.unknown(),\n  output: z.unknown().optional(),\n  context: z.array(z.unknown()).optional(),\n  reference: z.unknown().optional(),\n  testCaseId: z.string().optional(),\n  traceIds: z.array(z.string()).optional(),\n});\n\n// DataPoint that is to be used for actions. This needs testCaseId to be present.\nexport const BaseEvalDataPointSchema = BaseDataPointSchema.extend({\n  testCaseId: z.string(),\n});\nexport type BaseEvalDataPoint = z.infer<typeof BaseEvalDataPointSchema>;\n\nconst EvalStatusEnumSchema = z.enum(['UNKNOWN', 'PASS', 'FAIL']);\n\n/** Enum that indicates if an evaluation has passed or failed */\nexport enum EvalStatusEnum {\n  UNKNOWN = 'UNKNOWN',\n  PASS = 'PASS',\n  FAIL = 'FAIL',\n}\n\nexport const ScoreSchema = z.object({\n  id: z\n    .string()\n    .describe(\n      'Optional ID to differentiate different scores if applying in a single evaluation'\n    )\n    .optional(),\n  score: z.union([z.number(), z.string(), z.boolean()]).optional(),\n  status: EvalStatusEnumSchema.optional(),\n  error: z.string().optional(),\n  details: z\n    .object({\n      reasoning: z.string().optional(),\n    })\n    .passthrough()\n    .optional(),\n});\n\n// Update genkit-tools/src/utils/evals.ts if you change this value\nexport const EVALUATOR_METADATA_KEY_DISPLAY_NAME = 'evaluatorDisplayName';\nexport const EVALUATOR_METADATA_KEY_DEFINITION = 'evaluatorDefinition';\nexport const EVALUATOR_METADATA_KEY_IS_BILLED = 'evaluatorIsBilled';\n\nexport type Score = z.infer<typeof ScoreSchema>;\nexport type BaseDataPoint = z.infer<typeof BaseDataPointSchema>;\nexport type Dataset<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n> = Array<z.infer<DataPoint>>;\n\nexport const EvalResponseSchema = z.object({\n  sampleIndex: z.number().optional(),\n  testCaseId: z.string(),\n  traceId: z.string().optional(),\n  spanId: z.string().optional(),\n  evaluation: z.union([ScoreSchema, z.array(ScoreSchema)]),\n});\nexport type EvalResponse = z.infer<typeof EvalResponseSchema>;\n\nexport const EvalResponsesSchema = z.array(EvalResponseSchema);\nexport type EvalResponses = z.infer<typeof EvalResponsesSchema>;\n\nexport type EvaluatorFn<\n  EvalDataPoint extends\n    typeof BaseEvalDataPointSchema = typeof BaseEvalDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = (\n  input: z.infer<EvalDataPoint>,\n  evaluatorOptions?: z.infer<CustomOptions>\n) => Promise<EvalResponse>;\n\nexport type EvaluatorAction<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<typeof EvalRequestSchema, typeof EvalResponsesSchema> & {\n  __dataPointType?: DataPoint;\n  __configSchema?: CustomOptions;\n};\n\nfunction withMetadata<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  evaluator: Action<typeof EvalRequestSchema, typeof EvalResponsesSchema>,\n  dataPointType?: DataPoint,\n  configSchema?: CustomOptions\n): EvaluatorAction<DataPoint, CustomOptions> {\n  const withMeta = evaluator as EvaluatorAction<DataPoint, CustomOptions>;\n  withMeta.__dataPointType = dataPointType;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\nconst EvalRequestSchema = z.object({\n  dataset: z.array(BaseDataPointSchema),\n  evalRunId: z.string(),\n  options: z.unknown(),\n});\n\nexport interface EvaluatorParams<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  evaluator: EvaluatorArgument<DataPoint, CustomOptions>;\n  dataset: Dataset<DataPoint>;\n  evalRunId?: string;\n  options?: z.infer<CustomOptions>;\n}\n\n/**\n * Creates evaluator action for the provided {@link EvaluatorFn} implementation.\n */\nexport function defineEvaluator<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  EvalDataPoint extends\n    typeof BaseEvalDataPointSchema = typeof BaseEvalDataPointSchema,\n  EvaluatorOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    name: string;\n    displayName: string;\n    definition: string;\n    dataPointType?: DataPoint;\n    configSchema?: EvaluatorOptions;\n    isBilled?: boolean;\n  },\n  runner: EvaluatorFn<EvalDataPoint, EvaluatorOptions>\n) {\n  const evalMetadata = {};\n  evalMetadata[EVALUATOR_METADATA_KEY_IS_BILLED] =\n    options.isBilled == undefined ? true : options.isBilled;\n  evalMetadata[EVALUATOR_METADATA_KEY_DISPLAY_NAME] = options.displayName;\n  evalMetadata[EVALUATOR_METADATA_KEY_DEFINITION] = options.definition;\n  if (options.configSchema) {\n    evalMetadata['customOptions'] = toJsonSchema({\n      schema: options.configSchema,\n    });\n  }\n  const evaluator = defineAction(\n    registry,\n    {\n      actionType: 'evaluator',\n      name: options.name,\n      inputSchema: EvalRequestSchema.extend({\n        dataset: options.dataPointType\n          ? z.array(options.dataPointType)\n          : z.array(BaseDataPointSchema),\n        options: options.configSchema ?? z.unknown(),\n        evalRunId: z.string(),\n        batchSize: z.number().optional(),\n      }),\n      outputSchema: EvalResponsesSchema,\n      metadata: {\n        type: 'evaluator',\n        evaluator: evalMetadata,\n      },\n    },\n    async (i) => {\n      const evalResponses: EvalResponses = [];\n      // This also populates missing testCaseIds\n      const batches = getBatchedArray(i.dataset, i.batchSize);\n\n      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {\n        const batch = batches[batchIndex];\n        try {\n          await runInNewSpan(\n            registry,\n            {\n              metadata: {\n                name: i.batchSize\n                  ? `Batch ${batchIndex}`\n                  : `Test Case ${batch[0].testCaseId}`,\n                metadata: { 'evaluator:evalRunId': i.evalRunId },\n              },\n              labels: {\n                [SPAN_TYPE_ATTR]: 'evaluator',\n              },\n            },\n            async (metadata, otSpan) => {\n              const spanId = otSpan.spanContext().spanId;\n              const traceId = otSpan.spanContext().traceId;\n              const evalRunPromises = batch.map((d, index) => {\n                const sampleIndex = i.batchSize\n                  ? i.batchSize * batchIndex + index\n                  : batchIndex;\n                const datapoint = d as BaseEvalDataPoint;\n                metadata.input = {\n                  input: datapoint.input,\n                  output: datapoint.output,\n                  context: datapoint.context,\n                };\n                const evalOutputPromise = runner(datapoint, i.options)\n                  .then((result) => ({\n                    ...result,\n                    traceId,\n                    spanId,\n                    sampleIndex,\n                  }))\n                  .catch((error) => {\n                    return {\n                      sampleIndex,\n                      spanId,\n                      traceId,\n                      testCaseId: datapoint.testCaseId,\n                      evaluation: {\n                        error: `Evaluation of test case ${datapoint.testCaseId} failed: \\n${error}`,\n                      },\n                    };\n                  });\n                return evalOutputPromise;\n              });\n\n              const allResults = await Promise.all(evalRunPromises);\n              metadata.output =\n                allResults.length === 1 ? allResults[0] : allResults;\n              allResults.map((result) => {\n                evalResponses.push(result);\n              });\n            }\n          );\n        } catch (e) {\n          logger.error(\n            `Evaluation of batch ${batchIndex} failed: \\n${(e as Error).stack}`\n          );\n          continue;\n        }\n      }\n      return evalResponses;\n    }\n  );\n  const ewm = withMetadata(\n    evaluator as any as Action<\n      typeof EvalRequestSchema,\n      typeof EvalResponsesSchema\n    >,\n    options.dataPointType,\n    options.configSchema\n  );\n  return ewm;\n}\n\nexport type EvaluatorArgument<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> =\n  | string\n  | EvaluatorAction<DataPoint, CustomOptions>\n  | EvaluatorReference<CustomOptions>;\n\n/**\n * A veneer for interacting with evaluators.\n */\nexport async function evaluate<\n  DataPoint extends typeof BaseDataPointSchema = typeof BaseDataPointSchema,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  params: EvaluatorParams<DataPoint, CustomOptions>\n): Promise<EvalResponses> {\n  let evaluator: EvaluatorAction<DataPoint, CustomOptions>;\n  if (typeof params.evaluator === 'string') {\n    evaluator = await registry.lookupAction(`/evaluator/${params.evaluator}`);\n  } else if (Object.hasOwnProperty.call(params.evaluator, 'info')) {\n    evaluator = await registry.lookupAction(\n      `/evaluator/${params.evaluator.name}`\n    );\n  } else {\n    evaluator = params.evaluator as EvaluatorAction<DataPoint, CustomOptions>;\n  }\n  if (!evaluator) {\n    throw new Error('Unable to utilize the provided evaluator');\n  }\n  return (await evaluator({\n    dataset: params.dataset,\n    options: params.options,\n    evalRunId: params.evalRunId ?? randomUUID(),\n  })) as EvalResponses;\n}\n\nexport const EvaluatorInfoSchema = z.object({\n  /** Friendly label for this evaluator */\n  label: z.string().optional(),\n  metrics: z.array(z.string()),\n});\nexport type EvaluatorInfo = z.infer<typeof EvaluatorInfoSchema>;\n\nexport interface EvaluatorReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: EvaluatorInfo;\n}\n\n/**\n * Helper method to configure a {@link EvaluatorReference} to a plugin.\n */\nexport function evaluatorRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: EvaluatorReference<CustomOptionsSchema>\n): EvaluatorReference<CustomOptionsSchema> {\n  return { ...options };\n}\n\n/**\n * Helper method to generated batched array. Also ensures each testCase has a\n * testCaseId\n */\nfunction getBatchedArray<T extends { testCaseId?: string }>(\n  arr: T[],\n  batchSize?: number\n): T[][] {\n  let size: number;\n  if (!batchSize) {\n    size = 1;\n  } else {\n    size = batchSize;\n  }\n\n  const batches: T[][] = [];\n  for (var i = 0; i < arr.length; i += size) {\n    batches.push(\n      arr.slice(i, i + size).map((d) => ({\n        ...d,\n        testCaseId: d.testCaseId ?? randomUUID(),\n      }))\n    );\n  }\n\n  return batches;\n}\n"], "names": ["EvalStatusEnum"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,oBAAA,CAAA;AAAA,SAAA,mBAAA;IAAA,aAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,yBAAA,IAAA;IAAA,mCAAA,IAAA;IAAA,qCAAA,IAAA;IAAA,kCAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,aAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,UAAA,IAAA;IAAA,cAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA6C;AAC7C,IAAA,iBAAuB;AAEvB,IAAA,gBAA6B;AAC7B,IAAA,iBAA6C;AAC7C,IAAA,gBAA2B;AAEpB,MAAM,cAAc;AACpB,MAAM,kBAAkB,cAAc;AAEtC,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC1C,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ;IACjB,QAAQ,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC7B,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS;IACvC,WAAW,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAChC,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAChC,UAAU,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;AACzC,CAAC;AAGM,MAAM,0BAA0B,oBAAoB,MAAA,CAAO;IAChE,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO;AACvB,CAAC;AAGD,MAAM,uBAAuB,YAAA,CAAA,CAAE,IAAA,CAAK;IAAC;IAAW;IAAQ,MAAM;CAAC;AAGxD,IAAK,iBAAL,aAAA,GAAA,CAAA,CAAKA,oBAAL;IACLA,eAAAA,CAAA,UAAA,GAAU;IACVA,eAAAA,CAAA,OAAA,GAAO;IACPA,eAAAA,CAAA,OAAA,GAAO;IAHG,OAAAA;AAAA,CAAA,EAAA,kBAAA,CAAA;AAML,MAAM,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO;IAClC,IAAI,YAAA,CAAA,CACD,MAAA,CAAO,EACP,QAAA,CACC,oFAED,QAAA,CAAS;IACZ,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM;QAAC,YAAA,CAAA,CAAE,MAAA,CAAO;QAAG,YAAA,CAAA,CAAE,MAAA,CAAO;QAAG,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC;KAAC,EAAE,QAAA,CAAS;IAC/D,QAAQ,qBAAqB,QAAA,CAAS;IACtC,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC3B,SAAS,YAAA,CAAA,CACN,MAAA,CAAO;QACN,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,CAAC,EACA,WAAA,CAAY,EACZ,QAAA,CAAS;AACd,CAAC;AAGM,MAAM,sCAAsC;AAC5C,MAAM,oCAAoC;AAC1C,MAAM,mCAAmC;AAQzC,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IACzC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO;IACrB,SAAS,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC7B,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC5B,YAAY,YAAA,CAAA,CAAE,KAAA,CAAM;QAAC;QAAa,YAAA,CAAA,CAAE,KAAA,CAAM,WAAW,CAAC;KAAC;AACzD,CAAC;AAGM,MAAM,sBAAsB,YAAA,CAAA,CAAE,KAAA,CAAM,kBAAkB;AAoB7D,SAAS,aAIP,SAAA,EACA,aAAA,EACA,YAAA,EAC2C;IAC3C,MAAM,WAAW;IACjB,SAAS,eAAA,GAAkB;IAC3B,SAAS,cAAA,GAAiB;IAC1B,OAAO;AACT;AAEA,MAAM,oBAAoB,YAAA,CAAA,CAAE,MAAA,CAAO;IACjC,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,mBAAmB;IACpC,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO;IACpB,SAAS,YAAA,CAAA,CAAE,OAAA,CAAQ;AACrB,CAAC;AAeM,SAAS,gBAMd,QAAA,EACA,OAAA,EAQA,MAAA,EACA;IACA,MAAM,eAAe,CAAC;IACtB,YAAA,CAAa,gCAAgC,CAAA,GAC3C,QAAQ,QAAA,IAAY,KAAA,IAAY,OAAO,QAAQ,QAAA;IACjD,YAAA,CAAa,mCAAmC,CAAA,GAAI,QAAQ,WAAA;IAC5D,YAAA,CAAa,iCAAiC,CAAA,GAAI,QAAQ,UAAA;IAC1D,IAAI,QAAQ,YAAA,EAAc;QACxB,YAAA,CAAa,eAAe,CAAA,GAAA,CAAA,GAAI,cAAA,YAAA,EAAa;YAC3C,QAAQ,QAAQ,YAAA;QAClB,CAAC;IACH;IACA,MAAM,YAAA,CAAA,GAAY,YAAA,YAAA,EAChB,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa,kBAAkB,MAAA,CAAO;YACpC,SAAS,QAAQ,aAAA,GACb,YAAA,CAAA,CAAE,KAAA,CAAM,QAAQ,aAAa,IAC7B,YAAA,CAAA,CAAE,KAAA,CAAM,mBAAmB;YAC/B,SAAS,QAAQ,YAAA,IAAgB,YAAA,CAAA,CAAE,OAAA,CAAQ;YAC3C,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO;YACpB,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;QACjC,CAAC;QACD,cAAc;QACd,UAAU;YACR,MAAM;YACN,WAAW;QACb;IACF,GACA,OAAO,MAAM;QACX,MAAM,gBAA+B,CAAC,CAAA;QAEtC,MAAM,UAAU,gBAAgB,EAAE,OAAA,EAAS,EAAE,SAAS;QAEtD,IAAA,IAAS,aAAa,GAAG,aAAa,QAAQ,MAAA,EAAQ,aAAc;YAClE,MAAM,QAAQ,OAAA,CAAQ,UAAU,CAAA;YAChC,IAAI;gBACF,MAAA,CAAA,GAAM,eAAA,YAAA,EACJ,UACA;oBACE,UAAU;wBACR,MAAM,EAAE,SAAA,GACJ,CAAA,MAAA,EAAS,UAAU,EAAA,GACnB,CAAA,UAAA,EAAa,KAAA,CAAM,CAAC,CAAA,CAAE,UAAU,EAAA;wBACpC,UAAU;4BAAE,uBAAuB,EAAE,SAAA;wBAAU;oBACjD;oBACA,QAAQ;wBACN,CAAC,eAAA,cAAc,CAAA,EAAG;oBACpB;gBACF,GACA,OAAO,UAAU,WAAW;oBAC1B,MAAM,SAAS,OAAO,WAAA,CAAY,EAAE,MAAA;oBACpC,MAAM,UAAU,OAAO,WAAA,CAAY,EAAE,OAAA;oBACrC,MAAM,kBAAkB,MAAM,GAAA,CAAI,CAAC,GAAG,UAAU;wBAC9C,MAAM,cAAc,EAAE,SAAA,GAClB,EAAE,SAAA,GAAY,aAAa,QAC3B;wBACJ,MAAM,YAAY;wBAClB,SAAS,KAAA,GAAQ;4BACf,OAAO,UAAU,KAAA;4BACjB,QAAQ,UAAU,MAAA;4BAClB,SAAS,UAAU,OAAA;wBACrB;wBACA,MAAM,oBAAoB,OAAO,WAAW,EAAE,OAAO,EAClD,IAAA,CAAK,CAAC,SAAA,CAAY;gCACjB,GAAG,MAAA;gCACH;gCACA;gCACA;4BACF,CAAA,CAAE,EACD,KAAA,CAAM,CAAC,UAAU;4BAChB,OAAO;gCACL;gCACA;gCACA;gCACA,YAAY,UAAU,UAAA;gCACtB,YAAY;oCACV,OAAO,CAAA,wBAAA,EAA2B,UAAU,UAAU,CAAA;AAAA,EAAc,KAAK,EAAA;gCAC3E;4BACF;wBACF,CAAC;wBACH,OAAO;oBACT,CAAC;oBAED,MAAM,aAAa,MAAM,QAAQ,GAAA,CAAI,eAAe;oBACpD,SAAS,MAAA,GACP,WAAW,MAAA,KAAW,IAAI,UAAA,CAAW,CAAC,CAAA,GAAI;oBAC5C,WAAW,GAAA,CAAI,CAAC,WAAW;wBACzB,cAAc,IAAA,CAAK,MAAM;oBAC3B,CAAC;gBACH;YAEJ,EAAA,OAAS,GAAG;gBACV,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,oBAAA,EAAuB,UAAU,CAAA;AAAA,EAAe,EAAY,KAAK,EAAA;gBAEnE;YACF;QACF;QACA,OAAO;IACT;IAEF,MAAM,MAAM,aACV,WAIA,QAAQ,aAAA,EACR,QAAQ,YAAA;IAEV,OAAO;AACT;AAaA,eAAsB,SAIpB,QAAA,EACA,MAAA,EACwB;IACxB,IAAI;IACJ,IAAI,OAAO,OAAO,SAAA,KAAc,UAAU;QACxC,YAAY,MAAM,SAAS,YAAA,CAAa,CAAA,WAAA,EAAc,OAAO,SAAS,EAAE;IAC1E,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,SAAA,EAAW,MAAM,GAAG;QAC/D,YAAY,MAAM,SAAS,YAAA,CACzB,CAAA,WAAA,EAAc,OAAO,SAAA,CAAU,IAAI,EAAA;IAEvC,OAAO;QACL,YAAY,OAAO,SAAA;IACrB;IACA,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MAAM,0CAA0C;IAC5D;IACA,OAAQ,MAAM,UAAU;QACtB,SAAS,OAAO,OAAA;QAChB,SAAS,OAAO,OAAA;QAChB,WAAW,OAAO,SAAA,IAAA,CAAA,GAAa,cAAA,UAAA,EAAW;IAC5C,CAAC;AACH;AAEO,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,sCAAA,GAE1C,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC3B,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC;AAC7B,CAAC;AAYM,SAAS,aAGd,OAAA,EACyC;IACzC,OAAO;QAAE,GAAG,OAAA;IAAQ;AACtB;AAMA,SAAS,gBACP,GAAA,EACA,SAAA,EACO;IACP,IAAI;IACJ,IAAI,CAAC,WAAW;QACd,OAAO;IACT,OAAO;QACL,OAAO;IACT;IAEA,MAAM,UAAiB,CAAC,CAAA;IACxB,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,MAAA,EAAQ,KAAK,KAAM;QACzC,QAAQ,IAAA,CACN,IAAI,KAAA,CAAM,GAAG,IAAI,IAAI,EAAE,GAAA,CAAI,CAAC,IAAA,CAAO;gBACjC,GAAG,CAAA;gBACH,YAAY,EAAE,UAAA,IAAA,CAAA,GAAc,cAAA,UAAA,EAAW;YACzC,CAAA,CAAE;IAEN;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/extract.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport JSON5 from 'json5';\nimport { Allow, parse } from 'partial-json';\n\n/**\n * Parses partially complete JSON string.\n */\nexport function parsePartialJson<T = unknown>(jsonString: string): T {\n  return JSON5.parse<T>(JSON.stringify(parse(jsonString, Allow.ALL)));\n}\n\n/**\n * Extracts JSON from string with lenient parsing rules to improve likelihood of successful extraction.\n */\nexport function extractJson<T = unknown>(\n  text: string,\n  throwOnBadJson?: true\n): T;\nexport function extractJson<T = unknown>(\n  text: string,\n  throwOnBadJson?: false\n): T | null;\nexport function extractJson<T = unknown>(\n  text: string,\n  throwOnBadJson?: boolean\n): T | null {\n  let openingChar: '{' | '[' | undefined;\n  let closingChar: '}' | ']' | undefined;\n  let startPos: number | undefined;\n  let nestingCount = 0;\n  let inString = false;\n  let escapeNext = false;\n\n  for (let i = 0; i < text.length; i++) {\n    const char = text[i].replace(/\\u00A0/g, ' ');\n\n    if (escapeNext) {\n      escapeNext = false;\n      continue;\n    }\n\n    if (char === '\\\\') {\n      escapeNext = true;\n      continue;\n    }\n\n    if (char === '\"') {\n      inString = !inString;\n      continue;\n    }\n\n    if (inString) {\n      continue;\n    }\n\n    if (!openingChar && (char === '{' || char === '[')) {\n      // Look for opening character\n      openingChar = char;\n      closingChar = char === '{' ? '}' : ']';\n      startPos = i;\n      nestingCount++;\n    } else if (char === openingChar) {\n      // Increment nesting for matching opening character\n      nestingCount++;\n    } else if (char === closingChar) {\n      // Decrement nesting for matching closing character\n      nestingCount--;\n      if (!nestingCount) {\n        // Reached end of target element\n        return JSON5.parse(text.substring(startPos || 0, i + 1)) as T;\n      }\n    }\n  }\n\n  if (startPos !== undefined && nestingCount > 0) {\n    // If an incomplete JSON structure is detected\n    try {\n      // Parse the incomplete JSON structure using partial-json for lenient parsing\n      return parsePartialJson<T>(text.substring(startPos));\n    } catch {\n      // If parsing fails, throw an error\n      if (throwOnBadJson) {\n        throw new Error(`Invalid JSON extracted from model output: ${text}`);\n      }\n      return null;\n    }\n  }\n  if (throwOnBadJson) {\n    throw new Error(`Invalid JSON extracted from model output: ${text}`);\n  }\n  return null;\n}\n\ninterface ExtractItemsResult {\n  items: unknown[];\n  cursor: number;\n}\n\n/**\n * Extracts complete objects from the first array found in the text.\n * Processes text from the cursor position and returns both complete items\n * and the new cursor position.\n */\nexport function extractItems(text: string, cursor = 0): ExtractItemsResult {\n  const items: unknown[] = [];\n  let currentCursor = cursor;\n\n  // Find the first array start if we haven't already processed any text\n  if (cursor === 0) {\n    const arrayStart = text.indexOf('[');\n    if (arrayStart === -1) {\n      return { items: [], cursor: text.length };\n    }\n    currentCursor = arrayStart + 1;\n  }\n\n  let objectStart = -1;\n  let braceCount = 0;\n  let inString = false;\n  let escapeNext = false;\n\n  // Process the text from the cursor position\n  for (let i = currentCursor; i < text.length; i++) {\n    const char = text[i];\n\n    if (escapeNext) {\n      escapeNext = false;\n      continue;\n    }\n\n    if (char === '\\\\') {\n      escapeNext = true;\n      continue;\n    }\n\n    if (char === '\"') {\n      inString = !inString;\n      continue;\n    }\n\n    if (inString) {\n      continue;\n    }\n\n    if (char === '{') {\n      if (braceCount === 0) {\n        objectStart = i;\n      }\n      braceCount++;\n    } else if (char === '}') {\n      braceCount--;\n      if (braceCount === 0 && objectStart !== -1) {\n        try {\n          const obj = JSON5.parse(text.substring(objectStart, i + 1));\n          items.push(obj);\n          currentCursor = i + 1;\n          objectStart = -1;\n        } catch {\n          // If parsing fails, continue\n        }\n      }\n    } else if (char === ']' && braceCount === 0) {\n      // End of array\n      break;\n    }\n  }\n\n  return {\n    items,\n    cursor: currentCursor,\n  };\n}\n"], "names": ["JSON5"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,cAAA,IAAA;IAAA,aAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,eAAkB;AAClB,IAAA,sBAA6B;AAKtB,SAAS,iBAA8B,UAAA,EAAuB;IACnE,OAAO,aAAAA,OAAAA,CAAM,KAAA,CAAS,KAAK,SAAA,CAAA,CAAA,GAAU,oBAAA,KAAA,EAAM,YAAY,oBAAA,KAAA,CAAM,GAAG,CAAC,CAAC;AACpE;AAaO,SAAS,YACd,IAAA,EACA,cAAA,EACU;IACV,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe;IACnB,IAAI,WAAW;IACf,IAAI,aAAa;IAEjB,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,IAAK;QACpC,MAAM,OAAO,IAAA,CAAK,CAAC,CAAA,CAAE,OAAA,CAAQ,WAAW,GAAG;QAE3C,IAAI,YAAY;YACd,aAAa;YACb;QACF;QAEA,IAAI,SAAS,MAAM;YACjB,aAAa;YACb;QACF;QAEA,IAAI,SAAS,KAAK;YAChB,WAAW,CAAC;YACZ;QACF;QAEA,IAAI,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,eAAA,CAAgB,SAAS,OAAO,SAAS,GAAA,GAAM;YAElD,cAAc;YACd,cAAc,SAAS,MAAM,MAAM;YACnC,WAAW;YACX;QACF,OAAA,IAAW,SAAS,aAAa;YAE/B;QACF,OAAA,IAAW,SAAS,aAAa;YAE/B;YACA,IAAI,CAAC,cAAc;gBAEjB,OAAO,aAAAA,OAAAA,CAAM,KAAA,CAAM,KAAK,SAAA,CAAU,YAAY,GAAG,IAAI,CAAC,CAAC;YACzD;QACF;IACF;IAEA,IAAI,aAAa,KAAA,KAAa,eAAe,GAAG;QAE9C,IAAI;YAEF,OAAO,iBAAoB,KAAK,SAAA,CAAU,QAAQ,CAAC;QACrD,EAAA,OAAQ;YAEN,IAAI,gBAAgB;gBAClB,MAAM,IAAI,MAAM,CAAA,0CAAA,EAA6C,IAAI,EAAE;YACrE;YACA,OAAO;QACT;IACF;IACA,IAAI,gBAAgB;QAClB,MAAM,IAAI,MAAM,CAAA,0CAAA,EAA6C,IAAI,EAAE;IACrE;IACA,OAAO;AACT;AAYO,SAAS,aAAa,IAAA,EAAc,SAAS,CAAA,EAAuB;IACzE,MAAM,QAAmB,CAAC,CAAA;IAC1B,IAAI,gBAAgB;IAGpB,IAAI,WAAW,GAAG;QAChB,MAAM,aAAa,KAAK,OAAA,CAAQ,GAAG;QACnC,IAAI,eAAe,CAAA,GAAI;YACrB,OAAO;gBAAE,OAAO,CAAC,CAAA;gBAAG,QAAQ,KAAK,MAAA;YAAO;QAC1C;QACA,gBAAgB,aAAa;IAC/B;IAEA,IAAI,cAAc,CAAA;IAClB,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI,aAAa;IAGjB,IAAA,IAAS,IAAI,eAAe,IAAI,KAAK,MAAA,EAAQ,IAAK;QAChD,MAAM,OAAO,IAAA,CAAK,CAAC,CAAA;QAEnB,IAAI,YAAY;YACd,aAAa;YACb;QACF;QAEA,IAAI,SAAS,MAAM;YACjB,aAAa;YACb;QACF;QAEA,IAAI,SAAS,KAAK;YAChB,WAAW,CAAC;YACZ;QACF;QAEA,IAAI,UAAU;YACZ;QACF;QAEA,IAAI,SAAS,KAAK;YAChB,IAAI,eAAe,GAAG;gBACpB,cAAc;YAChB;YACA;QACF,OAAA,IAAW,SAAS,KAAK;YACvB;YACA,IAAI,eAAe,KAAK,gBAAgB,CAAA,GAAI;gBAC1C,IAAI;oBACF,MAAM,MAAM,aAAAA,OAAAA,CAAM,KAAA,CAAM,KAAK,SAAA,CAAU,aAAa,IAAI,CAAC,CAAC;oBAC1D,MAAM,IAAA,CAAK,GAAG;oBACd,gBAAgB,IAAI;oBACpB,cAAc,CAAA;gBAChB,EAAA,OAAQ,CAER;YACF;QACF,OAAA,IAAW,SAAS,OAAO,eAAe,GAAG;YAE3C;QACF;IACF;IAEA,OAAO;QACL;QACA,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/array.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError } from '@genkit-ai/core';\nimport { extractItems } from '../extract';\nimport type { Formatter } from './types';\n\nexport const arrayFormatter: Formatter<unknown[], unknown[]> = {\n  name: 'array',\n  config: {\n    contentType: 'application/json',\n    constrained: true,\n  },\n  handler: (schema) => {\n    if (schema && schema.type !== 'array') {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `Must supply an 'array' schema type when using the 'items' parser format.`,\n      });\n    }\n\n    let instructions: string | undefined;\n    if (schema) {\n      instructions = `Output should be a JSON array conforming to the following schema:\n    \n\\`\\`\\`\n${JSON.stringify(schema)}\n\\`\\`\\`\n    `;\n    }\n\n    return {\n      parseChunk: (chunk) => {\n        // first, determine the cursor position from the previous chunks\n        const cursor = chunk.previousChunks?.length\n          ? extractItems(chunk.previousText).cursor\n          : 0;\n        // then, extract the items starting at that cursor\n        const { items } = extractItems(chunk.accumulatedText, cursor);\n\n        return items;\n      },\n\n      parseMessage: (message) => {\n        const { items } = extractItems(message.text, 0);\n        return items;\n      },\n\n      instructions,\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA4B;AAC5B,IAAA,iBAA6B;AAGtB,MAAM,iBAAkD;IAC7D,MAAM;IACN,QAAQ;QACN,aAAa;QACb,aAAa;IACf;IACA,SAAS,CAAC,WAAW;QACnB,IAAI,UAAU,OAAO,IAAA,KAAS,SAAS;YACrC,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,wEAAA,CAAA;YACX,CAAC;QACH;QAEA,IAAI;QACJ,IAAI,QAAQ;YACV,eAAe,CAAA;;;AAAA,EAGnB,KAAK,SAAA,CAAU,MAAM,CAAC,CAAA;;IAAA,CAAA;QAGpB;QAEA,OAAO;YACL,YAAY,CAAC,UAAU;gBAErB,MAAM,SAAS,MAAM,cAAA,EAAgB,SAAA,CAAA,GACjC,eAAA,YAAA,EAAa,MAAM,YAAY,EAAE,MAAA,GACjC;gBAEJ,MAAM,EAAE,KAAA,CAAM,CAAA,GAAA,CAAA,GAAI,eAAA,YAAA,EAAa,MAAM,eAAA,EAAiB,MAAM;gBAE5D,OAAO;YACT;YAEA,cAAc,CAAC,YAAY;gBACzB,MAAM,EAAE,KAAA,CAAM,CAAA,GAAA,CAAA,GAAI,eAAA,YAAA,EAAa,QAAQ,IAAA,EAAM,CAAC;gBAC9C,OAAO;YACT;YAEA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/enum.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError } from '@genkit-ai/core';\nimport type { Formatter } from './types';\n\nexport const enumFormatter: Formatter<string, string> = {\n  name: 'enum',\n  config: {\n    contentType: 'text/enum',\n    constrained: true,\n  },\n  handler: (schema) => {\n    if (schema && schema.type !== 'string' && schema.type !== 'enum') {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `Must supply a 'string' or 'enum' schema type when using the enum parser format.`,\n      });\n    }\n\n    let instructions: string | undefined;\n    if (schema?.enum) {\n      instructions = `Output should be ONLY one of the following enum values. Do not output any additional information or add quotes.\\n\\n${schema.enum.map((v) => v.toString()).join('\\n')}`;\n    }\n\n    return {\n      parseMessage: (message) => {\n        return message.text.replace(/['\"]/g, '').trim();\n      },\n      instructions,\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,eAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA4B;AAGrB,MAAM,gBAA2C;IACtD,MAAM;IACN,QAAQ;QACN,aAAa;QACb,aAAa;IACf;IACA,SAAS,CAAC,WAAW;QACnB,IAAI,UAAU,OAAO,IAAA,KAAS,YAAY,OAAO,IAAA,KAAS,QAAQ;YAChE,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,+EAAA,CAAA;YACX,CAAC;QACH;QAEA,IAAI;QACJ,IAAI,QAAQ,MAAM;YAChB,eAAe,CAAA;;AAAA,EAAsH,OAAO,IAAA,CAAK,GAAA,CAAI,CAAC,IAAM,EAAE,QAAA,CAAS,CAAC,EAAE,IAAA,CAAK,IAAI,CAAC,EAAA;QACtL;QAEA,OAAO;YACL,cAAc,CAAC,YAAY;gBACzB,OAAO,QAAQ,IAAA,CAAK,OAAA,CAAQ,SAAS,EAAE,EAAE,IAAA,CAAK;YAChD;YACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/json.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { extractJson } from '../extract';\nimport type { Formatter } from './types';\n\nexport const jsonFormatter: Formatter<unknown, unknown> = {\n  name: 'json',\n  config: {\n    format: 'json',\n    contentType: 'application/json',\n    constrained: true,\n    defaultInstructions: false,\n  },\n  handler: (schema) => {\n    let instructions: string | undefined;\n\n    if (schema) {\n      instructions = `Output should be in JSON format and conform to the following schema:\n\n\\`\\`\\`\n${JSON.stringify(schema)}\n\\`\\`\\`\n`;\n    }\n    return {\n      parseChunk: (chunk) => {\n        return extractJson(chunk.accumulatedText);\n      },\n\n      parseMessage: (message) => {\n        return extractJson(message.text);\n      },\n\n      instructions,\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,eAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,iBAA4B;AAGrB,MAAM,gBAA6C;IACxD,MAAM;IACN,QAAQ;QACN,QAAQ;QACR,aAAa;QACb,aAAa;QACb,qBAAqB;IACvB;IACA,SAAS,CAAC,WAAW;QACnB,IAAI;QAEJ,IAAI,QAAQ;YACV,eAAe,CAAA;;;AAAA,EAGnB,KAAK,SAAA,CAAU,MAAM,CAAC,CAAA;;AAAA,CAAA;QAGpB;QACA,OAAO;YACL,YAAY,CAAC,UAAU;gBACrB,OAAA,CAAA,GAAO,eAAA,WAAA,EAAY,MAAM,eAAe;YAC1C;YAEA,cAAc,CAAC,YAAY;gBACzB,OAAA,CAAA,GAAO,eAAA,WAAA,EAAY,QAAQ,IAAI;YACjC;YAEA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/jsonl.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError } from '@genkit-ai/core';\nimport JSON5 from 'json5';\nimport { extractJson } from '../extract';\nimport type { Formatter } from './types';\n\nfunction objectLines(text: string): string[] {\n  return text\n    .split('\\n')\n    .map((line) => line.trim())\n    .filter((line) => line.startsWith('{'));\n}\n\nexport const jsonlFormatter: Formatter<unknown[], unknown[]> = {\n  name: 'jsonl',\n  config: {\n    contentType: 'application/jsonl',\n  },\n  handler: (schema) => {\n    if (\n      schema &&\n      (schema.type !== 'array' || schema.items?.type !== 'object')\n    ) {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `Must supply an 'array' schema type containing 'object' items when using the 'jsonl' parser format.`,\n      });\n    }\n\n    let instructions: string | undefined;\n    if (schema?.items) {\n      instructions = `Output should be JSONL format, a sequence of JSON objects (one per line) separated by a newline \\`\\\\n\\` character. Each line should be a JSON object conforming to the following schema:\n\n\\`\\`\\`\n${JSON.stringify(schema.items)}\n\\`\\`\\`\n    `;\n    }\n\n    return {\n      parseChunk: (chunk) => {\n        const results: unknown[] = [];\n\n        const text = chunk.accumulatedText;\n\n        let startIndex = 0;\n        if (chunk.previousChunks?.length) {\n          const lastNewline = chunk.previousText.lastIndexOf('\\n');\n          if (lastNewline !== -1) {\n            startIndex = lastNewline + 1;\n          }\n        }\n\n        const lines = text.slice(startIndex).split('\\n');\n\n        for (const line of lines) {\n          const trimmed = line.trim();\n          if (trimmed.startsWith('{')) {\n            try {\n              const result = JSON5.parse(trimmed);\n              if (result) {\n                results.push(result);\n              }\n            } catch (e) {\n              break;\n            }\n          }\n        }\n\n        return results;\n      },\n\n      parseMessage: (message) => {\n        const items = objectLines(message.text)\n          .map((l) => extractJson(l))\n          .filter((l) => !!l);\n\n        return items;\n      },\n\n      instructions,\n    };\n  },\n};\n"], "names": ["JSON5"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA4B;AAC5B,IAAA,eAAkB;AAClB,IAAA,iBAA4B;AAG5B,SAAS,YAAY,IAAA,EAAwB;IAC3C,OAAO,KACJ,KAAA,CAAM,IAAI,EACV,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,CAAK,CAAC,EACzB,MAAA,CAAO,CAAC,OAAS,KAAK,UAAA,CAAW,GAAG,CAAC;AAC1C;AAEO,MAAM,iBAAkD;IAC7D,MAAM;IACN,QAAQ;QACN,aAAa;IACf;IACA,SAAS,CAAC,WAAW;QACnB,IACE,UAAA,CACC,OAAO,IAAA,KAAS,WAAW,OAAO,KAAA,EAAO,SAAS,QAAA,GACnD;YACA,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,kGAAA,CAAA;YACX,CAAC;QACH;QAEA,IAAI;QACJ,IAAI,QAAQ,OAAO;YACjB,eAAe,CAAA;;;AAAA,EAGnB,KAAK,SAAA,CAAU,OAAO,KAAK,CAAC,CAAA;;IAAA,CAAA;QAG1B;QAEA,OAAO;YACL,YAAY,CAAC,UAAU;gBACrB,MAAM,UAAqB,CAAC,CAAA;gBAE5B,MAAM,OAAO,MAAM,eAAA;gBAEnB,IAAI,aAAa;gBACjB,IAAI,MAAM,cAAA,EAAgB,QAAQ;oBAChC,MAAM,cAAc,MAAM,YAAA,CAAa,WAAA,CAAY,IAAI;oBACvD,IAAI,gBAAgB,CAAA,GAAI;wBACtB,aAAa,cAAc;oBAC7B;gBACF;gBAEA,MAAM,QAAQ,KAAK,KAAA,CAAM,UAAU,EAAE,KAAA,CAAM,IAAI;gBAE/C,KAAA,MAAW,QAAQ,MAAO;oBACxB,MAAM,UAAU,KAAK,IAAA,CAAK;oBAC1B,IAAI,QAAQ,UAAA,CAAW,GAAG,GAAG;wBAC3B,IAAI;4BACF,MAAM,SAAS,aAAAA,OAAAA,CAAM,KAAA,CAAM,OAAO;4BAClC,IAAI,QAAQ;gCACV,QAAQ,IAAA,CAAK,MAAM;4BACrB;wBACF,EAAA,OAAS,GAAG;4BACV;wBACF;oBACF;gBACF;gBAEA,OAAO;YACT;YAEA,cAAc,CAAC,YAAY;gBACzB,MAAM,QAAQ,YAAY,QAAQ,IAAI,EACnC,GAAA,CAAI,CAAC,IAAA,CAAA,GAAM,eAAA,WAAA,EAAY,CAAC,CAAC,EACzB,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC;gBAEpB,OAAO;YACT;YAEA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/text.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Formatter } from './types';\n\nexport const textFormatter: Formatter<string, string> = {\n  name: 'text',\n  config: {\n    contentType: 'text/plain',\n  },\n  handler: () => {\n    return {\n      parseChunk: (chunk) => {\n        return chunk.text;\n      },\n\n      parseMessage: (message) => {\n        return message.text;\n      },\n    };\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,eAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAkBO,MAAM,gBAA2C;IACtD,MAAM;IACN,QAAQ;QACN,aAAa;IACf;IACA,SAAS,MAAM;QACb,OAAO;YACL,YAAY,CAAC,UAAU;gBACrB,OAAO,MAAM,IAAA;YACf;YAEA,cAAc,CAAC,YAAY;gBACzB,OAAO,QAAQ,IAAA;YACjB;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/formats/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { JSONSchema } from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport type { OutputOptions } from '../generate.js';\nimport type { MessageData, TextPart } from '../model.js';\nimport { arrayFormatter } from './array.js';\nimport { enumFormatter } from './enum.js';\nimport { jsonFormatter } from './json.js';\nimport { jsonlFormatter } from './jsonl.js';\nimport { textFormatter } from './text.js';\nimport type { Formatter } from './types.js';\n\nexport type { Formatter };\n\nexport function defineFormat(\n  registry: Registry,\n  options: { name: string } & Formatter['config'],\n  handler: Formatter['handler']\n): { config: Formatter['config']; handler: Formatter['handler'] } {\n  const { name, ...config } = options;\n  const formatter = { config, handler };\n  registry.registerValue('format', name, formatter);\n  return formatter;\n}\n\nexport type FormatArgument =\n  | keyof typeof DEFAULT_FORMATS\n  | Omit<string, keyof typeof DEFAULT_FORMATS>\n  | undefined\n  | null;\n\nexport async function resolveFormat(\n  registry: Registry,\n  outputOpts: OutputOptions | undefined\n): Promise<Formatter<any, any> | undefined> {\n  if (!outputOpts) return undefined;\n  // If schema is set but no explicit format is set we default to json.\n  if ((outputOpts.jsonSchema || outputOpts.schema) && !outputOpts.format) {\n    return registry.lookupValue<Formatter>('format', 'json');\n  }\n  if (outputOpts.format) {\n    return registry.lookupValue<Formatter>('format', outputOpts.format);\n  }\n  return undefined;\n}\n\nexport function resolveInstructions(\n  format?: Formatter,\n  schema?: JSONSchema,\n  instructionsOption?: boolean | string\n): string | undefined {\n  if (typeof instructionsOption === 'string') return instructionsOption; // user provided instructions\n  if (instructionsOption === false) return undefined; // user says no instructions\n  if (!format) return undefined;\n  return format.handler(schema).instructions;\n}\n\nexport function injectInstructions(\n  messages: MessageData[],\n  instructions: string | false | undefined\n): MessageData[] {\n  if (!instructions) return messages;\n\n  // bail out if a non-pending output part is already present\n  if (\n    messages.find((m) =>\n      m.content.find(\n        (p) => p.metadata?.purpose === 'output' && !p.metadata?.pending\n      )\n    )\n  ) {\n    return messages;\n  }\n\n  const newPart: TextPart = {\n    text: instructions,\n    metadata: { purpose: 'output' },\n  };\n\n  // find the system message or the last user message\n  let targetIndex = messages.findIndex((m) => m.role === 'system');\n  if (targetIndex < 0)\n    targetIndex = messages.map((m) => m.role).lastIndexOf('user');\n  if (targetIndex < 0) return messages;\n\n  const m = {\n    ...messages[targetIndex],\n    content: [...messages[targetIndex].content],\n  };\n\n  const partIndex = m.content.findIndex(\n    (p) => p.metadata?.purpose === 'output' && p.metadata?.pending\n  );\n  if (partIndex > 0) {\n    m.content.splice(partIndex, 1, newPart);\n  } else {\n    m.content.push(newPart);\n  }\n\n  const outMessages = [...messages];\n  outMessages.splice(targetIndex, 1, m);\n  return outMessages;\n}\n\nexport const DEFAULT_FORMATS: Formatter<any, any>[] = [\n  jsonFormatter,\n  arrayFormatter,\n  textFormatter,\n  enumFormatter,\n  jsonlFormatter,\n];\n\n/**\n * configureFormats registers the default built-in formats on a registry.\n */\nexport function configureFormats(registry: Registry) {\n  for (const format of DEFAULT_FORMATS) {\n    defineFormat(\n      registry,\n      { name: format.name, ...format.config },\n      format.handler\n    );\n  }\n}\n"], "names": ["m"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,iBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,cAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,qBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAoBA,IAAA,eAA+B;AAC/B,IAAA,cAA8B;AAC9B,IAAA,cAA8B;AAC9B,IAAA,eAA+B;AAC/B,IAAA,cAA8B;AAKvB,SAAS,aACd,QAAA,EACA,OAAA,EACA,OAAA,EACgE;IAChE,MAAM,EAAE,IAAA,EAAM,GAAG,OAAO,CAAA,GAAI;IAC5B,MAAM,YAAY;QAAE;QAAQ;IAAQ;IACpC,SAAS,aAAA,CAAc,UAAU,MAAM,SAAS;IAChD,OAAO;AACT;AAQA,eAAsB,cACpB,QAAA,EACA,UAAA,EAC0C;IAC1C,IAAI,CAAC,WAAY,CAAA,OAAO,KAAA;IAExB,IAAA,CAAK,WAAW,UAAA,IAAc,WAAW,MAAA,KAAW,CAAC,WAAW,MAAA,EAAQ;QACtE,OAAO,SAAS,WAAA,CAAuB,UAAU,MAAM;IACzD;IACA,IAAI,WAAW,MAAA,EAAQ;QACrB,OAAO,SAAS,WAAA,CAAuB,UAAU,WAAW,MAAM;IACpE;IACA,OAAO,KAAA;AACT;AAEO,SAAS,oBACd,MAAA,EACA,MAAA,EACA,kBAAA,EACoB;IACpB,IAAI,OAAO,uBAAuB,SAAU,CAAA,OAAO;IACnD,IAAI,uBAAuB,MAAO,CAAA,OAAO,KAAA;IACzC,IAAI,CAAC,OAAQ,CAAA,OAAO,KAAA;IACpB,OAAO,OAAO,OAAA,CAAQ,MAAM,EAAE,YAAA;AAChC;AAEO,SAAS,mBACd,QAAA,EACA,YAAA,EACe;IACf,IAAI,CAAC,aAAc,CAAA,OAAO;IAG1B,IACE,SAAS,IAAA,CAAK,CAACA,KACbA,GAAE,OAAA,CAAQ,IAAA,CACR,CAAC,IAAM,EAAE,QAAA,EAAU,YAAY,YAAY,CAAC,EAAE,QAAA,EAAU,WAG5D;QACA,OAAO;IACT;IAEA,MAAM,UAAoB;QACxB,MAAM;QACN,UAAU;YAAE,SAAS;QAAS;IAChC;IAGA,IAAI,cAAc,SAAS,SAAA,CAAU,CAACA,KAAMA,GAAE,IAAA,KAAS,QAAQ;IAC/D,IAAI,cAAc,GAChB,cAAc,SAAS,GAAA,CAAI,CAACA,KAAMA,GAAE,IAAI,EAAE,WAAA,CAAY,MAAM;IAC9D,IAAI,cAAc,EAAG,CAAA,OAAO;IAE5B,MAAM,IAAI;QACR,GAAG,QAAA,CAAS,WAAW,CAAA;QACvB,SAAS,CAAC;eAAG,QAAA,CAAS,WAAW,CAAA,CAAE,OAAO;SAAA;IAC5C;IAEA,MAAM,YAAY,EAAE,OAAA,CAAQ,SAAA,CAC1B,CAAC,IAAM,EAAE,QAAA,EAAU,YAAY,YAAY,EAAE,QAAA,EAAU;IAEzD,IAAI,YAAY,GAAG;QACjB,EAAE,OAAA,CAAQ,MAAA,CAAO,WAAW,GAAG,OAAO;IACxC,OAAO;QACL,EAAE,OAAA,CAAQ,IAAA,CAAK,OAAO;IACxB;IAEA,MAAM,cAAc,CAAC;WAAG,QAAQ;KAAA;IAChC,YAAY,MAAA,CAAO,aAAa,GAAG,CAAC;IACpC,OAAO;AACT;AAEO,MAAM,kBAAyC;IACpD,YAAA,aAAA;IACA,aAAA,cAAA;IACA,YAAA,aAAA;IACA,YAAA,aAAA;IACA,aAAA,cAAA;CACF;AAKO,SAAS,iBAAiB,QAAA,EAAoB;IACnD,KAAA,MAAW,UAAU,gBAAiB;QACpC,aACE,UACA;YAAE,MAAM,OAAO,IAAA;YAAM,GAAG,OAAO,MAAA;QAAO,GACtC,OAAO,OAAA;IAEX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/model-types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OperationSchema, z } from '@genkit-ai/core';\nimport {\n  CustomPartSchema,\n  DataPartSchema,\n  DocumentDataSchema,\n  MediaPartSchema,\n  ReasoningPartSchema,\n  ResourcePartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n} from './document.js';\n\n//\n// IMPORTANT: Please keep type definitions in sync with\n//   genkit-tools/src/types/model.ts\n//\n\n/**\n * Zod schema of message part.\n */\nexport const PartSchema = z.union([\n  TextPartSchema,\n  MediaPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  DataPartSchema,\n  CustomPartSchema,\n  ReasoningPartSchema,\n  ResourcePartSchema,\n]);\n\n/**\n * Message part.\n */\nexport type Part = z.infer<typeof PartSchema>;\n\n/**\n * Zod schema of a message role.\n */\nexport const RoleSchema = z.enum(['system', 'user', 'model', 'tool']);\n\n/**\n * Message role.\n */\nexport type Role = z.infer<typeof RoleSchema>;\n\n/**\n * Zod schema of a message.\n */\nexport const MessageSchema = z.object({\n  role: RoleSchema,\n  content: z.array(PartSchema),\n  metadata: z.record(z.unknown()).optional(),\n});\n\n/**\n * Model message data.\n */\nexport type MessageData = z.infer<typeof MessageSchema>;\n\n/**\n * Zod schema of model info metadata.\n */\nexport const ModelInfoSchema = z.object({\n  /** Acceptable names for this model (e.g. different versions). */\n  versions: z.array(z.string()).optional(),\n  /** Friendly label for this model (e.g. \"Google AI - Gemini Pro\") */\n  label: z.string().optional(),\n  /** Model Specific configuration. */\n  configSchema: z.record(z.any()).optional(),\n  /** Supported model capabilities. */\n  supports: z\n    .object({\n      /** Model can process historical messages passed with a prompt. */\n      multiturn: z.boolean().optional(),\n      /** Model can process media as part of the prompt (multimodal input). */\n      media: z.boolean().optional(),\n      /** Model can perform tool calls. */\n      tools: z.boolean().optional(),\n      /** Model can accept messages with role \"system\". */\n      systemRole: z.boolean().optional(),\n      /** Model can output this type of data. */\n      output: z.array(z.string()).optional(),\n      /** Model supports output in these content types. */\n      contentType: z.array(z.string()).optional(),\n      /** Model can natively support document-based context grounding. */\n      context: z.boolean().optional(),\n      /** Model can natively support constrained generation. */\n      constrained: z.enum(['none', 'all', 'no-tools']).optional(),\n      /** Model supports controlling tool choice, e.g. forced tool calling. */\n      toolChoice: z.boolean().optional(),\n    })\n    .optional(),\n  /** At which stage of development this model is.\n   * - `featured` models are recommended for general use.\n   * - `stable` models are well-tested and reliable.\n   * - `unstable` models are experimental and may change.\n   * - `legacy` models are no longer recommended for new projects.\n   * - `deprecated` models are deprecated by the provider and may be removed in future versions.\n   */\n  stage: z\n    .enum(['featured', 'stable', 'unstable', 'legacy', 'deprecated'])\n    .optional(),\n});\n\n/**\n * Model info metadata.\n */\nexport type ModelInfo = z.infer<typeof ModelInfoSchema>;\n\n/**\n * Zod schema of a tool definition.\n */\nexport const ToolDefinitionSchema = z.object({\n  name: z.string(),\n  description: z.string(),\n  inputSchema: z\n    .record(z.any())\n    .describe('Valid JSON Schema representing the input of the tool.')\n    .nullish(),\n  outputSchema: z\n    .record(z.any())\n    .describe('Valid JSON Schema describing the output of the tool.')\n    .nullish(),\n  metadata: z\n    .record(z.any())\n    .describe('additional metadata for this tool definition')\n    .optional(),\n});\n\n/**\n * Tool definition.\n */\nexport type ToolDefinition = z.infer<typeof ToolDefinitionSchema>;\n\n/**\n * Configuration parameter descriptions.\n */\nexport const GenerationCommonConfigDescriptions = {\n  temperature:\n    'Controls the degree of randomness in token selection. A lower value is ' +\n    'good for a more predictable response. A higher value leads to more ' +\n    'diverse or unexpected results.',\n  maxOutputTokens: 'The maximum number of tokens to include in the response.',\n  topK: 'The maximum number of tokens to consider when sampling.',\n  topP:\n    'Decides how many possible words to consider. A higher value means ' +\n    'that the model looks at more possible words, even the less likely ' +\n    'ones, which makes the generated text more diverse.',\n};\n\n/**\n * Zod schema of a common config object.\n */\nexport const GenerationCommonConfigSchema = z\n  .object({\n    version: z\n      .string()\n      .describe(\n        'A specific version of a model family, e.g. `gemini-2.0-flash` ' +\n          'for the `googleai` family.'\n      )\n      .optional(),\n    temperature: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.temperature)\n      .optional(),\n    maxOutputTokens: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.maxOutputTokens)\n      .optional(),\n    topK: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.topK)\n      .optional(),\n    topP: z\n      .number()\n      .describe(GenerationCommonConfigDescriptions.topP)\n      .optional(),\n    stopSequences: z\n      .array(z.string())\n      .max(5)\n      .describe(\n        'Set of character sequences (up to 5) that will stop output generation.'\n      )\n      .optional(),\n  })\n  .passthrough();\n\n/**\n * Common config object.\n */\nexport type GenerationCommonConfig = typeof GenerationCommonConfigSchema;\n\n/**\n * Zod schema of output config.\n */\nexport const OutputConfigSchema = z.object({\n  format: z.string().optional(),\n  schema: z.record(z.any()).optional(),\n  constrained: z.boolean().optional(),\n  contentType: z.string().optional(),\n});\n\n/**\n * Output config.\n */\nexport type OutputConfig = z.infer<typeof OutputConfigSchema>;\n\n/** ModelRequestSchema represents the parameters that are passed to a model when generating content. */\nexport const ModelRequestSchema = z.object({\n  messages: z.array(MessageSchema),\n  config: z.any().optional(),\n  tools: z.array(ToolDefinitionSchema).optional(),\n  toolChoice: z.enum(['auto', 'required', 'none']).optional(),\n  output: OutputConfigSchema.optional(),\n  docs: z.array(DocumentDataSchema).optional(),\n});\n/** ModelRequest represents the parameters that are passed to a model when generating content. */\nexport interface ModelRequest<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> extends z.infer<typeof ModelRequestSchema> {\n  config?: z.infer<CustomOptionsSchema>;\n}\n/**\n * Zod schema of a generate request.\n */\nexport const GenerateRequestSchema = ModelRequestSchema.extend({\n  /** @deprecated All responses now return a single candidate. This will always be `undefined`. */\n  candidates: z.number().optional(),\n});\n\n/**\n * Generate request data.\n */\nexport type GenerateRequestData = z.infer<typeof GenerateRequestSchema>;\n\n/**\n * Generate request.\n */\nexport interface GenerateRequest<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> extends z.infer<typeof GenerateRequestSchema> {\n  config?: z.infer<CustomOptionsSchema>;\n}\n\n/**\n * Zod schema of usage info from a generate request.\n */\nexport const GenerationUsageSchema = z.object({\n  inputTokens: z.number().optional(),\n  outputTokens: z.number().optional(),\n  totalTokens: z.number().optional(),\n  inputCharacters: z.number().optional(),\n  outputCharacters: z.number().optional(),\n  inputImages: z.number().optional(),\n  outputImages: z.number().optional(),\n  inputVideos: z.number().optional(),\n  outputVideos: z.number().optional(),\n  inputAudioFiles: z.number().optional(),\n  outputAudioFiles: z.number().optional(),\n  custom: z.record(z.number()).optional(),\n  thoughtsTokens: z.number().optional(),\n  cachedContentTokens: z.number().optional(),\n});\n\n/**\n * Usage info from a generate request.\n */\nexport type GenerationUsage = z.infer<typeof GenerationUsageSchema>;\n\n/** Model response finish reason enum. */\nexport const FinishReasonSchema = z.enum([\n  'stop',\n  'length',\n  'blocked',\n  'interrupted',\n  'other',\n  'unknown',\n]);\n\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport const CandidateSchema = z.object({\n  index: z.number(),\n  message: MessageSchema,\n  usage: GenerationUsageSchema.optional(),\n  finishReason: FinishReasonSchema,\n  finishMessage: z.string().optional(),\n  custom: z.unknown(),\n});\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport type CandidateData = z.infer<typeof CandidateSchema>;\n\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport const CandidateErrorSchema = z.object({\n  index: z.number(),\n  code: z.enum(['blocked', 'other', 'unknown']),\n  message: z.string().optional(),\n});\n/** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. */\nexport type CandidateError = z.infer<typeof CandidateErrorSchema>;\n\n/**\n * Zod schema of a model response.\n */\nexport const ModelResponseSchema = z.object({\n  message: MessageSchema.optional(),\n  finishReason: FinishReasonSchema,\n  finishMessage: z.string().optional(),\n  latencyMs: z.number().optional(),\n  usage: GenerationUsageSchema.optional(),\n  /** @deprecated use `raw` instead */\n  custom: z.unknown(),\n  raw: z.unknown(),\n  request: GenerateRequestSchema.optional(),\n  operation: OperationSchema.optional(),\n});\n\n/**\n * Model response data.\n */\nexport type ModelResponseData = z.infer<typeof ModelResponseSchema>;\n\n/**\n * Zod schema of generaete response.\n */\nexport const GenerateResponseSchema = ModelResponseSchema.extend({\n  /** @deprecated All responses now return a single candidate. Only the first candidate will be used if supplied. Return `message`, `finishReason`, and `finishMessage` instead. */\n  candidates: z.array(CandidateSchema).optional(),\n  finishReason: FinishReasonSchema.optional(),\n});\n\n/**\n * Generate response data.\n */\nexport type GenerateResponseData = z.infer<typeof GenerateResponseSchema>;\n\n/** ModelResponseChunkSchema represents a chunk of content to stream to the client. */\nexport const ModelResponseChunkSchema = z.object({\n  role: RoleSchema.optional(),\n  /** index of the message this chunk belongs to. */\n  index: z.number().optional(),\n  /** The chunk of content to stream right now. */\n  content: z.array(PartSchema),\n  /** Model-specific extra information attached to this chunk. */\n  custom: z.unknown().optional(),\n  /** If true, the chunk includes all data from previous chunks. Otherwise, considered to be incremental. */\n  aggregated: z.boolean().optional(),\n});\nexport type ModelResponseChunkData = z.infer<typeof ModelResponseChunkSchema>;\n\nexport const GenerateResponseChunkSchema = ModelResponseChunkSchema;\nexport type GenerateResponseChunkData = z.infer<\n  typeof GenerateResponseChunkSchema\n>;\n\nexport const GenerateActionOutputConfig = z.object({\n  format: z.string().optional(),\n  contentType: z.string().optional(),\n  instructions: z.union([z.boolean(), z.string()]).optional(),\n  jsonSchema: z.any().optional(),\n  constrained: z.boolean().optional(),\n});\n\nexport const GenerateActionOptionsSchema = z.object({\n  /** A model name (e.g. `vertexai/gemini-1.0-pro`). */\n  model: z.string(),\n  /** Retrieved documents to be used as context for this generation. */\n  docs: z.array(DocumentDataSchema).optional(),\n  /** Conversation history for multi-turn prompting when supported by the underlying model. */\n  messages: z.array(MessageSchema),\n  /** List of registered tool names for this generation if supported by the underlying model. */\n  tools: z.array(z.string()).optional(),\n  /** Tool calling mode. `auto` lets the model decide whether to use tools, `required` forces the model to choose a tool, and `none` forces the model not to use any tools. Defaults to `auto`.  */\n  toolChoice: z.enum(['auto', 'required', 'none']).optional(),\n  /** Configuration for the generation request. */\n  config: z.any().optional(),\n  /** Configuration for the desired output of the request. Defaults to the model's default output if unspecified. */\n  output: GenerateActionOutputConfig.optional(),\n  /** Options for resuming an interrupted generation. */\n  resume: z\n    .object({\n      respond: z.array(ToolResponsePartSchema).optional(),\n      restart: z.array(ToolRequestPartSchema).optional(),\n      metadata: z.record(z.any()).optional(),\n    })\n    .optional(),\n  /** When true, return tool calls for manual processing instead of automatically resolving them. */\n  returnToolRequests: z.boolean().optional(),\n  /** Maximum number of tool call iterations that can be performed in a single generate call (default 5). */\n  maxTurns: z.number().optional(),\n});\nexport type GenerateActionOptions = z.infer<typeof GenerateActionOptionsSchema>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,sBAAA,CAAA;AAAA,SAAA,qBAAA;IAAA,sBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,6BAAA,IAAA;IAAA,4BAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,6BAAA,IAAA;IAAA,wBAAA,IAAA;IAAA,oCAAA,IAAA;IAAA,8BAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,0BAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,YAAA,IAAA;IAAA,YAAA,IAAA;IAAA,sBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAAmC;AACnC,IAAA,kBAUO;AAUA,MAAM,aAAa,YAAA,CAAA,CAAE,KAAA,CAAM;IAChC,gBAAA,cAAA;IACA,gBAAA,eAAA;IACA,gBAAA,qBAAA;IACA,gBAAA,sBAAA;IACA,gBAAA,cAAA;IACA,gBAAA,gBAAA;IACA,gBAAA,mBAAA;IACA,gBAAA,kBAAA;CACD;AAUM,MAAM,aAAa,YAAA,CAAA,CAAE,IAAA,CAAK;IAAC;IAAU;IAAQ;IAAS,MAAM;CAAC;AAU7D,MAAM,gBAAgB,YAAA,CAAA,CAAE,MAAA,CAAO;IACpC,MAAM;IACN,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,UAAU;IAC3B,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,CAAC,EAAE,QAAA,CAAS;AAC3C,CAAC;AAUM,MAAM,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,+DAAA,GAEtC,UAAU,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;IAAA,kEAAA,GAEvC,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,kCAAA,GAE3B,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;IAAA,kCAAA,GAEzC,UAAU,YAAA,CAAA,CACP,MAAA,CAAO;QAAA,gEAAA,GAEN,WAAW,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;QAAA,sEAAA,GAEhC,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;QAAA,kCAAA,GAE5B,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;QAAA,kDAAA,GAE5B,YAAY,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;QAAA,wCAAA,GAEjC,QAAQ,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;QAAA,kDAAA,GAErC,aAAa,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;QAAA,iEAAA,GAE1C,SAAS,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;QAAA,uDAAA,GAE9B,aAAa,YAAA,CAAA,CAAE,IAAA,CAAK;YAAC;YAAQ;YAAO,UAAU;SAAC,EAAE,QAAA,CAAS;QAAA,sEAAA,GAE1D,YAAY,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IACnC,CAAC,EACA,QAAA,CAAS;IAAA;;;;;;GAAA,GAQZ,OAAO,YAAA,CAAA,CACJ,IAAA,CAAK;QAAC;QAAY;QAAU;QAAY;QAAU,YAAY;KAAC,EAC/D,QAAA,CAAS;AACd,CAAC;AAUM,MAAM,uBAAuB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC3C,MAAM,YAAA,CAAA,CAAE,MAAA,CAAO;IACf,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO;IACtB,aAAa,YAAA,CAAA,CACV,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EACd,QAAA,CAAS,uDAAuD,EAChE,OAAA,CAAQ;IACX,cAAc,YAAA,CAAA,CACX,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EACd,QAAA,CAAS,sDAAsD,EAC/D,OAAA,CAAQ;IACX,UAAU,YAAA,CAAA,CACP,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EACd,QAAA,CAAS,8CAA8C,EACvD,QAAA,CAAS;AACd,CAAC;AAUM,MAAM,qCAAqC;IAChD,aACE;IAGF,iBAAiB;IACjB,MAAM;IACN,MACE;AAGJ;AAKO,MAAM,+BAA+B,YAAA,CAAA,CACzC,MAAA,CAAO;IACN,SAAS,YAAA,CAAA,CACN,MAAA,CAAO,EACP,QAAA,CACC,4FAGD,QAAA,CAAS;IACZ,aAAa,YAAA,CAAA,CACV,MAAA,CAAO,EACP,QAAA,CAAS,mCAAmC,WAAW,EACvD,QAAA,CAAS;IACZ,iBAAiB,YAAA,CAAA,CACd,MAAA,CAAO,EACP,QAAA,CAAS,mCAAmC,eAAe,EAC3D,QAAA,CAAS;IACZ,MAAM,YAAA,CAAA,CACH,MAAA,CAAO,EACP,QAAA,CAAS,mCAAmC,IAAI,EAChD,QAAA,CAAS;IACZ,MAAM,YAAA,CAAA,CACH,MAAA,CAAO,EACP,QAAA,CAAS,mCAAmC,IAAI,EAChD,QAAA,CAAS;IACZ,eAAe,YAAA,CAAA,CACZ,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAChB,GAAA,CAAI,CAAC,EACL,QAAA,CACC,0EAED,QAAA,CAAS;AACd,CAAC,EACA,WAAA,CAAY;AAUR,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IACzC,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC5B,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;IACnC,aAAa,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAClC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AACnC,CAAC;AAQM,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IACzC,UAAU,YAAA,CAAA,CAAE,KAAA,CAAM,aAAa;IAC/B,QAAQ,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACzB,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM,oBAAoB,EAAE,QAAA,CAAS;IAC9C,YAAY,YAAA,CAAA,CAAE,IAAA,CAAK;QAAC;QAAQ;QAAY,MAAM;KAAC,EAAE,QAAA,CAAS;IAC1D,QAAQ,mBAAmB,QAAA,CAAS;IACpC,MAAM,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,kBAAkB,EAAE,QAAA,CAAS;AAC7C,CAAC;AAUM,MAAM,wBAAwB,mBAAmB,MAAA,CAAO;IAAA,8FAAA,GAE7D,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAClC,CAAC;AAmBM,MAAM,wBAAwB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC5C,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAClC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,iBAAiB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACrC,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACtC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAClC,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,cAAc,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAClC,iBAAiB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACrC,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACtC,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;IACtC,gBAAgB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACpC,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC3C,CAAC;AAQM,MAAM,qBAAqB,YAAA,CAAA,CAAE,IAAA,CAAK;IACvC;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO;IACtC,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO;IAChB,SAAS;IACT,OAAO,sBAAsB,QAAA,CAAS;IACtC,cAAc;IACd,eAAe,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACnC,QAAQ,YAAA,CAAA,CAAE,OAAA,CAAQ;AACpB,CAAC;AAKM,MAAM,uBAAuB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC3C,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO;IAChB,MAAM,YAAA,CAAA,CAAE,IAAA,CAAK;QAAC;QAAW;QAAS,SAAS;KAAC;IAC5C,SAAS,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC/B,CAAC;AAOM,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC1C,SAAS,cAAc,QAAA,CAAS;IAChC,cAAc;IACd,eAAe,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACnC,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC/B,OAAO,sBAAsB,QAAA,CAAS;IAAA,kCAAA,GAEtC,QAAQ,YAAA,CAAA,CAAE,OAAA,CAAQ;IAClB,KAAK,YAAA,CAAA,CAAE,OAAA,CAAQ;IACf,SAAS,sBAAsB,QAAA,CAAS;IACxC,WAAW,YAAA,eAAA,CAAgB,QAAA,CAAS;AACtC,CAAC;AAUM,MAAM,yBAAyB,oBAAoB,MAAA,CAAO;IAAA,+KAAA,GAE/D,YAAY,YAAA,CAAA,CAAE,KAAA,CAAM,eAAe,EAAE,QAAA,CAAS;IAC9C,cAAc,mBAAmB,QAAA,CAAS;AAC5C,CAAC;AAQM,MAAM,2BAA2B,YAAA,CAAA,CAAE,MAAA,CAAO;IAC/C,MAAM,WAAW,QAAA,CAAS;IAAA,gDAAA,GAE1B,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,8CAAA,GAE3B,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,UAAU;IAAA,6DAAA,GAE3B,QAAQ,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAAA,wGAAA,GAE7B,YAAY,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AACnC,CAAC;AAGM,MAAM,8BAA8B;AAKpC,MAAM,6BAA6B,YAAA,CAAA,CAAE,MAAA,CAAO;IACjD,QAAQ,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC5B,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,cAAc,YAAA,CAAA,CAAE,KAAA,CAAM;QAAC,YAAA,CAAA,CAAE,OAAA,CAAQ;QAAG,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC;KAAC,EAAE,QAAA,CAAS;IAC1D,YAAY,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IAC7B,aAAa,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AACpC,CAAC;AAEM,MAAM,8BAA8B,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,mDAAA,GAElD,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO;IAAA,mEAAA,GAEhB,MAAM,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,kBAAkB,EAAE,QAAA,CAAS;IAAA,0FAAA,GAE3C,UAAU,YAAA,CAAA,CAAE,KAAA,CAAM,aAAa;IAAA,4FAAA,GAE/B,OAAO,YAAA,CAAA,CAAE,KAAA,CAAM,YAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;IAAA,+LAAA,GAEpC,YAAY,YAAA,CAAA,CAAE,IAAA,CAAK;QAAC;QAAQ;QAAY,MAAM;KAAC,EAAE,QAAA,CAAS;IAAA,8CAAA,GAE1D,QAAQ,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IAAA,gHAAA,GAEzB,QAAQ,2BAA2B,QAAA,CAAS;IAAA,oDAAA,GAE5C,QAAQ,YAAA,CAAA,CACL,MAAA,CAAO;QACN,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,sBAAsB,EAAE,QAAA,CAAS;QAClD,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,qBAAqB,EAAE,QAAA,CAAS;QACjD,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,YAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;IACvC,CAAC,EACA,QAAA,CAAS;IAAA,gGAAA,GAEZ,oBAAoB,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAAA,wGAAA,GAEzC,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAChC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/model/middleware.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Document } from '../document.js';\nimport { injectInstructions } from '../formats/index.js';\nimport type {\n  MediaPart,\n  MessageData,\n  ModelInfo,\n  ModelMiddleware,\n  Part,\n} from '../model.js';\n/**\n * Preprocess a GenerateRequest to download referenced http(s) media URLs and\n * inline them as data URIs.\n */\nexport function downloadRequestMedia(options?: {\n  maxBytes?: number;\n  filter?: (part: MediaPart) => boolean;\n}): ModelMiddleware {\n  return async (req, next) => {\n    const { default: fetch } = await import('node-fetch');\n\n    const newReq = {\n      ...req,\n      messages: await Promise.all(\n        req.messages.map(async (message) => {\n          const content: Part[] = await Promise.all(\n            message.content.map(async (part) => {\n              // skip non-media parts and non-http urls, or parts that have been\n              // filtered out by user config\n              if (\n                !part.media ||\n                !part.media.url.startsWith('http') ||\n                (options?.filter && !options?.filter(part))\n              ) {\n                return part;\n              }\n\n              const response = await fetch(part.media.url, {\n                size: options?.maxBytes,\n              });\n              if (response.status !== 200)\n                throw new Error(\n                  `HTTP error downloading media '${\n                    part.media.url\n                  }': ${await response.text()}`\n                );\n\n              // use provided contentType or sniff from response\n              const contentType =\n                part.media.contentType ||\n                response.headers.get('content-type') ||\n                '';\n\n              return {\n                media: {\n                  contentType,\n                  url: `data:${contentType};base64,${Buffer.from(\n                    await response.arrayBuffer()\n                  ).toString('base64')}`,\n                },\n              };\n            })\n          );\n\n          return {\n            ...message,\n            content,\n          };\n        })\n      ),\n    };\n\n    return next(newReq);\n  };\n}\n\n/**\n * Validates that a GenerateRequest does not include unsupported features.\n */\nexport function validateSupport(options: {\n  name: string;\n  supports?: ModelInfo['supports'];\n}): ModelMiddleware {\n  const supports = options.supports || {};\n  return async (req, next) => {\n    function invalid(message: string): never {\n      throw new Error(\n        `Model '${\n          options.name\n        }' does not support ${message}. Request: ${JSON.stringify(\n          req,\n          null,\n          2\n        )}`\n      );\n    }\n\n    if (\n      supports.media === false &&\n      req.messages.some((message) => message.content.some((part) => part.media))\n    )\n      invalid('media, but media was provided');\n    if (supports.tools === false && req.tools?.length)\n      invalid('tool use, but tools were provided');\n    if (supports.multiturn === false && req.messages.length > 1)\n      invalid(`multiple messages, but ${req.messages.length} were provided`);\n    // if (\n    //   typeof supports.output !== 'undefined' &&\n    //   req.output?.format &&\n    //   !supports.output.includes(req.output?.format)\n    // )\n    //   invalid(`requested output format '${req.output?.format}'`);\n    return next();\n  };\n}\n\n// N.B. Figure out why array.findLast isn't available despite setting target\n// to ES2022 (Node 16.14.0)\nfunction lastUserMessage(messages: MessageData[]) {\n  for (let i = messages.length - 1; i >= 0; i--) {\n    if (messages[i].role === 'user') {\n      return messages[i];\n    }\n  }\n  return undefined;\n}\n\n/**\n * Provide a simulated system prompt for models that don't support it natively.\n */\nexport function simulateSystemPrompt(options?: {\n  preface: string;\n  acknowledgement: string;\n}): ModelMiddleware {\n  const preface = options?.preface || 'SYSTEM INSTRUCTIONS:\\n';\n  const acknowledgement = options?.acknowledgement || 'Understood.';\n\n  return (req, next) => {\n    const messages = [...req.messages];\n    for (let i = 0; i < messages.length; i++) {\n      if (req.messages[i].role === 'system') {\n        const systemPrompt = messages[i].content;\n        messages.splice(\n          i,\n          1,\n          { role: 'user', content: [{ text: preface }, ...systemPrompt] },\n          { role: 'model', content: [{ text: acknowledgement }] }\n        );\n        break;\n      }\n    }\n    return next({ ...req, messages });\n  };\n}\n\nexport interface AugmentWithContextOptions {\n  /** Preceding text to place before the rendered context documents. */\n  preface?: string | null;\n  /** A function to render a document into a text part to be included in the message. */\n  itemTemplate?: (d: Document, options?: AugmentWithContextOptions) => string;\n  /** The metadata key to use for citation reference. Pass `null` to provide no citations. */\n  citationKey?: string | null;\n}\n\nexport const CONTEXT_PREFACE =\n  '\\n\\nUse the following information to complete your task:\\n\\n';\nconst CONTEXT_ITEM_TEMPLATE = (\n  d: Document,\n  index: number,\n  options?: AugmentWithContextOptions\n) => {\n  let out = '- ';\n  if (options?.citationKey) {\n    out += `[${d.metadata![options.citationKey]}]: `;\n  } else if (options?.citationKey === undefined) {\n    out += `[${d.metadata?.['ref'] || d.metadata?.['id'] || index}]: `;\n  }\n  out += d.text + '\\n';\n  return out;\n};\n\nexport function augmentWithContext(\n  options?: AugmentWithContextOptions\n): ModelMiddleware {\n  const preface =\n    typeof options?.preface === 'undefined' ? CONTEXT_PREFACE : options.preface;\n  const itemTemplate = options?.itemTemplate || CONTEXT_ITEM_TEMPLATE;\n  return (req, next) => {\n    // if there is no context in the request, no-op\n    if (!req.docs?.length) return next(req);\n    const userMessage = lastUserMessage(req.messages);\n    // if there are no messages, no-op\n    if (!userMessage) return next(req);\n    // if there is already a context part, no-op\n    const contextPartIndex = userMessage?.content.findIndex(\n      (p) => p.metadata?.purpose === 'context'\n    );\n    const contextPart =\n      contextPartIndex >= 0 && userMessage.content[contextPartIndex];\n\n    if (contextPart && !contextPart.metadata?.pending) {\n      return next(req);\n    }\n    let out = `${preface || ''}`;\n    req.docs?.forEach((d, i) => {\n      out += itemTemplate(new Document(d), i, options);\n    });\n    out += '\\n';\n    if (contextPartIndex >= 0) {\n      userMessage.content[contextPartIndex] = {\n        ...contextPart,\n        text: out,\n        metadata: { purpose: 'context' },\n      } as Part;\n    } else {\n      userMessage.content.push({ text: out, metadata: { purpose: 'context' } });\n    }\n\n    return next(req);\n  };\n}\n\nexport interface SimulatedConstrainedGenerationOptions {\n  instructionsRenderer?: (schema: Record<string, any>) => string;\n}\n\nconst DEFAULT_CONSTRAINED_GENERATION_INSTRUCTIONS = (\n  schema: Record<string, any>\n) => `Output should be in JSON format and conform to the following schema:\n\n\\`\\`\\`\n${JSON.stringify(schema)}\n\\`\\`\\`\n`;\n\n/**\n * Model middleware that simulates constrained generation by injecting generation\n * instructions into the user message.\n */\nexport function simulateConstrainedGeneration(\n  options?: SimulatedConstrainedGenerationOptions\n): ModelMiddleware {\n  return (req, next) => {\n    let instructions: string | undefined;\n    if (req.output?.constrained && req.output?.schema) {\n      instructions = (\n        options?.instructionsRenderer ??\n        DEFAULT_CONSTRAINED_GENERATION_INSTRUCTIONS\n      )(req.output?.schema);\n\n      req = {\n        ...req,\n        messages: injectInstructions(req.messages, instructions),\n        output: {\n          ...req.output,\n          // we're simulating it, so to the underlying model it's unconstrained.\n          constrained: false,\n          format: undefined,\n          contentType: undefined,\n          schema: undefined,\n        },\n      };\n    }\n\n    return next(req);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,qBAAA,CAAA;AAAA,SAAA,oBAAA;IAAA,iBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,+BAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,iBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,kBAAyB;AACzB,IAAA,iBAAmC;AAY5B,SAAS,qBAAqB,OAAA,EAGjB;IAClB,OAAO,OAAO,KAAK,SAAS;QAC1B,MAAM,EAAE,SAAS,KAAA,CAAM,CAAA,GAAI,MAAM,OAAO,YAAY;QAEpD,MAAM,SAAS;YACb,GAAG,GAAA;YACH,UAAU,MAAM,QAAQ,GAAA,CACtB,IAAI,QAAA,CAAS,GAAA,CAAI,OAAO,YAAY;gBAClC,MAAM,UAAkB,MAAM,QAAQ,GAAA,CACpC,QAAQ,OAAA,CAAQ,GAAA,CAAI,OAAO,SAAS;oBAGlC,IACE,CAAC,KAAK,KAAA,IACN,CAAC,KAAK,KAAA,CAAM,GAAA,CAAI,UAAA,CAAW,MAAM,KAChC,SAAS,UAAU,CAAC,SAAS,OAAO,IAAI,GACzC;wBACA,OAAO;oBACT;oBAEA,MAAM,WAAW,MAAM,MAAM,KAAK,KAAA,CAAM,GAAA,EAAK;wBAC3C,MAAM,SAAS;oBACjB,CAAC;oBACD,IAAI,SAAS,MAAA,KAAW,KACtB,MAAM,IAAI,MACR,CAAA,8BAAA,EACE,KAAK,KAAA,CAAM,GACb,CAAA,GAAA,EAAM,MAAM,SAAS,IAAA,CAAK,CAAC,EAAA;oBAI/B,MAAM,cACJ,KAAK,KAAA,CAAM,WAAA,IACX,SAAS,OAAA,CAAQ,GAAA,CAAI,cAAc,KACnC;oBAEF,OAAO;wBACL,OAAO;4BACL;4BACA,KAAK,CAAA,KAAA,EAAQ,WAAW,CAAA,QAAA,EAAW,OAAO,IAAA,CACxC,MAAM,SAAS,WAAA,CAAY,GAC3B,QAAA,CAAS,QAAQ,CAAC,EAAA;wBACtB;oBACF;gBACF,CAAC;gBAGH,OAAO;oBACL,GAAG,OAAA;oBACH;gBACF;YACF,CAAC;QAEL;QAEA,OAAO,KAAK,MAAM;IACpB;AACF;AAKO,SAAS,gBAAgB,OAAA,EAGZ;IAClB,MAAM,WAAW,QAAQ,QAAA,IAAY,CAAC;IACtC,OAAO,OAAO,KAAK,SAAS;QAC1B,SAAS,QAAQ,OAAA,EAAwB;YACvC,MAAM,IAAI,MACR,CAAA,OAAA,EACE,QAAQ,IACV,CAAA,mBAAA,EAAsB,OAAO,CAAA,WAAA,EAAc,KAAK,SAAA,CAC9C,KACA,MACA,IACD;QAEL;QAEA,IACE,SAAS,KAAA,KAAU,SACnB,IAAI,QAAA,CAAS,IAAA,CAAK,CAAC,UAAY,QAAQ,OAAA,CAAQ,IAAA,CAAK,CAAC,OAAS,KAAK,KAAK,CAAC,GAEzE,QAAQ,+BAA+B;QACzC,IAAI,SAAS,KAAA,KAAU,SAAS,IAAI,KAAA,EAAO,QACzC,QAAQ,mCAAmC;QAC7C,IAAI,SAAS,SAAA,KAAc,SAAS,IAAI,QAAA,CAAS,MAAA,GAAS,GACxD,QAAQ,CAAA,uBAAA,EAA0B,IAAI,QAAA,CAAS,MAAM,CAAA,cAAA,CAAgB;QAOvE,OAAO,KAAK;IACd;AACF;AAIA,SAAS,gBAAgB,QAAA,EAAyB;IAChD,IAAA,IAAS,IAAI,SAAS,MAAA,GAAS,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,IAAA,KAAS,QAAQ;YAC/B,OAAO,QAAA,CAAS,CAAC,CAAA;QACnB;IACF;IACA,OAAO,KAAA;AACT;AAKO,SAAS,qBAAqB,OAAA,EAGjB;IAClB,MAAM,UAAU,SAAS,WAAW;IACpC,MAAM,kBAAkB,SAAS,mBAAmB;IAEpD,OAAO,CAAC,KAAK,SAAS;QACpB,MAAM,WAAW,CAAC;eAAG,IAAI,QAAQ;SAAA;QACjC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;YACxC,IAAI,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,IAAA,KAAS,UAAU;gBACrC,MAAM,eAAe,QAAA,CAAS,CAAC,CAAA,CAAE,OAAA;gBACjC,SAAS,MAAA,CACP,GACA,GACA;oBAAE,MAAM;oBAAQ,SAAS;wBAAC;4BAAE,MAAM;wBAAQ,GAAG;2BAAG,YAAY;qBAAA;gBAAE,GAC9D;oBAAE,MAAM;oBAAS,SAAS;wBAAC;4BAAE,MAAM;wBAAgB,CAAC;qBAAA;gBAAE;gBAExD;YACF;QACF;QACA,OAAO,KAAK;YAAE,GAAG,GAAA;YAAK;QAAS,CAAC;IAClC;AACF;AAWO,MAAM,kBACX;AACF,MAAM,wBAAwB,CAC5B,GACA,OACA,YACG;IACH,IAAI,MAAM;IACV,IAAI,SAAS,aAAa;QACxB,OAAO,CAAA,CAAA,EAAI,EAAE,QAAA,CAAU,QAAQ,WAAW,CAAC,CAAA,GAAA,CAAA;IAC7C,OAAA,IAAW,SAAS,gBAAgB,KAAA,GAAW;QAC7C,OAAO,CAAA,CAAA,EAAI,EAAE,QAAA,EAAA,CAAW,KAAK,CAAA,IAAK,EAAE,QAAA,EAAA,CAAW,IAAI,CAAA,IAAK,KAAK,CAAA,GAAA,CAAA;IAC/D;IACA,OAAO,EAAE,IAAA,GAAO;IAChB,OAAO;AACT;AAEO,SAAS,mBACd,OAAA,EACiB;IACjB,MAAM,UACJ,OAAO,SAAS,YAAY,cAAc,kBAAkB,QAAQ,OAAA;IACtE,MAAM,eAAe,SAAS,gBAAgB;IAC9C,OAAO,CAAC,KAAK,SAAS;QAEpB,IAAI,CAAC,IAAI,IAAA,EAAM,OAAQ,CAAA,OAAO,KAAK,GAAG;QACtC,MAAM,cAAc,gBAAgB,IAAI,QAAQ;QAEhD,IAAI,CAAC,YAAa,CAAA,OAAO,KAAK,GAAG;QAEjC,MAAM,mBAAmB,aAAa,QAAQ,UAC5C,CAAC,IAAM,EAAE,QAAA,EAAU,YAAY;QAEjC,MAAM,cACJ,oBAAoB,KAAK,YAAY,OAAA,CAAQ,gBAAgB,CAAA;QAE/D,IAAI,eAAe,CAAC,YAAY,QAAA,EAAU,SAAS;YACjD,OAAO,KAAK,GAAG;QACjB;QACA,IAAI,MAAM,GAAG,WAAW,EAAE,EAAA;QAC1B,IAAI,IAAA,EAAM,QAAQ,CAAC,GAAG,MAAM;YAC1B,OAAO,aAAa,IAAI,gBAAA,QAAA,CAAS,CAAC,GAAG,GAAG,OAAO;QACjD,CAAC;QACD,OAAO;QACP,IAAI,oBAAoB,GAAG;YACzB,YAAY,OAAA,CAAQ,gBAAgB,CAAA,GAAI;gBACtC,GAAG,WAAA;gBACH,MAAM;gBACN,UAAU;oBAAE,SAAS;gBAAU;YACjC;QACF,OAAO;YACL,YAAY,OAAA,CAAQ,IAAA,CAAK;gBAAE,MAAM;gBAAK,UAAU;oBAAE,SAAS;gBAAU;YAAE,CAAC;QAC1E;QAEA,OAAO,KAAK,GAAG;IACjB;AACF;AAMA,MAAM,8CAA8C,CAClD,SACG,CAAA;;;AAAA,EAGH,KAAK,SAAA,CAAU,MAAM,CAAC,CAAA;;AAAA,CAAA;AAQjB,SAAS,8BACd,OAAA,EACiB;IACjB,OAAO,CAAC,KAAK,SAAS;QACpB,IAAI;QACJ,IAAI,IAAI,MAAA,EAAQ,eAAe,IAAI,MAAA,EAAQ,QAAQ;YACjD,eAAA,CACE,SAAS,wBACT,2CAAA,EACA,IAAI,MAAA,EAAQ,MAAM;YAEpB,MAAM;gBACJ,GAAG,GAAA;gBACH,UAAA,CAAA,GAAU,eAAA,kBAAA,EAAmB,IAAI,QAAA,EAAU,YAAY;gBACvD,QAAQ;oBACN,GAAG,IAAI,MAAA;oBAAA,sEAAA;oBAEP,aAAa;oBACb,QAAQ,KAAA;oBACR,aAAa,KAAA;oBACb,QAAQ,KAAA;gBACV;YACF;QACF;QAEA,OAAO,KAAK,GAAG;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/model.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ActionFnArg,\n  BackgroundAction,\n  GenkitError,\n  Operation,\n  OperationSchema,\n  defineAction,\n  defineBackgroundAction,\n  getStreamingCallback,\n  z,\n  type Action,\n  type ActionMetadata,\n  type SimpleMiddleware,\n  type StreamingCallback,\n} from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { performance } from 'node:perf_hooks';\nimport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n} from './document.js';\nimport {\n  CandidateData,\n  GenerateRequest,\n  GenerateRequestSchema,\n  GenerateResponseChunkData,\n  GenerateResponseChunkSchema,\n  GenerateResponseData,\n  GenerateResponseSchema,\n  GenerationUsage,\n  MessageData,\n  ModelInfo,\n  Part,\n} from './model-types.js';\nimport {\n  augmentWithContext,\n  simulateConstrainedGeneration,\n  validateSupport,\n} from './model/middleware.js';\nexport { defineGenerateAction } from './generate/action.js';\nexport * from './model-types.js';\nexport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  simulateConstrainedGeneration,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n};\n\nexport type ModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema,\n  typeof GenerateResponseChunkSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type BackgroundModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = BackgroundAction<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type ModelMiddleware = SimpleMiddleware<\n  z.infer<typeof GenerateRequestSchema>,\n  z.infer<typeof GenerateResponseSchema>\n>;\n\nexport type DefineModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name: string;\n  /** Known version names for this model, e.g. `gemini-1.0-pro-001`. */\n  versions?: string[];\n  /** Capabilities this model supports. */\n  supports?: ModelInfo['supports'];\n  /** Custom options schema for this model. */\n  configSchema?: CustomOptionsSchema;\n  /** Descriptive name for this model e.g. 'Google AI - Gemini Pro'. */\n  label?: string;\n  /** Middleware to be used with this model. */\n  use?: ModelMiddleware[];\n};\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    apiVersion: 'v2';\n  } & DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: ActionFnArg<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    streamingCallback?: StreamingCallback<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: any,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: any\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineAction(\n    registry,\n    {\n      actionType: 'model',\n      name: options.name,\n      description: label,\n      inputSchema: GenerateRequestSchema,\n      outputSchema: GenerateResponseSchema,\n      metadata: {\n        model: {\n          label,\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n          versions: options.versions,\n          supports: options.supports,\n        },\n      },\n      use: middleware,\n    },\n    (input, ctx) => {\n      const startTimeMs = performance.now();\n      const secondParam =\n        options.apiVersion === 'v2'\n          ? ctx\n          : getStreamingCallback(registry) ||\n            (ctx.streamingRequested && ctx.sendChunk) ||\n            undefined;\n      return runner(input, secondParam).then((response) => {\n        const timedResponse = {\n          ...response,\n          latencyMs: performance.now() - startTimeMs,\n        };\n        return timedResponse;\n      });\n    }\n  );\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act as ModelAction<CustomOptionsSchema>;\n}\n\nexport type DefineBackgroundModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = DefineModelOptions<CustomOptionsSchema> & {\n  start: (\n    request: GenerateRequest<CustomOptionsSchema>\n  ) => Promise<Operation<GenerateResponseData>>;\n  check: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n  cancel?: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n};\n\n/**\n * Defines a new model that runs in the background.\n */\nexport function defineBackgroundModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineBackgroundModelOptions<CustomOptionsSchema>\n): BackgroundModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineBackgroundAction(registry, {\n    actionType: 'background-model',\n    name: options.name,\n    description: label,\n    inputSchema: GenerateRequestSchema,\n    outputSchema: GenerateResponseSchema,\n    metadata: {\n      model: {\n        label,\n        customOptions: options.configSchema\n          ? toJsonSchema({ schema: options.configSchema })\n          : undefined,\n        versions: options.versions,\n        supports: options.supports,\n      },\n    },\n    use: middleware,\n    async start(request) {\n      const startTimeMs = performance.now();\n      const response = await options.start(request);\n      Object.assign(response, {\n        latencyMs: performance.now() - startTimeMs,\n      });\n      return response;\n    },\n    async check(op) {\n      return options.check(op);\n    },\n    cancel: options.cancel\n      ? async (op) => {\n          if (!options.cancel) {\n            throw new GenkitError({\n              status: 'UNIMPLEMENTED',\n              message: 'cancel not implemented',\n            });\n          }\n          return options.cancel(op);\n        }\n      : undefined,\n  }) as BackgroundModelAction<CustomOptionsSchema>;\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act;\n}\n\nfunction getModelMiddleware(options: {\n  use?: ModelMiddleware[];\n  name: string;\n  supports?: ModelInfo['supports'];\n}) {\n  const middleware: ModelMiddleware[] = [\n    ...(options.use || []),\n    validateSupport(options),\n  ];\n  if (!options?.supports?.context) middleware.push(augmentWithContext());\n  const constratedSimulator = simulateConstrainedGeneration();\n  middleware.push((req, next) => {\n    if (\n      !options?.supports?.constrained ||\n      options?.supports?.constrained === 'none' ||\n      (options?.supports?.constrained === 'no-tools' &&\n        (req.tools?.length ?? 0) > 0)\n    ) {\n      return constratedSimulator(req, next);\n    }\n    return next(req);\n  });\n\n  return middleware;\n}\n\nexport interface ModelReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: ModelInfo;\n  version?: string;\n  config?: z.infer<CustomOptions>;\n\n  withConfig(cfg: z.infer<CustomOptions>): ModelReference<CustomOptions>;\n  withVersion(version: string): ModelReference<CustomOptions>;\n}\n\n/**\n * Packages model information into ActionMetadata object.\n */\nexport function modelActionMetadata({\n  name,\n  info,\n  configSchema,\n  background,\n}: {\n  name: string;\n  info?: ModelInfo;\n  configSchema?: z.ZodTypeAny;\n  background?: boolean;\n}): ActionMetadata {\n  return {\n    actionType: background ? 'background-model' : 'model',\n    name: name,\n    inputJsonSchema: toJsonSchema({ schema: GenerateRequestSchema }),\n    outputJsonSchema: background\n      ? toJsonSchema({ schema: OperationSchema })\n      : toJsonSchema({ schema: GenerateResponseSchema }),\n    metadata: {\n      model: {\n        ...info,\n        customOptions: configSchema\n          ? toJsonSchema({ schema: configSchema })\n          : undefined,\n      },\n    },\n  } as ActionMetadata;\n}\n\n/** Cretes a model reference. */\nexport function modelRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: Omit<\n    ModelReference<CustomOptionsSchema>,\n    'withConfig' | 'withVersion'\n  >\n): ModelReference<CustomOptionsSchema> {\n  const ref: Partial<ModelReference<CustomOptionsSchema>> = { ...options };\n  ref.withConfig = (\n    cfg: z.infer<CustomOptionsSchema>\n  ): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      config: cfg,\n    });\n  };\n  ref.withVersion = (version: string): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      version,\n    });\n  };\n  return ref as ModelReference<CustomOptionsSchema>;\n}\n\n/** Container for counting usage stats for a single input/output {Part} */\ntype PartCounts = {\n  characters: number;\n  images: number;\n  videos: number;\n  audio: number;\n};\n\n/**\n * Calculates basic usage statistics from the given model inputs and outputs.\n */\nexport function getBasicUsageStats(\n  input: MessageData[],\n  response: MessageData | CandidateData[]\n): GenerationUsage {\n  const inputCounts = getPartCounts(input.flatMap((md) => md.content));\n  const outputCounts = getPartCounts(\n    Array.isArray(response)\n      ? response.flatMap((c) => c.message.content)\n      : response.content\n  );\n  return {\n    inputCharacters: inputCounts.characters,\n    inputImages: inputCounts.images,\n    inputVideos: inputCounts.videos,\n    inputAudioFiles: inputCounts.audio,\n    outputCharacters: outputCounts.characters,\n    outputImages: outputCounts.images,\n    outputVideos: outputCounts.videos,\n    outputAudioFiles: outputCounts.audio,\n  };\n}\n\nfunction getPartCounts(parts: Part[]): PartCounts {\n  return parts.reduce(\n    (counts, part) => {\n      const isImage =\n        part.media?.contentType?.startsWith('image') ||\n        part.media?.url?.startsWith('data:image');\n      const isVideo =\n        part.media?.contentType?.startsWith('video') ||\n        part.media?.url?.startsWith('data:video');\n      const isAudio =\n        part.media?.contentType?.startsWith('audio') ||\n        part.media?.url?.startsWith('data:audio');\n      return {\n        characters: counts.characters + (part.text?.length || 0),\n        images: counts.images + (isImage ? 1 : 0),\n        videos: counts.videos + (isVideo ? 1 : 0),\n        audio: counts.audio + (isAudio ? 1 : 0),\n      };\n    },\n    { characters: 0, images: 0, videos: 0, audio: 0 }\n  );\n}\n\nexport type ModelArgument<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  | ModelAction<CustomOptions>\n  | ModelReference<CustomOptions>\n  | string;\n\nexport interface ResolvedModel<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  modelAction: ModelAction;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\nexport async function resolveModel<C extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  model: ModelArgument<C> | undefined,\n  options?: { warnDeprecated?: boolean }\n): Promise<ResolvedModel<C>> {\n  let out: ResolvedModel<C>;\n  let modelId: string;\n\n  if (!model) {\n    model = await registry.lookupValue('defaultModel', 'defaultModel');\n  }\n  if (!model) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Must supply a `model` to `generate()` calls.',\n    });\n  }\n  if (typeof model === 'string') {\n    modelId = model;\n    out = { modelAction: await lookupModel(registry, model) };\n  } else if (model.hasOwnProperty('__action')) {\n    modelId = (model as ModelAction).__action.name;\n    out = { modelAction: model as ModelAction };\n  } else {\n    const ref = model as ModelReference<any>;\n    modelId = ref.name;\n    out = {\n      modelAction: await lookupModel(registry, ref.name),\n      config: {\n        ...ref.config,\n      },\n      version: ref.version,\n    };\n  }\n\n  if (!out.modelAction) {\n    throw new GenkitError({\n      status: 'NOT_FOUND',\n      message: `Model '${modelId}' not found`,\n    });\n  }\n\n  if (\n    options?.warnDeprecated &&\n    out.modelAction.__action.metadata?.model?.stage === 'deprecated'\n  ) {\n    logger.warn(\n      `Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`\n    );\n  }\n\n  return out;\n}\n\nasync function lookupModel(\n  registry: Registry,\n  model: string\n): Promise<ModelAction> {\n  return (\n    (await registry.lookupAction(`/model/${model}`)) ||\n    (await registry.lookupAction(`/background-model/${model}`))\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,kBAAA,IAAA,gBAAA,gBAAA;IAAA,gBAAA,IAAA,gBAAA,cAAA;IAAA,iBAAA,IAAA,gBAAA,eAAA;IAAA,gBAAA,IAAA,gBAAA,cAAA;IAAA,uBAAA,IAAA,gBAAA,qBAAA;IAAA,wBAAA,IAAA,gBAAA,sBAAA;IAAA,uBAAA,IAAA;IAAA,sBAAA,IAAA,cAAA,oBAAA;IAAA,aAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,UAAA,IAAA;IAAA,cAAA,IAAA;IAAA,+BAAA,IAAA,kBAAA,6BAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAcO;AACP,IAAA,iBAAuB;AAEvB,IAAA,gBAA6B;AAC7B,IAAA,yBAA4B;AAC5B,IAAA,kBAaO;AACP,IAAA,qBAYO;AACP,IAAA,oBAIO;AACP,IAAA,gBAAqC;AACrC,WAAA,eAAc,mJApEd,OAAA,OAAA;AA2JO,SAAS,YAGd,QAAA,EACA,OAAA,EACA,MAAA,EAIkC;IAClC,MAAM,QAAQ,QAAQ,KAAA,IAAS,QAAQ,IAAA;IACvC,MAAM,aAAa,mBAAmB,OAAO;IAC7C,MAAM,MAAA,CAAA,GAAM,YAAA,YAAA,EACV,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa;QACb,aAAa,mBAAA,qBAAA;QACb,cAAc,mBAAA,sBAAA;QACd,UAAU;YACR,OAAO;gBACL;gBACA,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;gBACJ,UAAU,QAAQ,QAAA;gBAClB,UAAU,QAAQ,QAAA;YACpB;QACF;QACA,KAAK;IACP,GACA,CAAC,OAAO,QAAQ;QACd,MAAM,cAAc,uBAAA,WAAA,CAAY,GAAA,CAAI;QACpC,MAAM,cACJ,QAAQ,UAAA,KAAe,OACnB,MAAA,CAAA,GACA,YAAA,oBAAA,EAAqB,QAAQ,KAC5B,IAAI,kBAAA,IAAsB,IAAI,SAAA,IAC/B,KAAA;QACN,OAAO,OAAO,OAAO,WAAW,EAAE,IAAA,CAAK,CAAC,aAAa;YACnD,MAAM,gBAAgB;gBACpB,GAAG,QAAA;gBACH,WAAW,uBAAA,WAAA,CAAY,GAAA,CAAI,IAAI;YACjC;YACA,OAAO;QACT,CAAC;IACH;IAEF,OAAO,MAAA,CAAO,KAAK;QACjB,gBAAgB,QAAQ,YAAA,IAAgB,YAAA,CAAA,CAAE,OAAA,CAAQ;IACpD,CAAC;IACD,OAAO;AACT;AAmBO,SAAS,sBAGd,QAAA,EACA,OAAA,EAC4C;IAC5C,MAAM,QAAQ,QAAQ,KAAA,IAAS,QAAQ,IAAA;IACvC,MAAM,aAAa,mBAAmB,OAAO;IAC7C,MAAM,MAAA,CAAA,GAAM,YAAA,sBAAA,EAAuB,UAAU;QAC3C,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa;QACb,aAAa,mBAAA,qBAAA;QACb,cAAc,mBAAA,sBAAA;QACd,UAAU;YACR,OAAO;gBACL;gBACA,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;gBACJ,UAAU,QAAQ,QAAA;gBAClB,UAAU,QAAQ,QAAA;YACpB;QACF;QACA,KAAK;QACL,MAAM,OAAM,OAAA,EAAS;YACnB,MAAM,cAAc,uBAAA,WAAA,CAAY,GAAA,CAAI;YACpC,MAAM,WAAW,MAAM,QAAQ,KAAA,CAAM,OAAO;YAC5C,OAAO,MAAA,CAAO,UAAU;gBACtB,WAAW,uBAAA,WAAA,CAAY,GAAA,CAAI,IAAI;YACjC,CAAC;YACD,OAAO;QACT;QACA,MAAM,OAAM,EAAA,EAAI;YACd,OAAO,QAAQ,KAAA,CAAM,EAAE;QACzB;QACA,QAAQ,QAAQ,MAAA,GACZ,OAAO,OAAO;YACZ,IAAI,CAAC,QAAQ,MAAA,EAAQ;gBACnB,MAAM,IAAI,YAAA,WAAA,CAAY;oBACpB,QAAQ;oBACR,SAAS;gBACX,CAAC;YACH;YACA,OAAO,QAAQ,MAAA,CAAO,EAAE;QAC1B,IACA,KAAA;IACN,CAAC;IACD,OAAO,MAAA,CAAO,KAAK;QACjB,gBAAgB,QAAQ,YAAA,IAAgB,YAAA,CAAA,CAAE,OAAA,CAAQ;IACpD,CAAC;IACD,OAAO;AACT;AAEA,SAAS,mBAAmB,OAAA,EAIzB;IACD,MAAM,aAAgC;WAChC,QAAQ,GAAA,IAAO,CAAC,CAAA;QAAA,CAAA,GACpB,kBAAA,eAAA,EAAgB,OAAO;KACzB;IACA,IAAI,CAAC,SAAS,UAAU,QAAS,CAAA,WAAW,IAAA,CAAA,CAAA,GAAK,kBAAA,kBAAA,EAAmB,CAAC;IACrE,MAAM,sBAAA,CAAA,GAAsB,kBAAA,6BAAA,EAA8B;IAC1D,WAAW,IAAA,CAAK,CAAC,KAAK,SAAS;QAC7B,IACE,CAAC,SAAS,UAAU,eACpB,SAAS,UAAU,gBAAgB,UAClC,SAAS,UAAU,gBAAgB,cAAA,CACjC,IAAI,KAAA,EAAO,UAAU,CAAA,IAAK,GAC7B;YACA,OAAO,oBAAoB,KAAK,IAAI;QACtC;QACA,OAAO,KAAK,GAAG;IACjB,CAAC;IAED,OAAO;AACT;AAgBO,SAAS,oBAAoB,EAClC,IAAA,EACA,IAAA,EACA,YAAA,EACA,UAAA,EACF,EAKmB;IACjB,OAAO;QACL,YAAY,aAAa,qBAAqB;QAC9C;QACA,iBAAA,CAAA,GAAiB,cAAA,YAAA,EAAa;YAAE,QAAQ,mBAAA,qBAAA;QAAsB,CAAC;QAC/D,kBAAkB,aAAA,CAAA,GACd,cAAA,YAAA,EAAa;YAAE,QAAQ,YAAA,eAAA;QAAgB,CAAC,IAAA,CAAA,GACxC,cAAA,YAAA,EAAa;YAAE,QAAQ,mBAAA,sBAAA;QAAuB,CAAC;QACnD,UAAU;YACR,OAAO;gBACL,GAAG,IAAA;gBACH,eAAe,eAAA,CAAA,GACX,cAAA,YAAA,EAAa;oBAAE,QAAQ;gBAAa,CAAC,IACrC,KAAA;YACN;QACF;IACF;AACF;AAGO,SAAS,SAGd,OAAA,EAIqC;IACrC,MAAM,MAAoD;QAAE,GAAG,OAAA;IAAQ;IACvE,IAAI,UAAA,GAAa,CACf,QACwC;QACxC,OAAO,SAAS;YACd,GAAG,OAAA;YACH,QAAQ;QACV,CAAC;IACH;IACA,IAAI,WAAA,GAAc,CAAC,YAAyD;QAC1E,OAAO,SAAS;YACd,GAAG,OAAA;YACH;QACF,CAAC;IACH;IACA,OAAO;AACT;AAaO,SAAS,mBACd,KAAA,EACA,QAAA,EACiB;IACjB,MAAM,cAAc,cAAc,MAAM,OAAA,CAAQ,CAAC,KAAO,GAAG,OAAO,CAAC;IACnE,MAAM,eAAe,cACnB,MAAM,OAAA,CAAQ,QAAQ,IAClB,SAAS,OAAA,CAAQ,CAAC,IAAM,EAAE,OAAA,CAAQ,OAAO,IACzC,SAAS,OAAA;IAEf,OAAO;QACL,iBAAiB,YAAY,UAAA;QAC7B,aAAa,YAAY,MAAA;QACzB,aAAa,YAAY,MAAA;QACzB,iBAAiB,YAAY,KAAA;QAC7B,kBAAkB,aAAa,UAAA;QAC/B,cAAc,aAAa,MAAA;QAC3B,cAAc,aAAa,MAAA;QAC3B,kBAAkB,aAAa,KAAA;IACjC;AACF;AAEA,SAAS,cAAc,KAAA,EAA2B;IAChD,OAAO,MAAM,MAAA,CACX,CAAC,QAAQ,SAAS;QAChB,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,OAAO;YACL,YAAY,OAAO,UAAA,GAAA,CAAc,KAAK,IAAA,EAAM,UAAU,CAAA;YACtD,QAAQ,OAAO,MAAA,GAAA,CAAU,UAAU,IAAI,CAAA;YACvC,QAAQ,OAAO,MAAA,GAAA,CAAU,UAAU,IAAI,CAAA;YACvC,OAAO,OAAO,KAAA,GAAA,CAAS,UAAU,IAAI,CAAA;QACvC;IACF,GACA;QAAE,YAAY;QAAG,QAAQ;QAAG,QAAQ;QAAG,OAAO;IAAE;AAEpD;AAeA,eAAsB,aACpB,QAAA,EACA,KAAA,EACA,OAAA,EAC2B;IAC3B,IAAI;IACJ,IAAI;IAEJ,IAAI,CAAC,OAAO;QACV,QAAQ,MAAM,SAAS,WAAA,CAAY,gBAAgB,cAAc;IACnE;IACA,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;IACH;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,UAAU;QACV,MAAM;YAAE,aAAa,MAAM,YAAY,UAAU,KAAK;QAAE;IAC1D,OAAA,IAAW,MAAM,cAAA,CAAe,UAAU,GAAG;QAC3C,UAAW,MAAsB,QAAA,CAAS,IAAA;QAC1C,MAAM;YAAE,aAAa;QAAqB;IAC5C,OAAO;QACL,MAAM,MAAM;QACZ,UAAU,IAAI,IAAA;QACd,MAAM;YACJ,aAAa,MAAM,YAAY,UAAU,IAAI,IAAI;YACjD,QAAQ;gBACN,GAAG,IAAI,MAAA;YACT;YACA,SAAS,IAAI,OAAA;QACf;IACF;IAEA,IAAI,CAAC,IAAI,WAAA,EAAa;QACpB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,OAAA,EAAU,OAAO,CAAA,WAAA,CAAA;QAC5B,CAAC;IACH;IAEA,IACE,SAAS,kBACT,IAAI,WAAA,CAAY,QAAA,CAAS,QAAA,EAAU,OAAO,UAAU,cACpD;QACA,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,OAAA,EAAU,IAAI,WAAA,CAAY,QAAA,CAAS,IAAI,CAAA,uDAAA,CAAA;IAE3C;IAEA,OAAO;AACT;AAEA,eAAe,YACb,QAAA,EACA,KAAA,EACsB;IACtB,OACG,MAAM,SAAS,YAAA,CAAa,CAAA,OAAA,EAAU,KAAK,EAAE,KAC7C,MAAM,SAAS,YAAA,CAAa,CAAA,kBAAA,EAAqB,KAAK,EAAE;AAE7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/resource.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Action,\n  ActionContext,\n  defineAction,\n  GenkitError,\n  z,\n} from '@genkit-ai/core';\nimport { Registry } from '@genkit-ai/core/registry';\nimport uriTemplate from 'uri-templates';\nimport { PartSchema } from './model-types.js';\n\n/**\n * Options for defining a resource.\n */\nexport interface ResourceOptions {\n  /**\n   * Resource name. If not specified, uri or template will be used as name.\n   */\n  name?: string;\n\n  /**\n   * The URI of the resource. Can contain template variables.\n   */\n  uri?: string;\n\n  /**\n   * The URI template (ex. `my://resource/{id}`). See RFC6570 for specification.\n   */\n  template?: string;\n\n  /**\n   * A description of the resource.\n   */\n  description?: string;\n\n  /**\n   * Resource metadata.\n   */\n  metadata?: Record<string, any>;\n}\n\nexport const ResourceInputSchema = z.object({\n  uri: z.string(),\n});\n\nexport type ResourceInput = z.infer<typeof ResourceInputSchema>;\n\nexport const ResourceOutputSchema = z.object({\n  content: z.array(PartSchema),\n});\n\nexport type ResourceOutput = z.infer<typeof ResourceOutputSchema>;\n\n/**\n * A function that returns parts for a given resource.\n */\nexport type ResourceFn = (\n  input: ResourceInput,\n  ctx: ActionContext\n) => ResourceOutput | Promise<ResourceOutput>;\n\n/**\n * A resource action.\n */\nexport interface ResourceAction\n  extends Action<typeof ResourceInputSchema, typeof ResourceOutputSchema> {\n  matches(input: ResourceInput): boolean;\n}\n\n/**\n * Defines a resource.\n *\n * @param registry The registry to register the resource with.\n * @param opts The resource options.\n * @param fn The resource function.\n * @returns The resource action.\n */\nexport function defineResource(\n  registry: Registry,\n  opts: ResourceOptions,\n  fn: ResourceFn\n): ResourceAction {\n  const uri = opts.uri ?? opts.template;\n  if (!uri) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `must specify either url or template options`,\n    });\n  }\n  const template = opts.template ? uriTemplate(opts.template) : undefined;\n  const matcher = opts.uri\n    ? // TODO: normalize resource URI during comparisons\n      // foo://bar?baz=1&qux=2 and foo://bar?qux=2&baz=1 are equivalent URIs but would not match.\n      (input: string) => (input === opts.uri ? {} : undefined)\n    : (input: string) => {\n        return template!.fromUri(input);\n      };\n\n  const action = defineAction(\n    registry,\n    {\n      actionType: 'resource',\n      name: opts.name ?? uri,\n      description: opts.description,\n      inputSchema: ResourceInputSchema,\n      outputSchema: ResourceOutputSchema,\n      metadata: {\n        resource: {\n          uri: opts.uri,\n          template: opts.template,\n        },\n        ...opts.metadata,\n      },\n    },\n    async (input, ctx) => {\n      const templateMatch = matcher(input.uri);\n      if (!templateMatch) {\n        throw new GenkitError({\n          status: 'INVALID_ARGUMENT',\n          message: `input ${input} did not match template ${uri}`,\n        });\n      }\n      const parts = await fn(input, ctx);\n      parts.content.map((p) => {\n        if (!p.metadata) {\n          p.metadata = {};\n        }\n        if (p.metadata?.resource) {\n          if (!(p.metadata as any).resource.parent) {\n            (p.metadata as any).resource.parent = {\n              uri: input.uri,\n            };\n            if (opts.template) {\n              (p.metadata as any).resource.parent.template = opts.template;\n            }\n          }\n        } else {\n          (p.metadata as any).resource = {\n            uri: input.uri,\n          };\n          if (opts.template) {\n            (p.metadata as any).resource.template = opts.template;\n          }\n        }\n        return p;\n      });\n      return parts;\n    }\n  ) as ResourceAction;\n\n  action.matches = (input: ResourceInput) => matcher(input.uri) !== undefined;\n\n  return action;\n}\n\n/**\n * Finds a matching resource in the registry. If not found returns undefined.\n */\nexport async function findMatchingResource(\n  registry: Registry,\n  input: ResourceInput\n): Promise<ResourceAction | undefined> {\n  for (const actKeys of Object.keys(await registry.listResolvableActions())) {\n    if (actKeys.startsWith('/resource/')) {\n      const resource = (await registry.lookupAction(actKeys)) as ResourceAction;\n      if (resource.matches(input)) {\n        return resource;\n      }\n    }\n  }\n  return undefined;\n}\n"], "names": ["uriTemplate"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,qBAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,sBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAMO;AAEP,IAAA,uBAAwB;AACxB,IAAA,qBAA2B;AAgCpB,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC1C,KAAK,YAAA,CAAA,CAAE,MAAA,CAAO;AAChB,CAAC;AAIM,MAAM,uBAAuB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC3C,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,mBAAA,UAAU;AAC7B,CAAC;AA4BM,SAAS,eACd,QAAA,EACA,IAAA,EACA,EAAA,EACgB;IAChB,MAAM,MAAM,KAAK,GAAA,IAAO,KAAK,QAAA;IAC7B,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,2CAAA,CAAA;QACX,CAAC;IACH;IACA,MAAM,WAAW,KAAK,QAAA,GAAA,CAAA,GAAW,qBAAAA,OAAAA,EAAY,KAAK,QAAQ,IAAI,KAAA;IAC9D,MAAM,UAAU,KAAK,GAAA,GAAA,kDAAA;IAAA,2FAAA;IAGjB,CAAC,QAAmB,UAAU,KAAK,GAAA,GAAM,CAAC,IAAI,KAAA,IAC9C,CAAC,UAAkB;QACjB,OAAO,SAAU,OAAA,CAAQ,KAAK;IAChC;IAEJ,MAAM,SAAA,CAAA,GAAS,YAAA,YAAA,EACb,UACA;QACE,YAAY;QACZ,MAAM,KAAK,IAAA,IAAQ;QACnB,aAAa,KAAK,WAAA;QAClB,aAAa;QACb,cAAc;QACd,UAAU;YACR,UAAU;gBACR,KAAK,KAAK,GAAA;gBACV,UAAU,KAAK,QAAA;YACjB;YACA,GAAG,KAAK,QAAA;QACV;IACF,GACA,OAAO,OAAO,QAAQ;QACpB,MAAM,gBAAgB,QAAQ,MAAM,GAAG;QACvC,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,MAAA,EAAS,KAAK,CAAA,wBAAA,EAA2B,GAAG,EAAA;YACvD,CAAC;QACH;QACA,MAAM,QAAQ,MAAM,GAAG,OAAO,GAAG;QACjC,MAAM,OAAA,CAAQ,GAAA,CAAI,CAAC,MAAM;YACvB,IAAI,CAAC,EAAE,QAAA,EAAU;gBACf,EAAE,QAAA,GAAW,CAAC;YAChB;YACA,IAAI,EAAE,QAAA,EAAU,UAAU;gBACxB,IAAI,CAAE,EAAE,QAAA,CAAiB,QAAA,CAAS,MAAA,EAAQ;oBACvC,EAAE,QAAA,CAAiB,QAAA,CAAS,MAAA,GAAS;wBACpC,KAAK,MAAM,GAAA;oBACb;oBACA,IAAI,KAAK,QAAA,EAAU;wBAChB,EAAE,QAAA,CAAiB,QAAA,CAAS,MAAA,CAAO,QAAA,GAAW,KAAK,QAAA;oBACtD;gBACF;YACF,OAAO;gBACJ,EAAE,QAAA,CAAiB,QAAA,GAAW;oBAC7B,KAAK,MAAM,GAAA;gBACb;gBACA,IAAI,KAAK,QAAA,EAAU;oBAChB,EAAE,QAAA,CAAiB,QAAA,CAAS,QAAA,GAAW,KAAK,QAAA;gBAC/C;YACF;YACA,OAAO;QACT,CAAC;QACD,OAAO;IACT;IAGF,OAAO,OAAA,GAAU,CAAC,QAAyB,QAAQ,MAAM,GAAG,MAAM,KAAA;IAElE,OAAO;AACT;AAKA,eAAsB,qBACpB,QAAA,EACA,KAAA,EACqC;IACrC,KAAA,MAAW,WAAW,OAAO,IAAA,CAAK,MAAM,SAAS,qBAAA,CAAsB,CAAC,EAAG;QACzE,IAAI,QAAQ,UAAA,CAAW,YAAY,GAAG;YACpC,MAAM,WAAY,MAAM,SAAS,YAAA,CAAa,OAAO;YACrD,IAAI,SAAS,OAAA,CAAQ,KAAK,GAAG;gBAC3B,OAAO;YACT;QACF;IACF;IACA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2330, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/message.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { extractJson } from './extract';\nimport type {\n  MessageData,\n  Part,\n  ToolRequestPart,\n  ToolResponsePart,\n} from './model';\n\nexport type MessageParser<T = unknown> = (message: Message) => T;\n\n/**\n * Message represents a single role's contribution to a generation. Each message\n * can contain multiple parts (for example text and an image), and each generation\n * can contain multiple messages.\n */\nexport class Message<T = unknown> implements MessageData {\n  role: MessageData['role'];\n  content: Part[];\n  metadata?: Record<string, any>;\n  parser?: MessageParser<T>;\n\n  static parseData(\n    lenientMessage:\n      | string\n      | (MessageData & { content: string | Part | Part[]; role: string })\n      | MessageData,\n    defaultRole: MessageData['role'] = 'user'\n  ): MessageData {\n    if (typeof lenientMessage === 'string') {\n      return { role: defaultRole, content: [{ text: lenientMessage }] };\n    }\n    return {\n      ...lenientMessage,\n      content: Message.parseContent(lenientMessage.content),\n    };\n  }\n\n  static parse(\n    lenientMessage: string | (MessageData & { content: string }) | MessageData\n  ): Message {\n    return new Message(Message.parseData(lenientMessage));\n  }\n\n  static parseContent(lenientPart: string | Part | (string | Part)[]): Part[] {\n    if (typeof lenientPart === 'string') {\n      return [{ text: lenientPart }];\n    } else if (Array.isArray(lenientPart)) {\n      return lenientPart.map((p) => (typeof p === 'string' ? { text: p } : p));\n    } else {\n      return [lenientPart];\n    }\n  }\n\n  constructor(message: MessageData, options?: { parser?: MessageParser<T> }) {\n    this.role = message.role;\n    this.content = message.content;\n    this.metadata = message.metadata;\n    this.parser = options?.parser;\n  }\n\n  /**\n   * Attempts to parse the content of the message according to the supplied\n   * output parser. Without a parser, returns `data` contained in the message or\n   * tries to parse JSON from the text of the message.\n   *\n   * @returns The structured output contained in the message.\n   */\n  get output(): T {\n    return this.parser?.(this) || this.data || extractJson<T>(this.text);\n  }\n\n  toolResponseParts(): ToolResponsePart[] {\n    const res = this.content.filter((part) => !!part.toolResponse);\n    return res as ToolResponsePart[];\n  }\n\n  /**\n   * Concatenates all `text` parts present in the message with no delimiter.\n   * @returns A string of all concatenated text parts.\n   */\n  get text(): string {\n    return this.content.map((part) => part.text || '').join('');\n  }\n\n  /**\n   * Concatenates all `reasoning` parts present in the message with no delimiter.\n   * @returns A string of all concatenated reasoning parts.\n   */\n  get reasoning(): string {\n    return this.content.map((part) => part.reasoning || '').join('');\n  }\n\n  /**\n   * Returns the first media part detected in the message. Useful for extracting\n   * (for example) an image from a generation expected to create one.\n   * @returns The first detected `media` part in the message.\n   */\n  get media(): { url: string; contentType?: string } | null {\n    return this.content.find((part) => part.media)?.media || null;\n  }\n\n  /**\n   * Returns the first detected `data` part of a message.\n   * @returns The first `data` part detected in the message (if any).\n   */\n  get data(): T | null {\n    return this.content.find((part) => part.data)?.data as T | null;\n  }\n\n  /**\n   * Returns all tool request found in this message.\n   * @returns Array of all tool request found in this message.\n   */\n  get toolRequests(): ToolRequestPart[] {\n    return this.content.filter(\n      (part) => !!part.toolRequest\n    ) as ToolRequestPart[];\n  }\n\n  /**\n   * Returns all tool requests annotated with interrupt metadata.\n   * @returns Array of all interrupt tool requests.\n   */\n  get interrupts(): ToolRequestPart[] {\n    return this.toolRequests.filter((t) => !!t.metadata?.interrupt);\n  }\n\n  /**\n   * Converts the Message to a plain JS object.\n   * @returns Plain JS object representing the data contained in the message.\n   */\n  toJSON(): MessageData {\n    const out: MessageData = {\n      role: this.role,\n      content: [...this.content],\n    };\n    if (this.metadata) out.metadata = this.metadata;\n    return out;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,SAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,iBAA4B;AAerB,MAAM,QAA4C;IACvD,KAAA;IACA,QAAA;IACA,SAAA;IACA,OAAA;IAEA,OAAO,UACL,cAAA,EAIA,cAAmC,MAAA,EACtB;QACb,IAAI,OAAO,mBAAmB,UAAU;YACtC,OAAO;gBAAE,MAAM;gBAAa,SAAS;oBAAC;wBAAE,MAAM;oBAAe,CAAC;iBAAA;YAAE;QAClE;QACA,OAAO;YACL,GAAG,cAAA;YACH,SAAS,QAAQ,YAAA,CAAa,eAAe,OAAO;QACtD;IACF;IAEA,OAAO,MACL,cAAA,EACS;QACT,OAAO,IAAI,QAAQ,QAAQ,SAAA,CAAU,cAAc,CAAC;IACtD;IAEA,OAAO,aAAa,WAAA,EAAwD;QAC1E,IAAI,OAAO,gBAAgB,UAAU;YACnC,OAAO;gBAAC;oBAAE,MAAM;gBAAY,CAAC;aAAA;QAC/B,OAAA,IAAW,MAAM,OAAA,CAAQ,WAAW,GAAG;YACrC,OAAO,YAAY,GAAA,CAAI,CAAC,IAAO,OAAO,MAAM,WAAW;oBAAE,MAAM;gBAAE,IAAI,CAAE;QACzE,OAAO;YACL,OAAO;gBAAC,WAAW;aAAA;QACrB;IACF;IAEA,YAAY,OAAA,EAAsB,OAAA,CAAyC;QACzE,IAAA,CAAK,IAAA,GAAO,QAAQ,IAAA;QACpB,IAAA,CAAK,OAAA,GAAU,QAAQ,OAAA;QACvB,IAAA,CAAK,QAAA,GAAW,QAAQ,QAAA;QACxB,IAAA,CAAK,MAAA,GAAS,SAAS;IACzB;IAAA;;;;;;GAAA,GASA,IAAI,SAAY;QACd,OAAO,IAAA,CAAK,MAAA,GAAS,IAAI,KAAK,IAAA,CAAK,IAAA,IAAA,CAAA,GAAQ,eAAA,WAAA,EAAe,IAAA,CAAK,IAAI;IACrE;IAEA,oBAAwC;QACtC,MAAM,MAAM,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,CAAC,OAAS,CAAC,CAAC,KAAK,YAAY;QAC7D,OAAO;IACT;IAAA;;;GAAA,GAMA,IAAI,OAAe;QACjB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,IAAQ,EAAE,EAAE,IAAA,CAAK,EAAE;IAC5D;IAAA;;;GAAA,GAMA,IAAI,YAAoB;QACtB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,OAAS,KAAK,SAAA,IAAa,EAAE,EAAE,IAAA,CAAK,EAAE;IACjE;IAAA;;;;GAAA,GAOA,IAAI,QAAsD;QACxD,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,CAAC,OAAS,KAAK,KAAK,GAAG,SAAS;IAC3D;IAAA;;;GAAA,GAMA,IAAI,OAAiB;QACnB,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,CAAC,OAAS,KAAK,IAAI,GAAG;IACjD;IAAA;;;GAAA,GAMA,IAAI,eAAkC;QACpC,OAAO,IAAA,CAAK,OAAA,CAAQ,MAAA,CAClB,CAAC,OAAS,CAAC,CAAC,KAAK,WAAA;IAErB;IAAA;;;GAAA,GAMA,IAAI,aAAgC;QAClC,OAAO,IAAA,CAAK,YAAA,CAAa,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,EAAE,QAAA,EAAU,SAAS;IAChE;IAAA;;;GAAA,GAMA,SAAsB;QACpB,MAAM,MAAmB;YACvB,MAAM,IAAA,CAAK,IAAA;YACX,SAAS,CAAC;mBAAG,IAAA,CAAK,OAAO;aAAA;QAC3B;QACA,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,IAAI,QAAA,GAAW,IAAA,CAAK,QAAA;QACvC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2479, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/chat.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { StreamingCallback, z } from '@genkit-ai/core';\nimport { Channel } from '@genkit-ai/core/async';\nimport {\n  ATTR_PREFIX,\n  SPAN_TYPE_ATTR,\n  runInNewSpan,\n} from '@genkit-ai/core/tracing';\nimport {\n  generate,\n  type GenerateOptions,\n  type GenerateResponse,\n  type GenerateResponseChunk,\n  type GenerateStreamOptions,\n  type GenerateStreamResponse,\n  type GenerationCommonConfigSchema,\n  type MessageData,\n  type Part,\n} from './index.js';\nimport {\n  runWithSession,\n  type BaseGenerateOptions,\n  type Session,\n  type SessionStore,\n} from './session.js';\n\nexport const MAIN_THREAD = 'main';\nexport const SESSION_ID_ATTR = `${ATTR_PREFIX}:sessionId`;\nexport const THREAD_NAME_ATTR = `${ATTR_PREFIX}:threadName`;\n\nexport type ChatGenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = GenerateOptions<O, CustomOptions>;\n\nexport interface PromptRenderOptions<I> {\n  input?: I;\n}\n\nexport type ChatOptions<I = undefined, S = any> = (\n  | PromptRenderOptions<I>\n  | BaseGenerateOptions\n) & {\n  store?: SessionStore<S>;\n  sessionId?: string;\n};\n\n/**\n * Chat encapsulates a statful execution environment for chat.\n * Chat session executed within a session in this environment will have acesss to\n * session convesation history.\n *\n * ```ts\n * const ai = genkit({...});\n * const chat = ai.chat(); // create a Chat\n * let response = await chat.send('hi, my name is Genkit');\n * response = await chat.send('what is my name?'); // chat history aware conversation\n * ```\n */\nexport class Chat {\n  private requestBase?: Promise<BaseGenerateOptions>;\n  readonly sessionId: string;\n  private _messages?: MessageData[];\n  private threadName: string;\n\n  constructor(\n    readonly session: Session,\n    requestBase: Promise<BaseGenerateOptions>,\n    options: {\n      id: string;\n      thread: string;\n      messages?: MessageData[];\n    }\n  ) {\n    this.sessionId = options.id;\n    this.threadName = options.thread;\n    this.requestBase = requestBase?.then((rb) => {\n      const requestBase = { ...rb };\n      // this is handling dotprompt render case\n      if (requestBase && requestBase['prompt']) {\n        const basePrompt = requestBase['prompt'] as string | Part | Part[];\n        let promptMessage: MessageData;\n        if (typeof basePrompt === 'string') {\n          promptMessage = {\n            role: 'user',\n            content: [{ text: basePrompt }],\n          };\n        } else if (Array.isArray(basePrompt)) {\n          promptMessage = {\n            role: 'user',\n            content: basePrompt,\n          };\n        } else {\n          promptMessage = {\n            role: 'user',\n            content: [basePrompt],\n          };\n        }\n        requestBase.messages = [...(requestBase.messages ?? []), promptMessage];\n      }\n      if (hasPreamble(requestBase.messages)) {\n        requestBase.messages = [\n          // if request base contains a preamble, always put it first\n          ...(getPreamble(requestBase.messages) ?? []),\n          // strip out the preamble from history\n          ...(stripPreamble(options.messages) ?? []),\n          // add whatever non-preamble remains from request\n          ...(stripPreamble(requestBase.messages) ?? []),\n        ];\n      } else {\n        requestBase.messages = [\n          ...(options.messages ?? []),\n          ...(requestBase.messages ?? []),\n        ];\n      }\n      this._messages = requestBase.messages;\n      return requestBase;\n    });\n    this._messages = options.messages;\n  }\n\n  async send<\n    O extends z.ZodTypeAny = z.ZodTypeAny,\n    CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n  >(\n    options: string | Part[] | ChatGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateResponse<z.infer<O>>> {\n    return runWithSession(this.session.registry, this.session, () =>\n      runInNewSpan(\n        this.session.registry,\n        {\n          metadata: {\n            name: 'send',\n          },\n          labels: {\n            [SPAN_TYPE_ATTR]: 'helper',\n            [SESSION_ID_ATTR]: this.session.id,\n            [THREAD_NAME_ATTR]: this.threadName,\n          },\n        },\n        async (metadata) => {\n          const resolvedOptions = resolveSendOptions(options);\n          let streamingCallback:\n            | StreamingCallback<GenerateResponseChunk>\n            | undefined = undefined;\n\n          if (resolvedOptions.onChunk || resolvedOptions.streamingCallback) {\n            streamingCallback =\n              resolvedOptions.onChunk ?? resolvedOptions.streamingCallback;\n          }\n          const request: GenerateOptions = {\n            ...(await this.requestBase),\n            messages: this.messages,\n            ...resolvedOptions,\n          };\n          metadata.input = resolvedOptions;\n          const response = await generate(this.session.registry, {\n            ...request,\n            onChunk: streamingCallback,\n          });\n          this.requestBase = Promise.resolve({\n            ...(await this.requestBase),\n            // these things may get changed by tools calling within generate.\n            tools: response?.request?.tools?.map((td) => td.name),\n            toolChoice: response?.request?.toolChoice,\n            config: response?.request?.config,\n          });\n          await this.updateMessages(response.messages);\n          metadata.output = JSON.stringify(response);\n          return response;\n        }\n      )\n    );\n  }\n\n  sendStream<\n    O extends z.ZodTypeAny = z.ZodTypeAny,\n    CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n  >(\n    options: string | Part[] | GenerateStreamOptions<O, CustomOptions>\n  ): GenerateStreamResponse<z.infer<O>> {\n    const channel = new Channel<GenerateResponseChunk>();\n    const resolvedOptions = resolveSendOptions(options);\n\n    const sent = this.send({\n      ...resolvedOptions,\n      onChunk: (chunk) => channel.send(chunk),\n    });\n    sent.then(\n      () => channel.close(),\n      (err) => channel.error(err)\n    );\n\n    return {\n      response: sent,\n      stream: channel,\n    };\n  }\n\n  get messages(): MessageData[] {\n    return this._messages ?? [];\n  }\n\n  private async updateMessages(messages: MessageData[]): Promise<void> {\n    this._messages = messages;\n    await this.session.updateMessages(this.threadName, messages);\n  }\n}\n\nfunction hasPreamble(msgs?: MessageData[]) {\n  return !!msgs?.find((m) => m.metadata?.preamble);\n}\n\nfunction getPreamble(msgs?: MessageData[]) {\n  return msgs?.filter((m) => m.metadata?.preamble);\n}\n\nfunction stripPreamble(msgs?: MessageData[]) {\n  return msgs?.filter((m) => !m.metadata?.preamble);\n}\n\nfunction resolveSendOptions<\n  O extends z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny,\n>(\n  options: string | Part[] | ChatGenerateOptions<O, CustomOptions>\n): ChatGenerateOptions<O, CustomOptions> {\n  let resolvedOptions: ChatGenerateOptions<O, CustomOptions>;\n\n  // string\n  if (typeof options === 'string') {\n    resolvedOptions = {\n      prompt: options,\n    } as ChatGenerateOptions<O, CustomOptions>;\n  } else if (Array.isArray(options)) {\n    // Part[]\n    resolvedOptions = {\n      prompt: options,\n    } as ChatGenerateOptions<O, CustomOptions>;\n  } else {\n    resolvedOptions = options as ChatGenerateOptions<O, CustomOptions>;\n  }\n  return resolvedOptions;\n}\n"], "names": ["requestBase"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,MAAA,IAAA;IAAA,aAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAiBA,IAAA,eAAwB;AACxB,IAAA,iBAIO;AACP,IAAA,eAUO;AACP,IAAA,iBAKO;AAEA,MAAM,cAAc;AACpB,MAAM,kBAAkB,GAAG,eAAA,WAAW,CAAA,UAAA,CAAA;AACtC,MAAM,mBAAmB,GAAG,eAAA,WAAW,CAAA,WAAA,CAAA;AA+BvC,MAAM,KAAK;IAMhB,YACW,OAAA,EACT,WAAA,EACA,OAAA,CAKA;QAPS,IAAA,CAAA,OAAA,GAAA;QAQT,IAAA,CAAK,SAAA,GAAY,QAAQ,EAAA;QACzB,IAAA,CAAK,UAAA,GAAa,QAAQ,MAAA;QAC1B,IAAA,CAAK,WAAA,GAAc,aAAa,KAAK,CAAC,OAAO;YAC3C,MAAMA,eAAc;gBAAE,GAAG,EAAA;YAAG;YAE5B,IAAIA,gBAAeA,YAAAA,CAAY,QAAQ,CAAA,EAAG;gBACxC,MAAM,aAAaA,YAAAA,CAAY,QAAQ,CAAA;gBACvC,IAAI;gBACJ,IAAI,OAAO,eAAe,UAAU;oBAClC,gBAAgB;wBACd,MAAM;wBACN,SAAS;4BAAC;gCAAE,MAAM;4BAAW,CAAC;yBAAA;oBAChC;gBACF,OAAA,IAAW,MAAM,OAAA,CAAQ,UAAU,GAAG;oBACpC,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;gBACF,OAAO;oBACL,gBAAgB;wBACd,MAAM;wBACN,SAAS;4BAAC,UAAU;yBAAA;oBACtB;gBACF;gBACAA,aAAY,QAAA,GAAW,CAAC;uBAAIA,aAAY,QAAA,IAAY,CAAC,CAAA;oBAAI,aAAa;iBAAA;YACxE;YACA,IAAI,YAAYA,aAAY,QAAQ,GAAG;gBACrCA,aAAY,QAAA,GAAW;oBAAA,2DAAA;uBAEjB,YAAYA,aAAY,QAAQ,KAAK,CAAC,CAAA;oBAAA,sCAAA;uBAEtC,cAAc,QAAQ,QAAQ,KAAK,CAAC,CAAA;oBAAA,iDAAA;uBAEpC,cAAcA,aAAY,QAAQ,KAAK,CAAC,CAAA;iBAC9C;YACF,OAAO;gBACLA,aAAY,QAAA,GAAW;uBACjB,QAAQ,QAAA,IAAY,CAAC,CAAA;uBACrBA,aAAY,QAAA,IAAY,CAAC,CAAA;iBAC/B;YACF;YACA,IAAA,CAAK,SAAA,GAAYA,aAAY,QAAA;YAC7B,OAAOA;QACT,CAAC;QACD,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;IAC3B;IA3DQ,YAAA;IACC,UAAA;IACD,UAAA;IACA,WAAA;IA0DR,MAAM,KAIJ,OAAA,EACuC;QACvC,OAAA,CAAA,GAAO,eAAA,cAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,EAAS,IAAA,CAAA,GACzD,eAAA,YAAA,EACE,IAAA,CAAK,OAAA,CAAQ,QAAA,EACb;gBACE,UAAU;oBACR,MAAM;gBACR;gBACA,QAAQ;oBACN,CAAC,eAAA,cAAc,CAAA,EAAG;oBAClB,CAAC,eAAe,CAAA,EAAG,IAAA,CAAK,OAAA,CAAQ,EAAA;oBAChC,CAAC,gBAAgB,CAAA,EAAG,IAAA,CAAK,UAAA;gBAC3B;YACF,GACA,OAAO,aAAa;gBAClB,MAAM,kBAAkB,mBAAmB,OAAO;gBAClD,IAAI,oBAEY,KAAA;gBAEhB,IAAI,gBAAgB,OAAA,IAAW,gBAAgB,iBAAA,EAAmB;oBAChE,oBACE,gBAAgB,OAAA,IAAW,gBAAgB,iBAAA;gBAC/C;gBACA,MAAM,UAA2B;oBAC/B,GAAI,MAAM,IAAA,CAAK,WAAA;oBACf,UAAU,IAAA,CAAK,QAAA;oBACf,GAAG,eAAA;gBACL;gBACA,SAAS,KAAA,GAAQ;gBACjB,MAAM,WAAW,MAAA,CAAA,GAAM,aAAA,QAAA,EAAS,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;oBACrD,GAAG,OAAA;oBACH,SAAS;gBACX,CAAC;gBACD,IAAA,CAAK,WAAA,GAAc,QAAQ,OAAA,CAAQ;oBACjC,GAAI,MAAM,IAAA,CAAK,WAAA;oBAAA,iEAAA;oBAEf,OAAO,UAAU,SAAS,OAAO,IAAI,CAAC,KAAO,GAAG,IAAI;oBACpD,YAAY,UAAU,SAAS;oBAC/B,QAAQ,UAAU,SAAS;gBAC7B,CAAC;gBACD,MAAM,IAAA,CAAK,cAAA,CAAe,SAAS,QAAQ;gBAC3C,SAAS,MAAA,GAAS,KAAK,SAAA,CAAU,QAAQ;gBACzC,OAAO;YACT;IAGN;IAEA,WAIE,OAAA,EACoC;QACpC,MAAM,UAAU,IAAI,aAAA,OAAA,CAA+B;QACnD,MAAM,kBAAkB,mBAAmB,OAAO;QAElD,MAAM,OAAO,IAAA,CAAK,IAAA,CAAK;YACrB,GAAG,eAAA;YACH,SAAS,CAAC,QAAU,QAAQ,IAAA,CAAK,KAAK;QACxC,CAAC;QACD,KAAK,IAAA,CACH,IAAM,QAAQ,KAAA,CAAM,GACpB,CAAC,MAAQ,QAAQ,KAAA,CAAM,GAAG;QAG5B,OAAO;YACL,UAAU;YACV,QAAQ;QACV;IACF;IAEA,IAAI,WAA0B;QAC5B,OAAO,IAAA,CAAK,SAAA,IAAa,CAAC,CAAA;IAC5B;IAEA,MAAc,eAAe,QAAA,EAAwC;QACnE,IAAA,CAAK,SAAA,GAAY;QACjB,MAAM,IAAA,CAAK,OAAA,CAAQ,cAAA,CAAe,IAAA,CAAK,UAAA,EAAY,QAAQ;IAC7D;AACF;AAEA,SAAS,YAAY,IAAA,EAAsB;IACzC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAM,EAAE,QAAA,EAAU,QAAQ;AACjD;AAEA,SAAS,YAAY,IAAA,EAAsB;IACzC,OAAO,MAAM,OAAO,CAAC,IAAM,EAAE,QAAA,EAAU,QAAQ;AACjD;AAEA,SAAS,cAAc,IAAA,EAAsB;IAC3C,OAAO,MAAM,OAAO,CAAC,IAAM,CAAC,EAAE,QAAA,EAAU,QAAQ;AAClD;AAEA,SAAS,mBAIP,OAAA,EACuC;IACvC,IAAI;IAGJ,IAAI,OAAO,YAAY,UAAU;QAC/B,kBAAkB;YAChB,QAAQ;QACV;IACF,OAAA,IAAW,MAAM,OAAA,CAAQ,OAAO,GAAG;QAEjC,kBAAkB;YAChB,QAAQ;QACV;IACF,OAAO;QACL,kBAAkB;IACpB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2675, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/session.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { z } from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { v4 as uuidv4 } from 'uuid';\nimport {\n  Chat,\n  MAIN_THREAD,\n  type ChatOptions,\n  type PromptRenderOptions,\n} from './chat.js';\nimport {\n  Message,\n  isExecutablePrompt,\n  tagAsPreamble,\n  type ExecutablePrompt,\n  type GenerateOptions,\n  type MessageData,\n  type PromptGenerateOptions,\n} from './index.js';\n\nexport type BaseGenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = Omit<GenerateOptions<O, CustomOptions>, 'prompt'>;\n\nexport interface SessionOptions<S = any> {\n  /** Session store implementation for persisting the session state. */\n  store?: SessionStore<S>;\n  /** Initial state of the session.  */\n  initialState?: S;\n  /** Custom session Id. */\n  sessionId?: string;\n}\n\n/**\n * Session encapsulates a statful execution environment for chat.\n * Chat session executed within a session in this environment will have acesss to\n * session session convesation history.\n *\n * ```ts\n * const ai = genkit({...});\n * const chat = ai.chat(); // create a Session\n * let response = await chat.send('hi'); // session/history aware conversation\n * response = await chat.send('tell me a story');\n * ```\n */\nexport class Session<S = any> {\n  readonly id: string;\n  private sessionData?: SessionData<S>;\n  private store: SessionStore<S>;\n\n  constructor(\n    readonly registry: Registry,\n    options?: {\n      id?: string;\n      stateSchema?: S;\n      sessionData?: SessionData<S>;\n      store?: SessionStore<S>;\n    }\n  ) {\n    this.id = options?.id ?? uuidv4();\n    this.sessionData = options?.sessionData ?? {\n      id: this.id,\n    };\n    if (!this.sessionData) {\n      this.sessionData = { id: this.id };\n    }\n    if (!this.sessionData.threads) {\n      this.sessionData!.threads = {};\n    }\n    this.store = options?.store ?? new InMemorySessionStore<S>();\n  }\n\n  get state(): S | undefined {\n    return this.sessionData!.state;\n  }\n\n  /**\n   * Update session state data.\n   */\n  async updateState(data: S): Promise<void> {\n    let sessionData = this.sessionData;\n    if (!sessionData) {\n      sessionData = {} as SessionData<S>;\n    }\n    sessionData.state = data;\n    this.sessionData = sessionData;\n\n    await this.store.save(this.id, sessionData);\n  }\n\n  /**\n   * Update messages for a given thread.\n   */\n  async updateMessages(thread: string, messages: MessageData[]): Promise<void> {\n    let sessionData = this.sessionData;\n    if (!sessionData) {\n      sessionData = {} as SessionData<S>;\n    }\n    if (!sessionData.threads) {\n      sessionData.threads = {};\n    }\n    sessionData.threads[thread] = messages.map((m: any) =>\n      m.toJSON ? m.toJSON() : m\n    );\n    this.sessionData = sessionData;\n\n    await this.store.save(this.id, sessionData);\n  }\n\n  /**\n   * Create a chat session with the provided options.\n   *\n   * ```ts\n   * const session = ai.createSession({});\n   * const chat = session.chat({\n   *   system: 'talk like a pirate',\n   * })\n   * let response = await chat.send('tell me a joke');\n   * response = await chat.send('another one');\n   * ```\n   */\n  chat<I>(options?: ChatOptions<I, S>): Chat;\n\n  /**\n   * Create a chat session with the provided preamble.\n   *\n   * ```ts\n   * const triageAgent = ai.definePrompt({\n   *   system: 'help the user triage a problem',\n   * })\n   * const session = ai.createSession({});\n   * const chat = session.chat(triageAgent);\n   * const { text } = await chat.send('my phone feels hot');\n   * ```\n   */\n  chat<I>(preamble: ExecutablePrompt<I>, options?: ChatOptions<I, S>): Chat;\n\n  /**\n   * Craete a separate chat conversation (\"thread\") within the given preamble.\n   *\n   * ```ts\n   * const session = ai.createSession({});\n   * const lawyerChat = session.chat('lawyerThread', {\n   *   system: 'talk like a lawyer',\n   * });\n   * const pirateChat = session.chat('pirateThread', {\n   *   system: 'talk like a pirate',\n   * });\n   * await lawyerChat.send('tell me a joke');\n   * await pirateChat.send('tell me a joke');\n   * ```\n   */\n  chat<I>(\n    threadName: string,\n    preamble: ExecutablePrompt<I>,\n    options?: ChatOptions<I, S>\n  ): Chat;\n\n  /**\n   * Craete a separate chat conversation (\"thread\").\n   *\n   * ```ts\n   * const session = ai.createSession({});\n   * const lawyerChat = session.chat('lawyerThread', {\n   *   system: 'talk like a lawyer',\n   * });\n   * const pirateChat = session.chat('pirateThread', {\n   *   system: 'talk like a pirate',\n   * });\n   * await lawyerChat.send('tell me a joke');\n   * await pirateChat.send('tell me a joke');\n   * ```\n   */\n  chat<I>(threadName: string, options?: ChatOptions<I, S>): Chat;\n\n  chat<I>(\n    optionsOrPreambleOrThreadName?:\n      | ChatOptions<I, S>\n      | string\n      | ExecutablePrompt<I>,\n    maybeOptionsOrPreamble?: ChatOptions<I, S> | ExecutablePrompt<I>,\n    maybeOptions?: ChatOptions<I, S>\n  ): Chat {\n    return runWithSession(this.registry, this, () => {\n      let options: ChatOptions<S> | undefined;\n      let threadName = MAIN_THREAD;\n      let preamble: ExecutablePrompt<I> | undefined;\n\n      if (optionsOrPreambleOrThreadName) {\n        if (typeof optionsOrPreambleOrThreadName === 'string') {\n          threadName = optionsOrPreambleOrThreadName as string;\n        } else if (isExecutablePrompt(optionsOrPreambleOrThreadName)) {\n          preamble = optionsOrPreambleOrThreadName as ExecutablePrompt<I>;\n        } else {\n          options = optionsOrPreambleOrThreadName as ChatOptions<I, S>;\n        }\n      }\n      if (maybeOptionsOrPreamble) {\n        if (isExecutablePrompt(maybeOptionsOrPreamble)) {\n          preamble = maybeOptionsOrPreamble as ExecutablePrompt<I>;\n        } else {\n          options = maybeOptionsOrPreamble as ChatOptions<I, S>;\n        }\n      }\n      if (maybeOptions) {\n        options = maybeOptions as ChatOptions<I, S>;\n      }\n\n      let requestBase: Promise<BaseGenerateOptions>;\n      if (preamble) {\n        const renderOptions = options as PromptRenderOptions<I>;\n        requestBase = preamble\n          .render(renderOptions?.input, renderOptions as PromptGenerateOptions)\n          .then((rb) => {\n            return {\n              ...rb,\n              messages: tagAsPreamble(rb?.messages),\n            };\n          });\n      } else {\n        const baseOptions = { ...(options as BaseGenerateOptions) };\n        const messages: MessageData[] = [];\n        if (baseOptions.system) {\n          messages.push({\n            role: 'system',\n            content: Message.parseContent(baseOptions.system),\n          });\n        }\n        delete baseOptions.system;\n        if (baseOptions.messages) {\n          messages.push(...baseOptions.messages);\n        }\n        baseOptions.messages = tagAsPreamble(messages);\n\n        requestBase = Promise.resolve(baseOptions);\n      }\n      return new Chat(this, requestBase, {\n        thread: threadName,\n        id: this.id,\n        messages:\n          (this.sessionData?.threads &&\n            this.sessionData?.threads[threadName]) ??\n          [],\n      });\n    });\n  }\n\n  /**\n   * Executes provided function within this session context allowing calling\n   * `ai.currentSession().state`\n   */\n  run<O>(fn: () => O) {\n    return runWithSession(this.registry, this, fn);\n  }\n\n  toJSON() {\n    return this.sessionData;\n  }\n}\n\nexport interface SessionData<S = any> {\n  id: string;\n  state?: S;\n  threads?: Record<string, MessageData[]>;\n}\n\nconst sessionAlsKey = 'ai.session';\n\n/**\n * Executes provided function within the provided session state.\n */\nexport function runWithSession<S = any, O = any>(\n  registry: Registry,\n  session: Session<S>,\n  fn: () => O\n): O {\n  return registry.asyncStore.run(sessionAlsKey, session, fn);\n}\n\n/** Returns the current session. */\nexport function getCurrentSession<S = any>(\n  registry: Registry\n): Session<S> | undefined {\n  return registry.asyncStore.getStore(sessionAlsKey);\n}\n\n/** Throw when session state errors occur, ex. missing state, etc. */\nexport class SessionError extends Error {\n  constructor(msg: string) {\n    super(msg);\n  }\n}\n\n/** Session store persists session data such as state and chat messages. */\nexport interface SessionStore<S = any> {\n  get(sessionId: string): Promise<SessionData<S> | undefined>;\n\n  save(sessionId: string, data: Omit<SessionData<S>, 'id'>): Promise<void>;\n}\n\nexport function inMemorySessionStore() {\n  return new InMemorySessionStore();\n}\n\nclass InMemorySessionStore<S = any> implements SessionStore<S> {\n  private data: Record<string, SessionData<S>> = {};\n\n  async get(sessionId: string): Promise<SessionData<S> | undefined> {\n    return this.data[sessionId];\n  }\n\n  async save(sessionId: string, sessionData: SessionData<S>): Promise<void> {\n    this.data[sessionId] = sessionData;\n  }\n}\n"], "names": ["uuidv4"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,SAAA,IAAA;IAAA,cAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAkBA,IAAA,cAA6B;AAC7B,IAAA,cAKO;AACP,IAAA,eAQO;AA4BA,MAAM,QAAiB;IAK5B,YACW,QAAA,EACT,OAAA,CAMA;QAPS,IAAA,CAAA,QAAA,GAAA;QAQT,IAAA,CAAK,EAAA,GAAK,SAAS,MAAA,CAAA,GAAM,YAAAA,EAAAA,EAAO;QAChC,IAAA,CAAK,WAAA,GAAc,SAAS,eAAe;YACzC,IAAI,IAAA,CAAK,EAAA;QACX;QACA,IAAI,CAAC,IAAA,CAAK,WAAA,EAAa;YACrB,IAAA,CAAK,WAAA,GAAc;gBAAE,IAAI,IAAA,CAAK,EAAA;YAAG;QACnC;QACA,IAAI,CAAC,IAAA,CAAK,WAAA,CAAY,OAAA,EAAS;YAC7B,IAAA,CAAK,WAAA,CAAa,OAAA,GAAU,CAAC;QAC/B;QACA,IAAA,CAAK,KAAA,GAAQ,SAAS,SAAS,IAAI,qBAAwB;IAC7D;IAxBS,GAAA;IACD,YAAA;IACA,MAAA;IAwBR,IAAI,QAAuB;QACzB,OAAO,IAAA,CAAK,WAAA,CAAa,KAAA;IAC3B;IAAA;;GAAA,GAKA,MAAM,YAAY,IAAA,EAAwB;QACxC,IAAI,cAAc,IAAA,CAAK,WAAA;QACvB,IAAI,CAAC,aAAa;YAChB,cAAc,CAAC;QACjB;QACA,YAAY,KAAA,GAAQ;QACpB,IAAA,CAAK,WAAA,GAAc;QAEnB,MAAM,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,EAAA,EAAI,WAAW;IAC5C;IAAA;;GAAA,GAKA,MAAM,eAAe,MAAA,EAAgB,QAAA,EAAwC;QAC3E,IAAI,cAAc,IAAA,CAAK,WAAA;QACvB,IAAI,CAAC,aAAa;YAChB,cAAc,CAAC;QACjB;QACA,IAAI,CAAC,YAAY,OAAA,EAAS;YACxB,YAAY,OAAA,GAAU,CAAC;QACzB;QACA,YAAY,OAAA,CAAQ,MAAM,CAAA,GAAI,SAAS,GAAA,CAAI,CAAC,IAC1C,EAAE,MAAA,GAAS,EAAE,MAAA,CAAO,IAAI;QAE1B,IAAA,CAAK,WAAA,GAAc;QAEnB,MAAM,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,EAAA,EAAI,WAAW;IAC5C;IAoEA,KACE,6BAAA,EAIA,sBAAA,EACA,YAAA,EACM;QACN,OAAO,eAAe,IAAA,CAAK,QAAA,EAAU,IAAA,EAAM,MAAM;YAC/C,IAAI;YACJ,IAAI,aAAa,YAAA,WAAA;YACjB,IAAI;YAEJ,IAAI,+BAA+B;gBACjC,IAAI,OAAO,kCAAkC,UAAU;oBACrD,aAAa;gBACf,OAAA,IAAA,CAAA,GAAW,aAAA,kBAAA,EAAmB,6BAA6B,GAAG;oBAC5D,WAAW;gBACb,OAAO;oBACL,UAAU;gBACZ;YACF;YACA,IAAI,wBAAwB;gBAC1B,IAAA,CAAA,GAAI,aAAA,kBAAA,EAAmB,sBAAsB,GAAG;oBAC9C,WAAW;gBACb,OAAO;oBACL,UAAU;gBACZ;YACF;YACA,IAAI,cAAc;gBAChB,UAAU;YACZ;YAEA,IAAI;YACJ,IAAI,UAAU;gBACZ,MAAM,gBAAgB;gBACtB,cAAc,SACX,MAAA,CAAO,eAAe,OAAO,aAAsC,EACnE,IAAA,CAAK,CAAC,OAAO;oBACZ,OAAO;wBACL,GAAG,EAAA;wBACH,UAAA,CAAA,GAAU,aAAA,aAAA,EAAc,IAAI,QAAQ;oBACtC;gBACF,CAAC;YACL,OAAO;gBACL,MAAM,cAAc;oBAAE,GAAI,OAAA;gBAAgC;gBAC1D,MAAM,WAA0B,CAAC,CAAA;gBACjC,IAAI,YAAY,MAAA,EAAQ;oBACtB,SAAS,IAAA,CAAK;wBACZ,MAAM;wBACN,SAAS,aAAA,OAAA,CAAQ,YAAA,CAAa,YAAY,MAAM;oBAClD,CAAC;gBACH;gBACA,OAAO,YAAY,MAAA;gBACnB,IAAI,YAAY,QAAA,EAAU;oBACxB,SAAS,IAAA,CAAK,GAAG,YAAY,QAAQ;gBACvC;gBACA,YAAY,QAAA,GAAA,CAAA,GAAW,aAAA,aAAA,EAAc,QAAQ;gBAE7C,cAAc,QAAQ,OAAA,CAAQ,WAAW;YAC3C;YACA,OAAO,IAAI,YAAA,IAAA,CAAK,IAAA,EAAM,aAAa;gBACjC,QAAQ;gBACR,IAAI,IAAA,CAAK,EAAA;gBACT,UAAA,CACG,IAAA,CAAK,WAAA,EAAa,WACjB,IAAA,CAAK,WAAA,EAAa,OAAA,CAAQ,UAAU,CAAA,KACtC,CAAC,CAAA;YACL,CAAC;QACH,CAAC;IACH;IAAA;;;GAAA,GAMA,IAAO,EAAA,EAAa;QAClB,OAAO,eAAe,IAAA,CAAK,QAAA,EAAU,IAAA,EAAM,EAAE;IAC/C;IAEA,SAAS;QACP,OAAO,IAAA,CAAK,WAAA;IACd;AACF;AAQA,MAAM,gBAAgB;AAKf,SAAS,eACd,QAAA,EACA,OAAA,EACA,EAAA,EACG;IACH,OAAO,SAAS,UAAA,CAAW,GAAA,CAAI,eAAe,SAAS,EAAE;AAC3D;AAGO,SAAS,kBACd,QAAA,EACwB;IACxB,OAAO,SAAS,UAAA,CAAW,QAAA,CAAS,aAAa;AACnD;AAGO,MAAM,qBAAqB,MAAM;IACtC,YAAY,GAAA,CAAa;QACvB,KAAA,CAAM,GAAG;IACX;AACF;AASO,SAAS,uBAAuB;IACrC,OAAO,IAAI,qBAAqB;AAClC;AAEA,MAAM,qBAAyD;IACrD,OAAuC,CAAC,EAAA;IAEhD,MAAM,IAAI,SAAA,EAAwD;QAChE,OAAO,IAAA,CAAK,IAAA,CAAK,SAAS,CAAA;IAC5B;IAEA,MAAM,KAAK,SAAA,EAAmB,WAAA,EAA4C;QACxE,IAAA,CAAK,IAAA,CAAK,SAAS,CAAA,GAAI;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2863, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/prompt.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenkitError,\n  defineActionAsync,\n  getContext,\n  stripUndefinedProps,\n  type Action,\n  type ActionAsyncParams,\n  type ActionContext,\n  type JSONSchema7,\n  type z,\n} from '@genkit-ai/core';\nimport { lazy } from '@genkit-ai/core/async';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from '@genkit-ai/core/tracing';\nimport { Message as DpMessage, PromptFunction } from 'dotprompt';\nimport { existsSync, readFileSync, readdirSync } from 'fs';\nimport { basename, join, resolve } from 'path';\nimport type { DocumentData } from './document.js';\nimport {\n  generate,\n  generateStream,\n  toGenerateActionOptions,\n  toGenerateRequest,\n  type GenerateOptions,\n  type GenerateResponse,\n  type GenerateStreamResponse,\n  type OutputOptions,\n  type ToolChoice,\n} from './generate.js';\nimport { Message } from './message.js';\nimport {\n  GenerateActionOptionsSchema,\n  type GenerateActionOptions,\n  type GenerateRequest,\n  type GenerateRequestSchema,\n  type GenerateResponseChunkSchema,\n  type GenerateResponseSchema,\n  type MessageData,\n  type ModelAction,\n  type ModelArgument,\n  type ModelMiddleware,\n  type ModelReference,\n  type Part,\n} from './model.js';\nimport { getCurrentSession, type Session } from './session.js';\nimport type { ToolAction, ToolArgument } from './tool.js';\n\n/**\n * Prompt action.\n */\nexport type PromptAction<I extends z.ZodTypeAny = z.ZodTypeAny> = Action<\n  I,\n  typeof GenerateRequestSchema,\n  z.ZodNever\n> & {\n  __action: {\n    metadata: {\n      type: 'prompt';\n    };\n  };\n  __executablePrompt: ExecutablePrompt<I>;\n};\n\nexport function isPromptAction(action: Action): action is PromptAction {\n  return action.__action.metadata?.type === 'prompt';\n}\n\n/**\n * Prompt action.\n */\nexport type ExecutablePromptAction<I extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<\n    I,\n    typeof GenerateResponseSchema,\n    typeof GenerateResponseChunkSchema\n  > & {\n    __action: {\n      metadata: {\n        type: 'executablePrompt';\n      };\n    };\n    __executablePrompt: ExecutablePrompt<I>;\n  };\n\n/**\n * Configuration for a prompt action.\n */\nexport interface PromptConfig<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  name: string;\n  variant?: string;\n  model?: ModelArgument<CustomOptions>;\n  config?: z.infer<CustomOptions>;\n  description?: string;\n  input?: {\n    schema?: I;\n    jsonSchema?: JSONSchema7;\n  };\n  system?: string | Part | Part[] | PartsResolver<z.infer<I>>;\n  prompt?: string | Part | Part[] | PartsResolver<z.infer<I>>;\n  messages?: string | MessageData[] | MessagesResolver<z.infer<I>>;\n  docs?: DocumentData[] | DocsResolver<z.infer<I>>;\n  output?: OutputOptions<O>;\n  maxTurns?: number;\n  returnToolRequests?: boolean;\n  metadata?: Record<string, any>;\n  tools?: ToolArgument[];\n  toolChoice?: ToolChoice;\n  use?: ModelMiddleware[];\n  context?: ActionContext;\n}\n\n/**\n * Generate options of a prompt.\n */\nexport type PromptGenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = Omit<GenerateOptions<O, CustomOptions>, 'prompt' | 'system'>;\n\n/**\n * A prompt that can be executed as a function.\n */\nexport interface ExecutablePrompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /**\n   * Generates a response by rendering the prompt template with given user input and then calling the model.\n   *\n   * @param input Prompt inputs.\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns the model response as a promise of `GenerateStreamResponse`.\n   */\n  (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateResponse<z.infer<O>>>;\n\n  /**\n   * Generates a response by rendering the prompt template with given user input and then calling the model.\n   * @param input Prompt inputs.\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns the model response as a promise of `GenerateStreamResponse`.\n   */\n  stream(\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): GenerateStreamResponse<z.infer<O>>;\n\n  /**\n   * Renders the prompt template based on user input.\n   *\n   * @param opt Options for the prompt template, including user input variables and custom model configuration options.\n   * @returns a `GenerateOptions` object to be used with the `generate()` function from @genkit-ai/ai.\n   */\n  render(\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateOptions<O, CustomOptions>>;\n\n  /**\n   * Returns the prompt usable as a tool.\n   */\n  asTool(): Promise<ToolAction>;\n}\n\nexport type PartsResolver<I, S = any> = (\n  input: I,\n  options: {\n    state?: S;\n    context: ActionContext;\n  }\n) => Part[] | Promise<string | Part | Part[]>;\n\nexport type MessagesResolver<I, S = any> = (\n  input: I,\n  options: {\n    history?: MessageData[];\n    state?: S;\n    context: ActionContext;\n  }\n) => MessageData[] | Promise<MessageData[]>;\n\nexport type DocsResolver<I, S = any> = (\n  input: I,\n  options: {\n    context: ActionContext;\n    state?: S;\n  }\n) => DocumentData[] | Promise<DocumentData[]>;\n\ninterface PromptCache {\n  userPrompt?: PromptFunction;\n  system?: PromptFunction;\n  messages?: PromptFunction;\n}\n\n/**\n * Defines a prompt which can be used to generate content or render a request.\n *\n * @returns The new `ExecutablePrompt`.\n */\nexport function definePrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: PromptConfig<I, O, CustomOptions>\n): ExecutablePrompt<z.infer<I>, O, CustomOptions> {\n  return definePromptAsync(\n    registry,\n    `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n    Promise.resolve(options)\n  );\n}\n\nfunction definePromptAsync<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  optionsPromise: PromiseLike<PromptConfig<I, O, CustomOptions>>\n): ExecutablePrompt<z.infer<I>, O, CustomOptions> {\n  const promptCache = {} as PromptCache;\n\n  const renderOptionsFn = async (\n    input: z.infer<I>,\n    renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n  ): Promise<GenerateOptions> => {\n    return await runInNewSpan(\n      registry,\n      {\n        metadata: {\n          name: 'render',\n          input,\n        },\n        labels: {\n          [SPAN_TYPE_ATTR]: 'promptTemplate',\n        },\n      },\n      async (metadata) => {\n        const messages: MessageData[] = [];\n        renderOptions = { ...renderOptions }; // make a copy, we will be trimming\n        const session = getCurrentSession(registry);\n        const resolvedOptions = await optionsPromise;\n\n        // order of these matters:\n        await renderSystemPrompt(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          promptCache,\n          renderOptions\n        );\n        await renderMessages(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          renderOptions,\n          promptCache\n        );\n        await renderUserPrompt(\n          registry,\n          session,\n          input,\n          messages,\n          resolvedOptions,\n          promptCache,\n          renderOptions\n        );\n\n        let docs: DocumentData[] | undefined;\n        if (typeof resolvedOptions.docs === 'function') {\n          docs = await resolvedOptions.docs(input, {\n            state: session?.state,\n            context: renderOptions?.context || getContext(registry) || {},\n          });\n        } else {\n          docs = resolvedOptions.docs;\n        }\n\n        const opts: GenerateOptions = stripUndefinedProps({\n          model: resolvedOptions.model,\n          maxTurns: resolvedOptions.maxTurns,\n          messages,\n          docs,\n          tools: resolvedOptions.tools,\n          returnToolRequests: resolvedOptions.returnToolRequests,\n          toolChoice: resolvedOptions.toolChoice,\n          context: resolvedOptions.context,\n          output: resolvedOptions.output,\n          use: resolvedOptions.use,\n          ...stripUndefinedProps(renderOptions),\n          config: {\n            ...resolvedOptions?.config,\n            ...renderOptions?.config,\n          },\n        });\n        // if config is empty and it was not explicitly passed in, we delete it, don't want {}\n        if (Object.keys(opts.config).length === 0 && !renderOptions?.config) {\n          delete opts.config;\n        }\n        metadata.output = opts;\n        return opts;\n      }\n    );\n  };\n  const rendererActionConfig = lazy(() =>\n    optionsPromise.then((options: PromptConfig<I, O, CustomOptions>) => {\n      const metadata = promptMetadata(options);\n      return {\n        name: `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n        inputJsonSchema: options.input?.jsonSchema,\n        inputSchema: options.input?.schema,\n        description: options.description,\n        actionType: 'prompt',\n        metadata,\n        fn: async (\n          input: z.infer<I>\n        ): Promise<GenerateRequest<z.ZodTypeAny>> => {\n          return toGenerateRequest(\n            registry,\n            await renderOptionsFn(input, undefined)\n          );\n        },\n      } as ActionAsyncParams<any, any, any>;\n    })\n  );\n  const rendererAction = defineActionAsync(\n    registry,\n    'prompt',\n    name,\n    rendererActionConfig,\n    (action) => {\n      (action as PromptAction<I>).__executablePrompt =\n        executablePrompt as never as ExecutablePrompt<z.infer<I>>;\n    }\n  ) as Promise<PromptAction<I>>;\n\n  const executablePromptActionConfig = lazy(() =>\n    optionsPromise.then((options: PromptConfig<I, O, CustomOptions>) => {\n      const metadata = promptMetadata(options);\n      return {\n        name: `${options.name}${options.variant ? `.${options.variant}` : ''}`,\n        inputJsonSchema: options.input?.jsonSchema,\n        inputSchema: options.input?.schema,\n        outputSchema: GenerateActionOptionsSchema,\n        description: options.description,\n        actionType: 'executable-prompt',\n        metadata,\n        fn: async (input: z.infer<I>): Promise<GenerateActionOptions> => {\n          return await toGenerateActionOptions(\n            registry,\n            await renderOptionsFn(input, undefined)\n          );\n        },\n      } as ActionAsyncParams<any, any, any>;\n    })\n  );\n\n  defineActionAsync(\n    registry,\n    'executable-prompt',\n    name,\n    executablePromptActionConfig,\n    (action) => {\n      (action as ExecutablePromptAction<I>).__executablePrompt =\n        executablePrompt as never as ExecutablePrompt<z.infer<I>>;\n    }\n  ) as Promise<ExecutablePromptAction<I>>;\n\n  const executablePrompt = wrapInExecutablePrompt(\n    registry,\n    renderOptionsFn,\n    rendererAction\n  );\n\n  return executablePrompt;\n}\n\nfunction promptMetadata(options: PromptConfig<any, any, any>) {\n  const metadata = {\n    ...options.metadata,\n    prompt: {\n      ...options.metadata?.prompt,\n      config: options.config,\n      input: {\n        schema: options.input ? toJsonSchema(options.input) : undefined,\n      },\n      name: options.name.includes('.')\n        ? options.name.split('.')[0]\n        : options.name,\n      model: modelName(options.model),\n    },\n    type: 'prompt',\n  };\n\n  if (options.variant) {\n    metadata.prompt.variant = options.variant;\n  }\n\n  return metadata;\n}\n\nfunction wrapInExecutablePrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  renderOptionsFn: (\n    input: z.infer<I>,\n    renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n  ) => Promise<GenerateOptions>,\n  rendererAction: Promise<PromptAction<I>>\n) {\n  const executablePrompt = (async (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateResponse<z.infer<O>>> => {\n    return await runInNewSpan(\n      registry,\n      {\n        metadata: {\n          name: (await rendererAction).__action.name,\n          input,\n        },\n        labels: {\n          [SPAN_TYPE_ATTR]: 'dotprompt',\n        },\n      },\n      async (metadata) => {\n        const output = await generate(registry, {\n          ...(await renderOptionsFn(input, opts)),\n        });\n        metadata.output = output;\n        return output;\n      }\n    );\n  }) as ExecutablePrompt<z.infer<I>, O, CustomOptions>;\n\n  executablePrompt.render = async (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): Promise<GenerateOptions<O, CustomOptions>> => {\n    return {\n      ...(await renderOptionsFn(input, opts)),\n    } as GenerateOptions<O, CustomOptions>;\n  };\n\n  executablePrompt.stream = (\n    input?: I,\n    opts?: PromptGenerateOptions<O, CustomOptions>\n  ): GenerateStreamResponse<z.infer<O>> => {\n    return generateStream(registry, renderOptionsFn(input, opts));\n  };\n\n  executablePrompt.asTool = async (): Promise<ToolAction<I, O>> => {\n    return (await rendererAction) as unknown as ToolAction<I, O>;\n  };\n  return executablePrompt;\n}\n\nasync function renderSystemPrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  promptCache: PromptCache,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n) {\n  if (typeof options.system === 'function') {\n    messages.push({\n      role: 'system',\n      content: normalizeParts(\n        await options.system(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n        })\n      ),\n    });\n  } else if (typeof options.system === 'string') {\n    // memoize compiled prompt\n    if (!promptCache.system) {\n      promptCache.system = await registry.dotprompt.compile(options.system);\n    }\n    messages.push({\n      role: 'system',\n      content: await renderDotpromptToParts(\n        registry,\n        promptCache.system,\n        input,\n        session,\n        options,\n        renderOptions\n      ),\n    });\n  } else if (options.system) {\n    messages.push({\n      role: 'system',\n      content: normalizeParts(options.system),\n    });\n  }\n}\n\nasync function renderMessages<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  renderOptions: PromptGenerateOptions<O, CustomOptions>,\n  promptCache: PromptCache\n) {\n  if (options.messages) {\n    if (typeof options.messages === 'function') {\n      messages.push(\n        ...(await options.messages(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n          history: renderOptions?.messages,\n        }))\n      );\n    } else if (typeof options.messages === 'string') {\n      // memoize compiled prompt\n      if (!promptCache.messages) {\n        promptCache.messages = await registry.dotprompt.compile(\n          options.messages\n        );\n      }\n      const rendered = await promptCache.messages({\n        input,\n        context: {\n          ...(renderOptions?.context || getContext(registry)),\n          state: session?.state,\n        },\n        messages: renderOptions?.messages?.map((m) =>\n          Message.parseData(m)\n        ) as DpMessage[],\n      });\n      messages.push(...(rendered.messages as MessageData[]));\n    } else {\n      messages.push(...options.messages);\n    }\n  } else {\n    if (renderOptions.messages) {\n      messages.push(...renderOptions.messages);\n    }\n  }\n  if (renderOptions?.messages) {\n    // delete messages from opts so that we don't override messages downstream\n    delete renderOptions.messages;\n  }\n}\n\nasync function renderUserPrompt<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  session: Session | undefined,\n  input: z.infer<I>,\n  messages: MessageData[],\n  options: PromptConfig<I, O, CustomOptions>,\n  promptCache: PromptCache,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n) {\n  if (typeof options.prompt === 'function') {\n    messages.push({\n      role: 'user',\n      content: normalizeParts(\n        await options.prompt(input, {\n          state: session?.state,\n          context: renderOptions?.context || getContext(registry) || {},\n        })\n      ),\n    });\n  } else if (typeof options.prompt === 'string') {\n    // memoize compiled prompt\n    if (!promptCache.userPrompt) {\n      promptCache.userPrompt = await registry.dotprompt.compile(options.prompt);\n    }\n    messages.push({\n      role: 'user',\n      content: await renderDotpromptToParts(\n        registry,\n        promptCache.userPrompt,\n        input,\n        session,\n        options,\n        renderOptions\n      ),\n    });\n  } else if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: normalizeParts(options.prompt),\n    });\n  }\n}\n\nfunction modelName(\n  modelArg: ModelArgument<any> | undefined\n): string | undefined {\n  if (modelArg === undefined) {\n    return undefined;\n  }\n  if (typeof modelArg === 'string') {\n    return modelArg;\n  }\n  if ((modelArg as ModelReference<any>).name) {\n    return (modelArg as ModelReference<any>).name;\n  }\n  return (modelArg as ModelAction).__action.name;\n}\n\nfunction normalizeParts(parts: string | Part | Part[]): Part[] {\n  if (Array.isArray(parts)) return parts;\n  if (typeof parts === 'string') {\n    return [\n      {\n        text: parts,\n      },\n    ];\n  }\n  return [parts as Part];\n}\n\nasync function renderDotpromptToParts<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  promptFn: PromptFunction,\n  input: any,\n  session: Session | undefined,\n  options: PromptConfig<I, O, CustomOptions>,\n  renderOptions: PromptGenerateOptions<O, CustomOptions> | undefined\n): Promise<Part[]> {\n  const renderred = await promptFn({\n    input,\n    context: {\n      ...(renderOptions?.context || getContext(registry)),\n      state: session?.state,\n    },\n  });\n  if (renderred.messages.length !== 1) {\n    throw new Error('parts tempate must produce only one message');\n  }\n  return renderred.messages[0].content;\n}\n\n/**\n * Checks whether the provided object is an executable prompt.\n */\nexport function isExecutablePrompt(obj: any): obj is ExecutablePrompt {\n  return (\n    !!(obj as ExecutablePrompt)?.render &&\n    !!(obj as ExecutablePrompt)?.asTool &&\n    !!(obj as ExecutablePrompt)?.stream\n  );\n}\n\nexport function loadPromptFolder(\n  registry: Registry,\n  dir = './prompts',\n  ns: string\n): void {\n  const promptsPath = resolve(dir);\n  if (existsSync(promptsPath)) {\n    loadPromptFolderRecursively(registry, dir, ns, '');\n  }\n}\nexport function loadPromptFolderRecursively(\n  registry: Registry,\n  dir: string,\n  ns: string,\n  subDir: string\n): void {\n  const promptsPath = resolve(dir);\n  const dirEnts = readdirSync(join(promptsPath, subDir), {\n    withFileTypes: true,\n  });\n  for (const dirEnt of dirEnts) {\n    const parentPath = join(promptsPath, subDir);\n    const fileName = dirEnt.name;\n    if (dirEnt.isFile() && fileName.endsWith('.prompt')) {\n      if (fileName.startsWith('_')) {\n        const partialName = fileName.substring(1, fileName.length - 7);\n        definePartial(\n          registry,\n          partialName,\n          readFileSync(join(parentPath, fileName), {\n            encoding: 'utf8',\n          })\n        );\n        logger.debug(\n          `Registered Dotprompt partial \"${partialName}\" from \"${join(parentPath, fileName)}\"`\n        );\n      } else {\n        // If this prompt is in a subdirectory, we need to include that\n        // in the namespace to prevent naming conflicts.\n        loadPrompt(\n          registry,\n          promptsPath,\n          fileName,\n          subDir ? `${subDir}/` : '',\n          ns\n        );\n      }\n    } else if (dirEnt.isDirectory()) {\n      loadPromptFolderRecursively(registry, dir, ns, join(subDir, fileName));\n    }\n  }\n}\n\nexport function definePartial(\n  registry: Registry,\n  name: string,\n  source: string\n) {\n  registry.dotprompt.definePartial(name, source);\n}\n\nexport function defineHelper(\n  registry: Registry,\n  name: string,\n  fn: Handlebars.HelperDelegate\n) {\n  registry.dotprompt.defineHelper(name, fn);\n}\n\nfunction loadPrompt(\n  registry: Registry,\n  path: string,\n  filename: string,\n  prefix = '',\n  ns = 'dotprompt'\n): void {\n  let name = `${prefix ?? ''}${basename(filename, '.prompt')}`;\n  let variant: string | null = null;\n  if (name.includes('.')) {\n    const parts = name.split('.');\n    name = parts[0];\n    variant = parts[1];\n  }\n  const source = readFileSync(join(path, prefix ?? '', filename), 'utf8');\n  const parsedPrompt = registry.dotprompt.parse(source);\n  definePromptAsync(\n    registry,\n    registryDefinitionKey(name, variant ?? undefined, ns),\n    // We use a lazy promise here because we only want prompt loaded when it's first used.\n    // This is important because otherwise the loading may happen before the user has configured\n    // all the schemas, etc., which will result in dotprompt.renderMetadata errors.\n    lazy(async () => {\n      const promptMetadata =\n        await registry.dotprompt.renderMetadata(parsedPrompt);\n      if (variant) {\n        promptMetadata.variant = variant;\n      }\n\n      // dotprompt can set null description on the schema, which can confuse downstream schema consumers\n      if (promptMetadata.output?.schema?.description === null) {\n        delete promptMetadata.output.schema.description;\n      }\n      if (promptMetadata.input?.schema?.description === null) {\n        delete promptMetadata.input.schema.description;\n      }\n\n      return {\n        name: registryDefinitionKey(name, variant ?? undefined, ns),\n        model: promptMetadata.model,\n        config: promptMetadata.config,\n        tools: promptMetadata.tools,\n        description: promptMetadata.description,\n        output: {\n          jsonSchema: promptMetadata.output?.schema,\n          format: promptMetadata.output?.format,\n        },\n        input: {\n          jsonSchema: promptMetadata.input?.schema,\n        },\n        metadata: {\n          ...promptMetadata.metadata,\n          type: 'prompt',\n          prompt: {\n            ...promptMetadata,\n            template: parsedPrompt.template,\n          },\n        },\n        maxTurns: promptMetadata.raw?.['maxTurns'],\n        toolChoice: promptMetadata.raw?.['toolChoice'],\n        returnToolRequests: promptMetadata.raw?.['returnToolRequests'],\n        messages: parsedPrompt.template,\n      };\n    })\n  );\n}\n\nexport async function prompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  options?: { variant?: string; dir?: string }\n): Promise<ExecutablePrompt<I, O, CustomOptions>> {\n  return await lookupPrompt<I, O, CustomOptions>(\n    registry,\n    name,\n    options?.variant\n  );\n}\n\nfunction registryLookupKey(name: string, variant?: string, ns?: string) {\n  return `/prompt/${registryDefinitionKey(name, variant, ns)}`;\n}\n\nasync function lookupPrompt<\n  I = undefined,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  name: string,\n  variant?: string\n): Promise<ExecutablePrompt<I, O, CustomOptions>> {\n  const registryPrompt = await registry.lookupAction(\n    registryLookupKey(name, variant)\n  );\n  if (registryPrompt) {\n    return (registryPrompt as PromptAction)\n      .__executablePrompt as never as ExecutablePrompt<I, O, CustomOptions>;\n  }\n  throw new GenkitError({\n    status: 'NOT_FOUND',\n    message: `Prompt ${name + (variant ? ` (variant ${variant})` : '')} not found`,\n  });\n}\n\nfunction registryDefinitionKey(name: string, variant?: string, ns?: string) {\n  // \"ns/prompt.variant\" where ns and variant are optional\n  return `${ns ? `${ns}/` : ''}${name}${variant ? `.${variant}` : ''}`;\n}\n"], "names": ["promptMetadata"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,iBAAA,CAAA;AAAA,SAAA,gBAAA;IAAA,cAAA,IAAA;IAAA,eAAA,IAAA;IAAA,cAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,6BAAA,IAAA;IAAA,QAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAUO;AACP,IAAA,eAAqB;AACrB,IAAA,iBAAuB;AAEvB,IAAA,gBAA6B;AAC7B,IAAA,iBAA6C;AAE7C,IAAA,YAAsD;AACtD,IAAA,cAAwC;AAExC,IAAA,kBAUO;AACP,IAAA,iBAAwB;AACxB,IAAA,eAaO;AACP,IAAA,iBAAgD;AAmBzC,SAAS,eAAe,MAAA,EAAwC;IACrE,OAAO,OAAO,QAAA,CAAS,QAAA,EAAU,SAAS;AAC5C;AA8IO,SAAS,aAKd,QAAA,EACA,OAAA,EACgD;IAChD,OAAO,kBACL,UACA,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,QAAQ,OAAO,EAAA,GAAK,EAAE,EAAA,EAC9D,QAAQ,OAAA,CAAQ,OAAO;AAE3B;AAEA,SAAS,kBAKP,QAAA,EACA,IAAA,EACA,cAAA,EACgD;IAChD,MAAM,cAAc,CAAC;IAErB,MAAM,kBAAkB,OACtB,OACA,kBAC6B;QAC7B,OAAO,MAAA,CAAA,GAAM,eAAA,YAAA,EACX,UACA;YACE,UAAU;gBACR,MAAM;gBACN;YACF;YACA,QAAQ;gBACN,CAAC,eAAA,cAAc,CAAA,EAAG;YACpB;QACF,GACA,OAAO,aAAa;YAClB,MAAM,WAA0B,CAAC,CAAA;YACjC,gBAAgB;gBAAE,GAAG,aAAA;YAAc;YACnC,MAAM,UAAA,CAAA,GAAU,eAAA,iBAAA,EAAkB,QAAQ;YAC1C,MAAM,kBAAkB,MAAM;YAG9B,MAAM,mBACJ,UACA,SACA,OACA,UACA,iBACA,aACA;YAEF,MAAM,eACJ,UACA,SACA,OACA,UACA,iBACA,eACA;YAEF,MAAM,iBACJ,UACA,SACA,OACA,UACA,iBACA,aACA;YAGF,IAAI;YACJ,IAAI,OAAO,gBAAgB,IAAA,KAAS,YAAY;gBAC9C,OAAO,MAAM,gBAAgB,IAAA,CAAK,OAAO;oBACvC,OAAO,SAAS;oBAChB,SAAS,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,KAAK,CAAC;gBAC9D,CAAC;YACH,OAAO;gBACL,OAAO,gBAAgB,IAAA;YACzB;YAEA,MAAM,OAAA,CAAA,GAAwB,YAAA,mBAAA,EAAoB;gBAChD,OAAO,gBAAgB,KAAA;gBACvB,UAAU,gBAAgB,QAAA;gBAC1B;gBACA;gBACA,OAAO,gBAAgB,KAAA;gBACvB,oBAAoB,gBAAgB,kBAAA;gBACpC,YAAY,gBAAgB,UAAA;gBAC5B,SAAS,gBAAgB,OAAA;gBACzB,QAAQ,gBAAgB,MAAA;gBACxB,KAAK,gBAAgB,GAAA;gBACrB,GAAA,CAAA,GAAG,YAAA,mBAAA,EAAoB,aAAa,CAAA;gBACpC,QAAQ;oBACN,GAAG,iBAAiB,MAAA;oBACpB,GAAG,eAAe,MAAA;gBACpB;YACF,CAAC;YAED,IAAI,OAAO,IAAA,CAAK,KAAK,MAAM,EAAE,MAAA,KAAW,KAAK,CAAC,eAAe,QAAQ;gBACnE,OAAO,KAAK,MAAA;YACd;YACA,SAAS,MAAA,GAAS;YAClB,OAAO;QACT;IAEJ;IACA,MAAM,uBAAA,CAAA,GAAuB,aAAA,IAAA,EAAK,IAChC,eAAe,IAAA,CAAK,CAAC,YAA+C;YAClE,MAAM,WAAW,eAAe,OAAO;YACvC,OAAO;gBACL,MAAM,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,QAAQ,OAAO,EAAA,GAAK,EAAE,EAAA;gBACpE,iBAAiB,QAAQ,KAAA,EAAO;gBAChC,aAAa,QAAQ,KAAA,EAAO;gBAC5B,aAAa,QAAQ,WAAA;gBACrB,YAAY;gBACZ;gBACA,IAAI,OACF,UAC2C;oBAC3C,OAAA,CAAA,GAAO,gBAAA,iBAAA,EACL,UACA,MAAM,gBAAgB,OAAO,KAAA,CAAS;gBAE1C;YACF;QACF,CAAC;IAEH,MAAM,iBAAA,CAAA,GAAiB,YAAA,iBAAA,EACrB,UACA,UACA,MACA,sBACA,CAAC,WAAW;QACT,OAA2B,kBAAA,GAC1B;IACJ;IAGF,MAAM,+BAAA,CAAA,GAA+B,aAAA,IAAA,EAAK,IACxC,eAAe,IAAA,CAAK,CAAC,YAA+C;YAClE,MAAM,WAAW,eAAe,OAAO;YACvC,OAAO;gBACL,MAAM,GAAG,QAAQ,IAAI,GAAG,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,QAAQ,OAAO,EAAA,GAAK,EAAE,EAAA;gBACpE,iBAAiB,QAAQ,KAAA,EAAO;gBAChC,aAAa,QAAQ,KAAA,EAAO;gBAC5B,cAAc,aAAA,2BAAA;gBACd,aAAa,QAAQ,WAAA;gBACrB,YAAY;gBACZ;gBACA,IAAI,OAAO,UAAsD;oBAC/D,OAAO,MAAA,CAAA,GAAM,gBAAA,uBAAA,EACX,UACA,MAAM,gBAAgB,OAAO,KAAA,CAAS;gBAE1C;YACF;QACF,CAAC;IAGH,CAAA,GAAA,YAAA,iBAAA,EACE,UACA,qBACA,MACA,8BACA,CAAC,WAAW;QACT,OAAqC,kBAAA,GACpC;IACJ;IAGF,MAAM,mBAAmB,uBACvB,UACA,iBACA;IAGF,OAAO;AACT;AAEA,SAAS,eAAe,OAAA,EAAsC;IAC5D,MAAM,WAAW;QACf,GAAG,QAAQ,QAAA;QACX,QAAQ;YACN,GAAG,QAAQ,QAAA,EAAU,MAAA;YACrB,QAAQ,QAAQ,MAAA;YAChB,OAAO;gBACL,QAAQ,QAAQ,KAAA,GAAA,CAAA,GAAQ,cAAA,YAAA,EAAa,QAAQ,KAAK,IAAI,KAAA;YACxD;YACA,MAAM,QAAQ,IAAA,CAAK,QAAA,CAAS,GAAG,IAC3B,QAAQ,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,GACzB,QAAQ,IAAA;YACZ,OAAO,UAAU,QAAQ,KAAK;QAChC;QACA,MAAM;IACR;IAEA,IAAI,QAAQ,OAAA,EAAS;QACnB,SAAS,MAAA,CAAO,OAAA,GAAU,QAAQ,OAAA;IACpC;IAEA,OAAO;AACT;AAEA,SAAS,uBAKP,QAAA,EACA,eAAA,EAIA,cAAA,EACA;IACA,MAAM,mBAAoB,OACxB,OACA,SAC0C;QAC1C,OAAO,MAAA,CAAA,GAAM,eAAA,YAAA,EACX,UACA;YACE,UAAU;gBACR,MAAA,CAAO,MAAM,cAAA,EAAgB,QAAA,CAAS,IAAA;gBACtC;YACF;YACA,QAAQ;gBACN,CAAC,eAAA,cAAc,CAAA,EAAG;YACpB;QACF,GACA,OAAO,aAAa;YAClB,MAAM,SAAS,MAAA,CAAA,GAAM,gBAAA,QAAA,EAAS,UAAU;gBACtC,GAAI,MAAM,gBAAgB,OAAO,IAAI,CAAA;YACvC,CAAC;YACD,SAAS,MAAA,GAAS;YAClB,OAAO;QACT;IAEJ;IAEA,iBAAiB,MAAA,GAAS,OACxB,OACA,SAC+C;QAC/C,OAAO;YACL,GAAI,MAAM,gBAAgB,OAAO,IAAI,CAAA;QACvC;IACF;IAEA,iBAAiB,MAAA,GAAS,CACxB,OACA,SACuC;QACvC,OAAA,CAAA,GAAO,gBAAA,cAAA,EAAe,UAAU,gBAAgB,OAAO,IAAI,CAAC;IAC9D;IAEA,iBAAiB,MAAA,GAAS,YAAuC;QAC/D,OAAQ,MAAM;IAChB;IACA,OAAO;AACT;AAEA,eAAe,mBAKb,QAAA,EACA,OAAA,EACA,KAAA,EACA,QAAA,EACA,OAAA,EACA,WAAA,EACA,aAAA,EACA;IACA,IAAI,OAAO,QAAQ,MAAA,KAAW,YAAY;QACxC,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eACP,MAAM,QAAQ,MAAA,CAAO,OAAO;gBAC1B,OAAO,SAAS;gBAChB,SAAS,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,KAAK,CAAC;YAC9D,CAAC;QAEL,CAAC;IACH,OAAA,IAAW,OAAO,QAAQ,MAAA,KAAW,UAAU;QAE7C,IAAI,CAAC,YAAY,MAAA,EAAQ;YACvB,YAAY,MAAA,GAAS,MAAM,SAAS,SAAA,CAAU,OAAA,CAAQ,QAAQ,MAAM;QACtE;QACA,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,MAAM,uBACb,UACA,YAAY,MAAA,EACZ,OACA,SACA,SACA;QAEJ,CAAC;IACH,OAAA,IAAW,QAAQ,MAAA,EAAQ;QACzB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAe,QAAQ,MAAM;QACxC,CAAC;IACH;AACF;AAEA,eAAe,eAKb,QAAA,EACA,OAAA,EACA,KAAA,EACA,QAAA,EACA,OAAA,EACA,aAAA,EACA,WAAA,EACA;IACA,IAAI,QAAQ,QAAA,EAAU;QACpB,IAAI,OAAO,QAAQ,QAAA,KAAa,YAAY;YAC1C,SAAS,IAAA,IACH,MAAM,QAAQ,QAAA,CAAS,OAAO;gBAChC,OAAO,SAAS;gBAChB,SAAS,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,KAAK,CAAC;gBAC5D,SAAS,eAAe;YAC1B,CAAC;QAEL,OAAA,IAAW,OAAO,QAAQ,QAAA,KAAa,UAAU;YAE/C,IAAI,CAAC,YAAY,QAAA,EAAU;gBACzB,YAAY,QAAA,GAAW,MAAM,SAAS,SAAA,CAAU,OAAA,CAC9C,QAAQ,QAAA;YAEZ;YACA,MAAM,WAAW,MAAM,YAAY,QAAA,CAAS;gBAC1C;gBACA,SAAS;oBACP,GAAI,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,CAAA;oBACjD,OAAO,SAAS;gBAClB;gBACA,UAAU,eAAe,UAAU,IAAI,CAAC,IACtC,eAAA,OAAA,CAAQ,SAAA,CAAU,CAAC;YAEvB,CAAC;YACD,SAAS,IAAA,CAAK,GAAI,SAAS,QAA0B;QACvD,OAAO;YACL,SAAS,IAAA,CAAK,GAAG,QAAQ,QAAQ;QACnC;IACF,OAAO;QACL,IAAI,cAAc,QAAA,EAAU;YAC1B,SAAS,IAAA,CAAK,GAAG,cAAc,QAAQ;QACzC;IACF;IACA,IAAI,eAAe,UAAU;QAE3B,OAAO,cAAc,QAAA;IACvB;AACF;AAEA,eAAe,iBAKb,QAAA,EACA,OAAA,EACA,KAAA,EACA,QAAA,EACA,OAAA,EACA,WAAA,EACA,aAAA,EACA;IACA,IAAI,OAAO,QAAQ,MAAA,KAAW,YAAY;QACxC,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eACP,MAAM,QAAQ,MAAA,CAAO,OAAO;gBAC1B,OAAO,SAAS;gBAChB,SAAS,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,KAAK,CAAC;YAC9D,CAAC;QAEL,CAAC;IACH,OAAA,IAAW,OAAO,QAAQ,MAAA,KAAW,UAAU;QAE7C,IAAI,CAAC,YAAY,UAAA,EAAY;YAC3B,YAAY,UAAA,GAAa,MAAM,SAAS,SAAA,CAAU,OAAA,CAAQ,QAAQ,MAAM;QAC1E;QACA,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,MAAM,uBACb,UACA,YAAY,UAAA,EACZ,OACA,SACA,SACA;QAEJ,CAAC;IACH,OAAA,IAAW,QAAQ,MAAA,EAAQ;QACzB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAe,QAAQ,MAAM;QACxC,CAAC;IACH;AACF;AAEA,SAAS,UACP,QAAA,EACoB;IACpB,IAAI,aAAa,KAAA,GAAW;QAC1B,OAAO,KAAA;IACT;IACA,IAAI,OAAO,aAAa,UAAU;QAChC,OAAO;IACT;IACA,IAAK,SAAiC,IAAA,EAAM;QAC1C,OAAQ,SAAiC,IAAA;IAC3C;IACA,OAAQ,SAAyB,QAAA,CAAS,IAAA;AAC5C;AAEA,SAAS,eAAe,KAAA,EAAuC;IAC7D,IAAI,MAAM,OAAA,CAAQ,KAAK,EAAG,CAAA,OAAO;IACjC,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YACL;gBACE,MAAM;YACR;SACF;IACF;IACA,OAAO;QAAC,KAAa;KAAA;AACvB;AAEA,eAAe,uBAKb,QAAA,EACA,QAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,aAAA,EACiB;IACjB,MAAM,YAAY,MAAM,SAAS;QAC/B;QACA,SAAS;YACP,GAAI,eAAe,WAAA,CAAA,GAAW,YAAA,UAAA,EAAW,QAAQ,CAAA;YACjD,OAAO,SAAS;QAClB;IACF,CAAC;IACD,IAAI,UAAU,QAAA,CAAS,MAAA,KAAW,GAAG;QACnC,MAAM,IAAI,MAAM,6CAA6C;IAC/D;IACA,OAAO,UAAU,QAAA,CAAS,CAAC,CAAA,CAAE,OAAA;AAC/B;AAKO,SAAS,mBAAmB,GAAA,EAAmC;IACpE,OACE,CAAC,CAAE,KAA0B,UAC7B,CAAC,CAAE,KAA0B,UAC7B,CAAC,CAAE,KAA0B;AAEjC;AAEO,SAAS,iBACd,QAAA,EACA,MAAM,WAAA,EACN,EAAA,EACM;IACN,MAAM,cAAA,CAAA,GAAc,YAAA,OAAA,EAAQ,GAAG;IAC/B,IAAA,CAAA,GAAI,UAAA,UAAA,EAAW,WAAW,GAAG;QAC3B,4BAA4B,UAAU,KAAK,IAAI,EAAE;IACnD;AACF;AACO,SAAS,4BACd,QAAA,EACA,GAAA,EACA,EAAA,EACA,MAAA,EACM;IACN,MAAM,cAAA,CAAA,GAAc,YAAA,OAAA,EAAQ,GAAG;IAC/B,MAAM,UAAA,CAAA,GAAU,UAAA,WAAA,EAAA,CAAA,GAAY,YAAA,IAAA,EAAK,aAAa,MAAM,GAAG;QACrD,eAAe;IACjB,CAAC;IACD,KAAA,MAAW,UAAU,QAAS;QAC5B,MAAM,aAAA,CAAA,GAAa,YAAA,IAAA,EAAK,aAAa,MAAM;QAC3C,MAAM,WAAW,OAAO,IAAA;QACxB,IAAI,OAAO,MAAA,CAAO,KAAK,SAAS,QAAA,CAAS,SAAS,GAAG;YACnD,IAAI,SAAS,UAAA,CAAW,GAAG,GAAG;gBAC5B,MAAM,cAAc,SAAS,SAAA,CAAU,GAAG,SAAS,MAAA,GAAS,CAAC;gBAC7D,cACE,UACA,aAAA,CAAA,GACA,UAAA,YAAA,EAAA,CAAA,GAAa,YAAA,IAAA,EAAK,YAAY,QAAQ,GAAG;oBACvC,UAAU;gBACZ,CAAC;gBAEH,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,8BAAA,EAAiC,WAAW,CAAA,QAAA,EAAA,CAAA,GAAW,YAAA,IAAA,EAAK,YAAY,QAAQ,CAAC,CAAA,CAAA,CAAA;YAErF,OAAO;gBAGL,WACE,UACA,aACA,UACA,SAAS,GAAG,MAAM,CAAA,CAAA,CAAA,GAAM,IACxB;YAEJ;QACF,OAAA,IAAW,OAAO,WAAA,CAAY,GAAG;YAC/B,4BAA4B,UAAU,KAAK,IAAA,CAAA,GAAI,YAAA,IAAA,EAAK,QAAQ,QAAQ,CAAC;QACvE;IACF;AACF;AAEO,SAAS,cACd,QAAA,EACA,IAAA,EACA,MAAA,EACA;IACA,SAAS,SAAA,CAAU,aAAA,CAAc,MAAM,MAAM;AAC/C;AAEO,SAAS,aACd,QAAA,EACA,IAAA,EACA,EAAA,EACA;IACA,SAAS,SAAA,CAAU,YAAA,CAAa,MAAM,EAAE;AAC1C;AAEA,SAAS,WACP,QAAA,EACA,IAAA,EACA,QAAA,EACA,SAAS,EAAA,EACT,KAAK,WAAA,EACC;IACN,IAAI,OAAO,GAAG,UAAU,EAAE,GAAA,CAAA,GAAG,YAAA,QAAA,EAAS,UAAU,SAAS,CAAC,EAAA;IAC1D,IAAI,UAAyB;IAC7B,IAAI,KAAK,QAAA,CAAS,GAAG,GAAG;QACtB,MAAM,QAAQ,KAAK,KAAA,CAAM,GAAG;QAC5B,OAAO,KAAA,CAAM,CAAC,CAAA;QACd,UAAU,KAAA,CAAM,CAAC,CAAA;IACnB;IACA,MAAM,SAAA,CAAA,GAAS,UAAA,YAAA,EAAA,CAAA,GAAa,YAAA,IAAA,EAAK,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM;IACtE,MAAM,eAAe,SAAS,SAAA,CAAU,KAAA,CAAM,MAAM;IACpD,kBACE,UACA,sBAAsB,MAAM,WAAW,KAAA,GAAW,EAAE,GAAA,sFAAA;IAAA,4FAAA;IAAA,+EAAA;IAAA,CAAA,GAIpD,aAAA,IAAA,EAAK,YAAY;QACf,MAAMA,kBACJ,MAAM,SAAS,SAAA,CAAU,cAAA,CAAe,YAAY;QACtD,IAAI,SAAS;YACXA,gBAAe,OAAA,GAAU;QAC3B;QAGA,IAAIA,gBAAe,MAAA,EAAQ,QAAQ,gBAAgB,MAAM;YACvD,OAAOA,gBAAe,MAAA,CAAO,MAAA,CAAO,WAAA;QACtC;QACA,IAAIA,gBAAe,KAAA,EAAO,QAAQ,gBAAgB,MAAM;YACtD,OAAOA,gBAAe,KAAA,CAAM,MAAA,CAAO,WAAA;QACrC;QAEA,OAAO;YACL,MAAM,sBAAsB,MAAM,WAAW,KAAA,GAAW,EAAE;YAC1D,OAAOA,gBAAe,KAAA;YACtB,QAAQA,gBAAe,MAAA;YACvB,OAAOA,gBAAe,KAAA;YACtB,aAAaA,gBAAe,WAAA;YAC5B,QAAQ;gBACN,YAAYA,gBAAe,MAAA,EAAQ;gBACnC,QAAQA,gBAAe,MAAA,EAAQ;YACjC;YACA,OAAO;gBACL,YAAYA,gBAAe,KAAA,EAAO;YACpC;YACA,UAAU;gBACR,GAAGA,gBAAe,QAAA;gBAClB,MAAM;gBACN,QAAQ;oBACN,GAAGA,eAAAA;oBACH,UAAU,aAAa,QAAA;gBACzB;YACF;YACA,UAAUA,gBAAe,GAAA,EAAA,CAAM,UAAU,CAAA;YACzC,YAAYA,gBAAe,GAAA,EAAA,CAAM,YAAY,CAAA;YAC7C,oBAAoBA,gBAAe,GAAA,EAAA,CAAM,oBAAoB,CAAA;YAC7D,UAAU,aAAa,QAAA;QACzB;IACF,CAAC;AAEL;AAEA,eAAsB,OAKpB,QAAA,EACA,IAAA,EACA,OAAA,EACgD;IAChD,OAAO,MAAM,aACX,UACA,MACA,SAAS;AAEb;AAEA,SAAS,kBAAkB,IAAA,EAAc,OAAA,EAAkB,EAAA,EAAa;IACtE,OAAO,CAAA,QAAA,EAAW,sBAAsB,MAAM,SAAS,EAAE,CAAC,EAAA;AAC5D;AAEA,eAAe,aAKb,QAAA,EACA,IAAA,EACA,OAAA,EACgD;IAChD,MAAM,iBAAiB,MAAM,SAAS,YAAA,CACpC,kBAAkB,MAAM,OAAO;IAEjC,IAAI,gBAAgB;QAClB,OAAQ,eACL,kBAAA;IACL;IACA,MAAM,IAAI,YAAA,WAAA,CAAY;QACpB,QAAQ;QACR,SAAS,CAAA,OAAA,EAAU,OAAA,CAAQ,UAAU,CAAA,UAAA,EAAa,OAAO,CAAA,CAAA,CAAA,GAAM,EAAA,CAAG,CAAA,UAAA,CAAA;IACpE,CAAC;AACH;AAEA,SAAS,sBAAsB,IAAA,EAAc,OAAA,EAAkB,EAAA,EAAa;IAE1E,OAAO,GAAG,KAAK,GAAG,EAAE,CAAA,CAAA,CAAA,GAAM,EAAE,GAAG,IAAI,GAAG,UAAU,CAAA,CAAA,EAAI,OAAO,EAAA,GAAK,EAAE,EAAA;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3301, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/tool.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  assertUnstable,\n  defineAction,\n  detachedAction,\n  isAction,\n  isDetachedAction,\n  stripUndefinedProps,\n  z,\n  type Action,\n  type ActionContext,\n  type ActionRunOptions,\n  type DetachedAction,\n  type JSONSchema7,\n} from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { parseSchema, toJsonSchema } from '@genkit-ai/core/schema';\nimport { setCustomMetadataAttributes } from '@genkit-ai/core/tracing';\nimport type {\n  Part,\n  ToolDefinition,\n  ToolRequestPart,\n  ToolResponsePart,\n} from './model.js';\nimport { isExecutablePrompt, type ExecutablePrompt } from './prompt.js';\n\nexport interface Resumable<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /**\n   * respond constructs a tool response corresponding to the provided interrupt tool request\n   * using the provided reply data, validating it against the output schema of the tool if\n   * it exists.\n   *\n   * @beta\n   */\n  respond(\n    /** The interrupt tool request to which you want to respond. */\n    interrupt: ToolRequestPart,\n    /**\n     * The data with which you want to respond. Must conform to a tool's output schema or an\n     * interrupt's input schema.\n     **/\n    outputData: z.infer<O>,\n    options?: { metadata?: Record<string, any> }\n  ): ToolResponsePart;\n\n  /**\n   * restart constructs a tool request corresponding to the provided interrupt tool request\n   * that will then re-trigger the tool after e.g. a user confirms. The `resumedMetadata`\n   * supplied to this method will be passed to the tool to allow for custom handling of\n   * restart logic.\n   *\n   * @param interrupt The interrupt tool request you want to restart.\n   * @param resumedMetadata The metadata you want to provide to the tool to aide in reprocessing. Defaults to `true` if none is supplied.\n   * @param options Additional options for restarting the tool.\n   *\n   * @beta\n   */\n  restart(\n    interrupt: ToolRequestPart,\n    resumedMetadata?: any,\n    options?: {\n      /**\n       * Replace the existing input arguments to the tool with different ones, for example\n       * if the user revised an action before confirming. When input is replaced, the existing\n       * tool request will be amended in the message history.\n       **/\n      replaceInput?: z.infer<I>;\n    }\n  ): ToolRequestPart;\n}\n\n/**\n * An action with a `tool` type.\n */\nexport type ToolAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<I, O, z.ZodTypeAny, ToolRunOptions> &\n  Resumable<I, O> & {\n    __action: {\n      metadata: {\n        type: 'tool';\n      };\n    };\n  };\n\n/**\n * A dynamic action with a `tool` type. Dynamic tools are detached actions -- not associated with any registry.\n */\nexport type DynamicToolAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n> = DetachedAction<I, O, z.ZodTypeAny, ToolRunOptions> &\n  Resumable<I, O> & {\n    __action: {\n      metadata: {\n        type: 'tool';\n      };\n    };\n  };\n\nexport interface ToolRunOptions extends ActionRunOptions<z.ZodTypeAny> {\n  /**\n   * If resumed is supplied to a tool at runtime, that means that it was previously interrupted and this is a second\n   * @beta\n   **/\n  resumed?: boolean | Record<string, any>;\n  /** The metadata from the tool request that triggered this run. */\n  metadata?: Record<string, any>;\n}\n\n/**\n * Configuration for a tool.\n */\nexport interface ToolConfig<I extends z.ZodTypeAny, O extends z.ZodTypeAny> {\n  /** Unique name of the tool to use as a key in the registry. */\n  name: string;\n  /** Description of the tool. This is passed to the model to help understand what the tool is used for. */\n  description: string;\n  /** Input Zod schema. Mutually exclusive with `inputJsonSchema`. */\n  inputSchema?: I;\n  /** Input JSON schema. Mutually exclusive with `inputSchema`. */\n  inputJsonSchema?: JSONSchema7;\n  /** Output Zod schema. Mutually exclusive with `outputJsonSchema`. */\n  outputSchema?: O;\n  /** Output JSON schema. Mutually exclusive with `outputSchema`. */\n  outputJsonSchema?: JSONSchema7;\n  /** Metadata to be passed to the tool. */\n  metadata?: Record<string, any>;\n}\n\n/**\n * A reference to a tool in the form of a name, definition, or the action itself.\n */\nexport type ToolArgument<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n> =\n  | string\n  | ToolAction<I, O>\n  | DynamicToolAction<I, O>\n  | Action<I, O>\n  | ExecutablePrompt<any, any, any>;\n\n/**\n * Converts an action to a tool action by setting the appropriate metadata.\n */\nexport function asTool<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n  registry: Registry,\n  action: Action<I, O>\n): ToolAction<I, O> {\n  if (action.__action?.metadata?.type === 'tool') {\n    return action as ToolAction<I, O>;\n  }\n\n  const fn = ((input) => {\n    setCustomMetadataAttributes(registry, { subtype: 'tool' });\n    return action(input);\n  }) as ToolAction<I, O>;\n  fn.__action = {\n    ...action.__action,\n    metadata: { ...action.__action.metadata, type: 'tool' },\n  };\n  return fn;\n}\n\n/**\n * Resolves a mix of various formats of tool references to a list of tool actions by looking them up in the registry.\n */\nexport async function resolveTools<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  tools?: (ToolArgument | ToolDefinition)[]\n): Promise<ToolAction[]> {\n  if (!tools || tools.length === 0) {\n    return [];\n  }\n\n  return await Promise.all(\n    tools.map(async (ref): Promise<ToolAction> => {\n      if (typeof ref === 'string') {\n        return await lookupToolByName(registry, ref);\n      } else if (isAction(ref)) {\n        return asTool(registry, ref);\n      } else if (isExecutablePrompt(ref)) {\n        return await ref.asTool();\n      } else if ((ref as ToolDefinition).name) {\n        return await lookupToolByName(\n          registry,\n          (ref as ToolDefinition).metadata?.originalName ||\n            (ref as ToolDefinition).name\n        );\n      }\n      throw new Error('Tools must be strings, tool definitions, or actions.');\n    })\n  );\n}\n\nexport async function lookupToolByName(\n  registry: Registry,\n  name: string\n): Promise<ToolAction> {\n  const tool =\n    (await registry.lookupAction(name)) ||\n    (await registry.lookupAction(`/tool/${name}`)) ||\n    (await registry.lookupAction(`/prompt/${name}`));\n  if (!tool) {\n    throw new Error(`Tool ${name} not found`);\n  }\n  return tool as ToolAction;\n}\n\n/**\n * Converts a tool action to a definition of the tool to be passed to a model.\n */\nexport function toToolDefinition(\n  tool: Action<z.ZodTypeAny, z.ZodTypeAny>\n): ToolDefinition {\n  const originalName = tool.__action.name;\n  let name = originalName;\n  if (originalName.includes('/')) {\n    name = originalName.substring(originalName.lastIndexOf('/') + 1);\n  }\n\n  const out: ToolDefinition = {\n    name,\n    description: tool.__action.description || '',\n    outputSchema: toJsonSchema({\n      schema: tool.__action.outputSchema ?? z.void(),\n      jsonSchema: tool.__action.outputJsonSchema,\n    })!,\n    inputSchema: toJsonSchema({\n      schema: tool.__action.inputSchema ?? z.void(),\n      jsonSchema: tool.__action.inputJsonSchema,\n    })!,\n  };\n\n  if (originalName !== name) {\n    out.metadata = { originalName };\n  }\n\n  return out;\n}\n\nexport interface ToolFnOptions {\n  /**\n   * A function that can be called during tool execution that will result in the tool\n   * getting interrupted (immediately) and tool request returned to the upstream caller.\n   */\n  interrupt: (metadata?: Record<string, any>) => never;\n\n  context: ActionContext;\n}\n\nexport type ToolFn<I extends z.ZodTypeAny, O extends z.ZodTypeAny> = (\n  input: z.infer<I>,\n  ctx: ToolFnOptions & ToolRunOptions\n) => Promise<z.infer<O>>;\n\n/**\n * Defines a tool.\n *\n * A tool is an action that can be passed to a model to be called automatically if it so chooses.\n */\nexport function defineTool<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n  registry: Registry,\n  config: ToolConfig<I, O>,\n  fn: ToolFn<I, O>\n): ToolAction<I, O> {\n  const a = defineAction(\n    registry,\n    {\n      ...config,\n      actionType: 'tool',\n      metadata: { ...(config.metadata || {}), type: 'tool' },\n    },\n    (i, runOptions) => {\n      return fn(i, {\n        ...runOptions,\n        context: { ...runOptions.context },\n        interrupt: interruptTool(registry),\n      });\n    }\n  );\n  implementTool(a as ToolAction<I, O>, config, registry);\n  return a as ToolAction<I, O>;\n}\n\nfunction implementTool<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n  a: ToolAction<I, O>,\n  config: ToolConfig<I, O>,\n  registry?: Registry\n) {\n  (a as ToolAction<I, O>).respond = (interrupt, responseData, options) => {\n    if (registry) {\n      assertUnstable(\n        registry,\n        'beta',\n        \"The 'tool.reply' method is part of the 'interrupts' beta feature.\"\n      );\n    }\n    parseSchema(responseData, {\n      jsonSchema: config.outputJsonSchema,\n      schema: config.outputSchema,\n    });\n    return {\n      toolResponse: stripUndefinedProps({\n        name: interrupt.toolRequest.name,\n        ref: interrupt.toolRequest.ref,\n        output: responseData,\n      }),\n      metadata: {\n        interruptResponse: options?.metadata || true,\n      },\n    };\n  };\n\n  (a as ToolAction<I, O>).restart = (interrupt, resumedMetadata, options) => {\n    if (registry) {\n      assertUnstable(\n        registry,\n        'beta',\n        \"The 'tool.restart' method is part of the 'interrupts' beta feature.\"\n      );\n    }\n    let replaceInput = options?.replaceInput;\n    if (replaceInput) {\n      replaceInput = parseSchema(replaceInput, {\n        schema: config.inputSchema,\n        jsonSchema: config.inputJsonSchema,\n      });\n    }\n    return {\n      toolRequest: stripUndefinedProps({\n        name: interrupt.toolRequest.name,\n        ref: interrupt.toolRequest.ref,\n        input: replaceInput || interrupt.toolRequest.input,\n      }),\n      metadata: stripUndefinedProps({\n        ...interrupt.metadata,\n        resumed: resumedMetadata || true,\n        // annotate the original input if replacing it\n        replacedInput: replaceInput ? interrupt.toolRequest.input : undefined,\n      }),\n    };\n  };\n}\n\n/** InterruptConfig defines the options for configuring an interrupt. */\nexport type InterruptConfig<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  R extends z.ZodTypeAny = z.ZodTypeAny,\n> = ToolConfig<I, R> & {\n  /** requestMetadata adds additional `interrupt` metadata to the `toolRequest` generated by the interrupt */\n  requestMetadata?:\n    | Record<string, any>\n    | ((\n        input: z.infer<I>\n      ) => Record<string, any> | Promise<Record<string, any>>);\n};\n\nexport function isToolRequest(part: Part): part is ToolRequestPart {\n  return !!part.toolRequest;\n}\n\nexport function isToolResponse(part: Part): part is ToolResponsePart {\n  return !!part.toolResponse;\n}\n\nexport function isDynamicTool(t: unknown): t is DynamicToolAction {\n  return (\n    (isDetachedAction(t) || isAction(t)) &&\n    t.__action.metadata?.type === 'tool' &&\n    t.__action.metadata?.dynamic\n  );\n}\n\nexport function defineInterrupt<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n  registry: Registry,\n  config: InterruptConfig<I, O>\n): ToolAction<I, O> {\n  const { requestMetadata, ...toolConfig } = config;\n\n  return defineTool<I, O>(\n    registry,\n    toolConfig,\n    async (input, { interrupt }) => {\n      if (!config.requestMetadata) interrupt();\n      else if (typeof config.requestMetadata === 'object')\n        interrupt(config.requestMetadata);\n      else interrupt(await Promise.resolve(config.requestMetadata(input)));\n    }\n  );\n}\n\n/**\n * Thrown when tools execution is interrupted. It's meant to be caugh by the framework, not public API.\n */\nexport class ToolInterruptError extends Error {\n  constructor(readonly metadata?: Record<string, any>) {\n    super();\n    this.name = 'ToolInterruptError';\n  }\n}\n\n/**\n * Interrupts current tool execution causing tool request to be returned in the generation response.\n * Should only be called within a tool.\n */\nfunction interruptTool(registry: Registry) {\n  return (metadata?: Record<string, any>): never => {\n    assertUnstable(registry, 'beta', 'Tool interrupts are a beta feature.');\n    throw new ToolInterruptError(metadata);\n  };\n}\n\n/**\n * Defines a dynamic tool. Dynamic tools are just like regular tools but will not be registered in the\n * Genkit registry and can be defined dynamically at runtime.\n */\nexport function dynamicTool<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n  config: ToolConfig<I, O>,\n  fn?: ToolFn<I, O>\n): DynamicToolAction<I, O> {\n  const a = detachedAction(\n    {\n      ...config,\n      actionType: 'tool',\n      metadata: { ...(config.metadata || {}), type: 'tool', dynamic: true },\n    },\n    (i, runOptions) => {\n      const interrupt = interruptTool(runOptions.registry);\n      if (fn) {\n        return fn(i, {\n          ...runOptions,\n          context: { ...runOptions.context },\n          interrupt,\n        });\n      }\n      return interrupt();\n    }\n  );\n  implementTool(a as any, config);\n  return {\n    __action: {\n      ...a.__action,\n      metadata: {\n        ...a.__action.metadata,\n        type: 'tool',\n      },\n    },\n    attach(registry) {\n      const bound = a.attach(registry);\n      implementTool(bound as ToolAction<I, O>, config);\n      return bound;\n    },\n  } as DynamicToolAction<I, O>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,oBAAA,IAAA;IAAA,QAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,YAAA,IAAA;IAAA,aAAA,IAAA;IAAA,eAAA,IAAA;IAAA,eAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,cAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAaO;AAEP,IAAA,gBAA0C;AAC1C,IAAA,iBAA4C;AAO5C,IAAA,gBAA0D;AA8HnD,SAAS,OACd,QAAA,EACA,MAAA,EACkB;IAClB,IAAI,OAAO,QAAA,EAAU,UAAU,SAAS,QAAQ;QAC9C,OAAO;IACT;IAEA,MAAM,KAAM,CAAC,UAAU;QACrB,CAAA,GAAA,eAAA,2BAAA,EAA4B,UAAU;YAAE,SAAS;QAAO,CAAC;QACzD,OAAO,OAAO,KAAK;IACrB;IACA,GAAG,QAAA,GAAW;QACZ,GAAG,OAAO,QAAA;QACV,UAAU;YAAE,GAAG,OAAO,QAAA,CAAS,QAAA;YAAU,MAAM;QAAO;IACxD;IACA,OAAO;AACT;AAKA,eAAsB,aAIpB,QAAA,EACA,KAAA,EACuB;IACvB,IAAI,CAAC,SAAS,MAAM,MAAA,KAAW,GAAG;QAChC,OAAO,CAAC,CAAA;IACV;IAEA,OAAO,MAAM,QAAQ,GAAA,CACnB,MAAM,GAAA,CAAI,OAAO,QAA6B;QAC5C,IAAI,OAAO,QAAQ,UAAU;YAC3B,OAAO,MAAM,iBAAiB,UAAU,GAAG;QAC7C,OAAA,IAAA,CAAA,GAAW,YAAA,QAAA,EAAS,GAAG,GAAG;YACxB,OAAO,OAAO,UAAU,GAAG;QAC7B,OAAA,IAAA,CAAA,GAAW,cAAA,kBAAA,EAAmB,GAAG,GAAG;YAClC,OAAO,MAAM,IAAI,MAAA,CAAO;QAC1B,OAAA,IAAY,IAAuB,IAAA,EAAM;YACvC,OAAO,MAAM,iBACX,UACC,IAAuB,QAAA,EAAU,gBAC/B,IAAuB,IAAA;QAE9B;QACA,MAAM,IAAI,MAAM,sDAAsD;IACxE,CAAC;AAEL;AAEA,eAAsB,iBACpB,QAAA,EACA,IAAA,EACqB;IACrB,MAAM,OACH,MAAM,SAAS,YAAA,CAAa,IAAI,KAChC,MAAM,SAAS,YAAA,CAAa,CAAA,MAAA,EAAS,IAAI,EAAE,KAC3C,MAAM,SAAS,YAAA,CAAa,CAAA,QAAA,EAAW,IAAI,EAAE;IAChD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,CAAA,KAAA,EAAQ,IAAI,CAAA,UAAA,CAAY;IAC1C;IACA,OAAO;AACT;AAKO,SAAS,iBACd,IAAA,EACgB;IAChB,MAAM,eAAe,KAAK,QAAA,CAAS,IAAA;IACnC,IAAI,OAAO;IACX,IAAI,aAAa,QAAA,CAAS,GAAG,GAAG;QAC9B,OAAO,aAAa,SAAA,CAAU,aAAa,WAAA,CAAY,GAAG,IAAI,CAAC;IACjE;IAEA,MAAM,MAAsB;QAC1B;QACA,aAAa,KAAK,QAAA,CAAS,WAAA,IAAe;QAC1C,cAAA,CAAA,GAAc,cAAA,YAAA,EAAa;YACzB,QAAQ,KAAK,QAAA,CAAS,YAAA,IAAgB,YAAA,CAAA,CAAE,IAAA,CAAK;YAC7C,YAAY,KAAK,QAAA,CAAS,gBAAA;QAC5B,CAAC;QACD,aAAA,CAAA,GAAa,cAAA,YAAA,EAAa;YACxB,QAAQ,KAAK,QAAA,CAAS,WAAA,IAAe,YAAA,CAAA,CAAE,IAAA,CAAK;YAC5C,YAAY,KAAK,QAAA,CAAS,eAAA;QAC5B,CAAC;IACH;IAEA,IAAI,iBAAiB,MAAM;QACzB,IAAI,QAAA,GAAW;YAAE;QAAa;IAChC;IAEA,OAAO;AACT;AAsBO,SAAS,WACd,QAAA,EACA,MAAA,EACA,EAAA,EACkB;IAClB,MAAM,IAAA,CAAA,GAAI,YAAA,YAAA,EACR,UACA;QACE,GAAG,MAAA;QACH,YAAY;QACZ,UAAU;YAAE,GAAI,OAAO,QAAA,IAAY,CAAC,CAAA;YAAI,MAAM;QAAO;IACvD,GACA,CAAC,GAAG,eAAe;QACjB,OAAO,GAAG,GAAG;YACX,GAAG,UAAA;YACH,SAAS;gBAAE,GAAG,WAAW,OAAA;YAAQ;YACjC,WAAW,cAAc,QAAQ;QACnC,CAAC;IACH;IAEF,cAAc,GAAuB,QAAQ,QAAQ;IACrD,OAAO;AACT;AAEA,SAAS,cACP,CAAA,EACA,MAAA,EACA,QAAA,EACA;IACC,EAAuB,OAAA,GAAU,CAAC,WAAW,cAAc,YAAY;QACtE,IAAI,UAAU;YACZ,CAAA,GAAA,YAAA,cAAA,EACE,UACA,QACA;QAEJ;QACA,CAAA,GAAA,cAAA,WAAA,EAAY,cAAc;YACxB,YAAY,OAAO,gBAAA;YACnB,QAAQ,OAAO,YAAA;QACjB,CAAC;QACD,OAAO;YACL,cAAA,CAAA,GAAc,YAAA,mBAAA,EAAoB;gBAChC,MAAM,UAAU,WAAA,CAAY,IAAA;gBAC5B,KAAK,UAAU,WAAA,CAAY,GAAA;gBAC3B,QAAQ;YACV,CAAC;YACD,UAAU;gBACR,mBAAmB,SAAS,YAAY;YAC1C;QACF;IACF;IAEC,EAAuB,OAAA,GAAU,CAAC,WAAW,iBAAiB,YAAY;QACzE,IAAI,UAAU;YACZ,CAAA,GAAA,YAAA,cAAA,EACE,UACA,QACA;QAEJ;QACA,IAAI,eAAe,SAAS;QAC5B,IAAI,cAAc;YAChB,eAAA,CAAA,GAAe,cAAA,WAAA,EAAY,cAAc;gBACvC,QAAQ,OAAO,WAAA;gBACf,YAAY,OAAO,eAAA;YACrB,CAAC;QACH;QACA,OAAO;YACL,aAAA,CAAA,GAAa,YAAA,mBAAA,EAAoB;gBAC/B,MAAM,UAAU,WAAA,CAAY,IAAA;gBAC5B,KAAK,UAAU,WAAA,CAAY,GAAA;gBAC3B,OAAO,gBAAgB,UAAU,WAAA,CAAY,KAAA;YAC/C,CAAC;YACD,UAAA,CAAA,GAAU,YAAA,mBAAA,EAAoB;gBAC5B,GAAG,UAAU,QAAA;gBACb,SAAS,mBAAmB;gBAAA,8CAAA;gBAE5B,eAAe,eAAe,UAAU,WAAA,CAAY,KAAA,GAAQ,KAAA;YAC9D,CAAC;QACH;IACF;AACF;AAeO,SAAS,cAAc,IAAA,EAAqC;IACjE,OAAO,CAAC,CAAC,KAAK,WAAA;AAChB;AAEO,SAAS,eAAe,IAAA,EAAsC;IACnE,OAAO,CAAC,CAAC,KAAK,YAAA;AAChB;AAEO,SAAS,cAAc,CAAA,EAAoC;IAChE,OAAA,CAAA,CAAA,GACG,YAAA,gBAAA,EAAiB,CAAC,KAAA,CAAA,GAAK,YAAA,QAAA,EAAS,CAAC,CAAA,KAClC,EAAE,QAAA,CAAS,QAAA,EAAU,SAAS,UAC9B,EAAE,QAAA,CAAS,QAAA,EAAU;AAEzB;AAEO,SAAS,gBACd,QAAA,EACA,MAAA,EACkB;IAClB,MAAM,EAAE,eAAA,EAAiB,GAAG,WAAW,CAAA,GAAI;IAE3C,OAAO,WACL,UACA,YACA,OAAO,OAAO,EAAE,SAAA,CAAU,CAAA,KAAM;QAC9B,IAAI,CAAC,OAAO,eAAA,CAAiB,CAAA,UAAU;aAAA,IAC9B,OAAO,OAAO,eAAA,KAAoB,UACzC,UAAU,OAAO,eAAe;aAC7B,UAAU,MAAM,QAAQ,OAAA,CAAQ,OAAO,eAAA,CAAgB,KAAK,CAAC,CAAC;IACrE;AAEJ;AAKO,MAAM,2BAA2B,MAAM;IAC5C,YAAqB,QAAA,CAAgC;QACnD,KAAA,CAAM;QADa,IAAA,CAAA,QAAA,GAAA;QAEnB,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAMA,SAAS,cAAc,QAAA,EAAoB;IACzC,OAAO,CAAC,aAA0C;QAChD,CAAA,GAAA,YAAA,cAAA,EAAe,UAAU,QAAQ,qCAAqC;QACtE,MAAM,IAAI,mBAAmB,QAAQ;IACvC;AACF;AAMO,SAAS,YACd,MAAA,EACA,EAAA,EACyB;IACzB,MAAM,IAAA,CAAA,GAAI,YAAA,cAAA,EACR;QACE,GAAG,MAAA;QACH,YAAY;QACZ,UAAU;YAAE,GAAI,OAAO,QAAA,IAAY,CAAC,CAAA;YAAI,MAAM;YAAQ,SAAS;QAAK;IACtE,GACA,CAAC,GAAG,eAAe;QACjB,MAAM,YAAY,cAAc,WAAW,QAAQ;QACnD,IAAI,IAAI;YACN,OAAO,GAAG,GAAG;gBACX,GAAG,UAAA;gBACH,SAAS;oBAAE,GAAG,WAAW,OAAA;gBAAQ;gBACjC;YACF,CAAC;QACH;QACA,OAAO,UAAU;IACnB;IAEF,cAAc,GAAU,MAAM;IAC9B,OAAO;QACL,UAAU;YACR,GAAG,EAAE,QAAA;YACL,UAAU;gBACR,GAAG,EAAE,QAAA,CAAS,QAAA;gBACd,MAAM;YACR;QACF;QACA,QAAO,QAAA,EAAU;YACf,MAAM,QAAQ,EAAE,MAAA,CAAO,QAAQ;YAC/B,cAAc,OAA2B,MAAM;YAC/C,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3564, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/generate/resolve-tool-requests.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError, stripUndefinedProps } from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport type {\n  GenerateActionOptions,\n  GenerateResponseData,\n  MessageData,\n  Part,\n  ToolRequestPart,\n  ToolResponsePart,\n} from '../model.js';\nimport { isPromptAction } from '../prompt.js';\nimport {\n  ToolInterruptError,\n  isToolRequest,\n  resolveTools,\n  type ToolAction,\n  type ToolRunOptions,\n} from '../tool.js';\n\nexport function toToolMap(tools: ToolAction[]): Record<string, ToolAction> {\n  assertValidToolNames(tools);\n  const out: Record<string, ToolAction> = {};\n  for (const tool of tools) {\n    const name = tool.__action.name;\n    const shortName = name.substring(name.lastIndexOf('/') + 1);\n    out[shortName] = tool;\n  }\n  return out;\n}\n\n/** Ensures that each tool has a unique name. */\nexport function assertValidToolNames(tools: ToolAction[]) {\n  const nameMap: Record<string, string> = {};\n  for (const tool of tools) {\n    const name = tool.__action.name;\n    const shortName = name.substring(name.lastIndexOf('/') + 1);\n    if (nameMap[shortName]) {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `Cannot provide two tools with the same name: '${name}' and '${nameMap[shortName]}'`,\n      });\n    }\n    nameMap[shortName] = name;\n  }\n}\n\nfunction toRunOptions(part: ToolRequestPart): ToolRunOptions {\n  const out: ToolRunOptions = { metadata: part.metadata };\n  if (part.metadata?.resumed) out.resumed = part.metadata.resumed;\n  return out;\n}\n\nexport function toPendingOutput(\n  part: ToolRequestPart,\n  response: ToolResponsePart\n): ToolRequestPart {\n  return {\n    ...part,\n    metadata: {\n      ...part.metadata,\n      pendingOutput: response.toolResponse.output,\n    },\n  };\n}\n\nexport async function resolveToolRequest(\n  rawRequest: GenerateActionOptions,\n  part: ToolRequestPart,\n  toolMap: Record<string, ToolAction>,\n  runOptions?: ToolRunOptions\n): Promise<{\n  response?: ToolResponsePart;\n  interrupt?: ToolRequestPart;\n  preamble?: GenerateActionOptions;\n}> {\n  const tool = toolMap[part.toolRequest.name];\n  if (!tool) {\n    throw new GenkitError({\n      status: 'NOT_FOUND',\n      message: `Tool ${part.toolRequest.name} not found`,\n      detail: { request: rawRequest },\n    });\n  }\n\n  // if it's a prompt action, go ahead and render the preamble\n  if (isPromptAction(tool)) {\n    const preamble = await tool(part.toolRequest.input);\n    const response = {\n      toolResponse: {\n        name: part.toolRequest.name,\n        ref: part.toolRequest.ref,\n        output: `transferred to ${part.toolRequest.name}`,\n      },\n    };\n\n    return { preamble, response };\n  }\n\n  // otherwise, execute the tool and catch interrupts\n  try {\n    const output = await tool(part.toolRequest.input, toRunOptions(part));\n    const response = stripUndefinedProps({\n      toolResponse: {\n        name: part.toolRequest.name,\n        ref: part.toolRequest.ref,\n        output,\n      },\n    });\n\n    return { response };\n  } catch (e) {\n    if (\n      e instanceof ToolInterruptError ||\n      // There's an inexplicable case when the above type check fails, only in tests.\n      (e as Error).name === 'ToolInterruptError'\n    ) {\n      const ie = e as ToolInterruptError;\n      logger.debug(\n        `tool '${toolMap[part.toolRequest?.name].__action.name}' triggered an interrupt${ie.metadata ? `: ${JSON.stringify(ie.metadata)}` : ''}`\n      );\n      const interrupt = {\n        toolRequest: part.toolRequest,\n        metadata: { ...part.metadata, interrupt: ie.metadata || true },\n      };\n\n      return { interrupt };\n    }\n\n    throw e;\n  }\n}\n\n/**\n * resolveToolRequests is responsible for executing the tools requested by the model for a single turn. it\n * returns either a toolMessage to append or a revisedModelMessage when an interrupt occurs, and a transferPreamble\n * if a prompt tool is called\n */\nexport async function resolveToolRequests(\n  registry: Registry,\n  rawRequest: GenerateActionOptions,\n  generatedMessage: MessageData\n): Promise<{\n  revisedModelMessage?: MessageData;\n  toolMessage?: MessageData;\n  transferPreamble?: GenerateActionOptions;\n}> {\n  const toolMap = toToolMap(await resolveTools(registry, rawRequest.tools));\n\n  const responseParts: ToolResponsePart[] = [];\n  let hasInterrupts = false;\n  let transferPreamble: GenerateActionOptions | undefined;\n\n  const revisedModelMessage = {\n    ...generatedMessage,\n    content: [...generatedMessage.content],\n  };\n\n  await Promise.all(\n    revisedModelMessage.content.map(async (part, i) => {\n      if (!part.toolRequest) return; // skip non-tool-request parts\n\n      const { preamble, response, interrupt } = await resolveToolRequest(\n        rawRequest,\n        part as ToolRequestPart,\n        toolMap\n      );\n\n      if (preamble) {\n        if (transferPreamble) {\n          throw new GenkitError({\n            status: 'INVALID_ARGUMENT',\n            message: `Model attempted to transfer to multiple prompt tools.`,\n          });\n        }\n\n        transferPreamble = preamble;\n      }\n\n      // this happens for preamble or normal tools\n      if (response) {\n        responseParts.push(response!);\n        revisedModelMessage.content.splice(\n          i,\n          1,\n          toPendingOutput(part, response)\n        );\n      }\n\n      if (interrupt) {\n        revisedModelMessage.content.splice(i, 1, interrupt);\n        hasInterrupts = true;\n      }\n    })\n  );\n\n  if (hasInterrupts) {\n    return { revisedModelMessage };\n  }\n\n  return {\n    toolMessage: { role: 'tool', content: responseParts },\n    transferPreamble,\n  };\n}\n\nfunction findCorrespondingToolRequest(\n  parts: Part[],\n  part: ToolRequestPart | ToolResponsePart\n): ToolRequestPart | undefined {\n  const name = part.toolRequest?.name || part.toolResponse?.name;\n  const ref = part.toolRequest?.ref || part.toolResponse?.ref;\n\n  return parts.find(\n    (p) => p.toolRequest?.name === name && p.toolRequest?.ref === ref\n  ) as ToolRequestPart | undefined;\n}\n\nfunction findCorrespondingToolResponse(\n  parts: Part[],\n  part: ToolRequestPart | ToolResponsePart\n): ToolResponsePart | undefined {\n  const name = part.toolRequest?.name || part.toolResponse?.name;\n  const ref = part.toolRequest?.ref || part.toolResponse?.ref;\n\n  return parts.find(\n    (p) => p.toolResponse?.name === name && p.toolResponse?.ref === ref\n  ) as ToolResponsePart | undefined;\n}\n\nasync function resolveResumedToolRequest(\n  rawRequest: GenerateActionOptions,\n  part: ToolRequestPart,\n  toolMap: Record<string, ToolAction>\n): Promise<{\n  toolRequest?: ToolRequestPart;\n  toolResponse?: ToolResponsePart;\n  interrupt?: ToolRequestPart;\n}> {\n  if (part.metadata?.pendingOutput) {\n    const { pendingOutput, ...metadata } = part.metadata;\n    const toolResponse = {\n      toolResponse: {\n        name: part.toolRequest.name,\n        ref: part.toolRequest.ref,\n        output: pendingOutput,\n      },\n      metadata: { ...metadata, source: 'pending' },\n    };\n\n    // strip pendingOutput from metadata when returning\n    return stripUndefinedProps({\n      toolResponse,\n      toolRequest: { ...part, metadata },\n    });\n  }\n\n  // if there's a corresponding reply, append it to toolResponses\n  const providedResponse = findCorrespondingToolResponse(\n    rawRequest.resume?.respond || [],\n    part\n  );\n  if (providedResponse) {\n    const toolResponse = providedResponse;\n\n    // remove the 'interrupt' but leave a 'resolvedInterrupt'\n    const { interrupt, ...metadata } = part.metadata || {};\n    return stripUndefinedProps({\n      toolResponse,\n      toolRequest: {\n        ...part,\n        metadata: { ...metadata, resolvedInterrupt: interrupt },\n      },\n    });\n  }\n\n  // if there's a corresponding restart, execute then add to toolResponses\n  const restartRequest = findCorrespondingToolRequest(\n    rawRequest.resume?.restart || [],\n    part\n  );\n  if (restartRequest) {\n    const { response, interrupt, preamble } = await resolveToolRequest(\n      rawRequest,\n      restartRequest,\n      toolMap\n    );\n\n    if (preamble) {\n      throw new GenkitError({\n        status: 'INTERNAL',\n        message: `Prompt tool '${restartRequest.toolRequest.name}' executed inside 'restart' resolution. This should never happen.`,\n      });\n    }\n\n    // if there's a new interrupt, return it\n    if (interrupt) return { interrupt };\n\n    if (response) {\n      const toolResponse = response;\n\n      // remove the 'interrupt' but leave a 'resolvedInterrupt'\n      const { interrupt, ...metadata } = part.metadata || {};\n      return stripUndefinedProps({\n        toolResponse,\n        toolRequest: {\n          ...part,\n          metadata: { ...metadata, resolvedInterrupt: interrupt },\n        },\n      });\n    }\n  }\n\n  throw new GenkitError({\n    status: 'INVALID_ARGUMENT',\n    message: `Unresolved tool request '${part.toolRequest.name}${part.toolRequest.ref ? `#${part.toolRequest.ref}` : ''}' was not handled by the 'resume' argument. You must supply replies or restarts for all interrupted tool requests.'`,\n  });\n}\n\n/** Amends message history to handle `resume` arguments. Returns the amended history. */\nexport async function resolveResumeOption(\n  registry: Registry,\n  rawRequest: GenerateActionOptions\n): Promise<{\n  revisedRequest?: GenerateActionOptions;\n  interruptedResponse?: GenerateResponseData;\n  toolMessage?: MessageData;\n}> {\n  if (!rawRequest.resume) return { revisedRequest: rawRequest }; // no-op if no resume option\n  const toolMap = toToolMap(await resolveTools(registry, rawRequest.tools));\n\n  const messages = rawRequest.messages;\n  const lastMessage = messages.at(-1);\n\n  if (\n    !lastMessage ||\n    lastMessage.role !== 'model' ||\n    !lastMessage.content.find((p) => p.toolRequest)\n  ) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message: `Cannot 'resume' generation unless the previous message is a model message with at least one tool request.`,\n    });\n  }\n\n  const toolResponses: ToolResponsePart[] = [];\n  let interrupted = false;\n\n  lastMessage.content = await Promise.all(\n    lastMessage.content.map(async (part) => {\n      if (!isToolRequest(part)) return part;\n      const resolved = await resolveResumedToolRequest(\n        rawRequest,\n        part,\n        toolMap\n      );\n      if (resolved.interrupt) {\n        interrupted = true;\n        return resolved.interrupt;\n      }\n\n      toolResponses.push(resolved.toolResponse!);\n      return resolved.toolRequest!;\n    })\n  );\n\n  if (interrupted) {\n    // TODO: figure out how to make this trigger an interrupt response.\n    return {\n      interruptedResponse: {\n        finishReason: 'interrupted',\n        finishMessage:\n          'One or more tools triggered interrupts while resuming generation. The model was not called.',\n        message: lastMessage,\n      },\n    };\n  }\n\n  const numToolRequests = lastMessage.content.filter(\n    (p) => !!p.toolRequest\n  ).length;\n  if (toolResponses.length !== numToolRequests) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message: `Expected ${numToolRequests} tool responses but resolved to ${toolResponses.length}.`,\n      detail: { toolResponses, message: lastMessage },\n    });\n  }\n\n  const toolMessage: MessageData = {\n    role: 'tool',\n    content: toolResponses,\n    metadata: {\n      resumed: rawRequest.resume.metadata || true,\n    },\n  };\n\n  return stripUndefinedProps({\n    revisedRequest: {\n      ...rawRequest,\n      resume: undefined,\n      messages: [...messages, toolMessage],\n    },\n    toolMessage,\n  });\n}\n\nexport async function resolveRestartedTools(\n  registry: Registry,\n  rawRequest: GenerateActionOptions\n): Promise<ToolRequestPart[]> {\n  const toolMap = toToolMap(await resolveTools(registry, rawRequest.tools));\n  const lastMessage = rawRequest.messages.at(-1);\n  if (!lastMessage || lastMessage.role !== 'model') return [];\n\n  const restarts = lastMessage.content.filter(\n    (p) => p.toolRequest && p.metadata?.resumed\n  ) as ToolRequestPart[];\n\n  return await Promise.all(\n    restarts.map(async (p) => {\n      const { response, interrupt } = await resolveToolRequest(\n        rawRequest,\n        p,\n        toolMap\n      );\n\n      // this means that it interrupted *again* after the restart\n      if (interrupt) return interrupt;\n      return toPendingOutput(p, response!);\n    })\n  );\n}\n"], "names": ["interrupt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gCAAA,CAAA;AAAA,SAAA,+BAAA;IAAA,sBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,WAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAAiD;AACjD,IAAA,iBAAuB;AAUvB,IAAA,gBAA+B;AAC/B,IAAA,cAMO;AAEA,SAAS,UAAU,KAAA,EAAiD;IACzE,qBAAqB,KAAK;IAC1B,MAAM,MAAkC,CAAC;IACzC,KAAA,MAAW,QAAQ,MAAO;QACxB,MAAM,OAAO,KAAK,QAAA,CAAS,IAAA;QAC3B,MAAM,YAAY,KAAK,SAAA,CAAU,KAAK,WAAA,CAAY,GAAG,IAAI,CAAC;QAC1D,GAAA,CAAI,SAAS,CAAA,GAAI;IACnB;IACA,OAAO;AACT;AAGO,SAAS,qBAAqB,KAAA,EAAqB;IACxD,MAAM,UAAkC,CAAC;IACzC,KAAA,MAAW,QAAQ,MAAO;QACxB,MAAM,OAAO,KAAK,QAAA,CAAS,IAAA;QAC3B,MAAM,YAAY,KAAK,SAAA,CAAU,KAAK,WAAA,CAAY,GAAG,IAAI,CAAC;QAC1D,IAAI,OAAA,CAAQ,SAAS,CAAA,EAAG;YACtB,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,8CAAA,EAAiD,IAAI,CAAA,OAAA,EAAU,OAAA,CAAQ,SAAS,CAAC,CAAA,CAAA,CAAA;YAC5F,CAAC;QACH;QACA,OAAA,CAAQ,SAAS,CAAA,GAAI;IACvB;AACF;AAEA,SAAS,aAAa,IAAA,EAAuC;IAC3D,MAAM,MAAsB;QAAE,UAAU,KAAK,QAAA;IAAS;IACtD,IAAI,KAAK,QAAA,EAAU,QAAS,CAAA,IAAI,OAAA,GAAU,KAAK,QAAA,CAAS,OAAA;IACxD,OAAO;AACT;AAEO,SAAS,gBACd,IAAA,EACA,QAAA,EACiB;IACjB,OAAO;QACL,GAAG,IAAA;QACH,UAAU;YACR,GAAG,KAAK,QAAA;YACR,eAAe,SAAS,YAAA,CAAa,MAAA;QACvC;IACF;AACF;AAEA,eAAsB,mBACpB,UAAA,EACA,IAAA,EACA,OAAA,EACA,UAAA,EAKC;IACD,MAAM,OAAO,OAAA,CAAQ,KAAK,WAAA,CAAY,IAAI,CAAA;IAC1C,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,KAAA,EAAQ,KAAK,WAAA,CAAY,IAAI,CAAA,UAAA,CAAA;YACtC,QAAQ;gBAAE,SAAS;YAAW;QAChC,CAAC;IACH;IAGA,IAAA,CAAA,GAAI,cAAA,cAAA,EAAe,IAAI,GAAG;QACxB,MAAM,WAAW,MAAM,KAAK,KAAK,WAAA,CAAY,KAAK;QAClD,MAAM,WAAW;YACf,cAAc;gBACZ,MAAM,KAAK,WAAA,CAAY,IAAA;gBACvB,KAAK,KAAK,WAAA,CAAY,GAAA;gBACtB,QAAQ,CAAA,eAAA,EAAkB,KAAK,WAAA,CAAY,IAAI,EAAA;YACjD;QACF;QAEA,OAAO;YAAE;YAAU;QAAS;IAC9B;IAGA,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,KAAK,WAAA,CAAY,KAAA,EAAO,aAAa,IAAI,CAAC;QACpE,MAAM,WAAA,CAAA,GAAW,YAAA,mBAAA,EAAoB;YACnC,cAAc;gBACZ,MAAM,KAAK,WAAA,CAAY,IAAA;gBACvB,KAAK,KAAK,WAAA,CAAY,GAAA;gBACtB;YACF;QACF,CAAC;QAED,OAAO;YAAE;QAAS;IACpB,EAAA,OAAS,GAAG;QACV,IACE,aAAa,YAAA,kBAAA,IAAA,+EAAA;QAEZ,EAAY,IAAA,KAAS,sBACtB;YACA,MAAM,KAAK;YACX,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,MAAA,EAAS,OAAA,CAAQ,KAAK,WAAA,EAAa,IAAI,CAAA,CAAE,QAAA,CAAS,IAAI,CAAA,wBAAA,EAA2B,GAAG,QAAA,GAAW,CAAA,EAAA,EAAK,KAAK,SAAA,CAAU,GAAG,QAAQ,CAAC,EAAA,GAAK,EAAE,EAAA;YAExI,MAAM,YAAY;gBAChB,aAAa,KAAK,WAAA;gBAClB,UAAU;oBAAE,GAAG,KAAK,QAAA;oBAAU,WAAW,GAAG,QAAA,IAAY;gBAAK;YAC/D;YAEA,OAAO;gBAAE;YAAU;QACrB;QAEA,MAAM;IACR;AACF;AAOA,eAAsB,oBACpB,QAAA,EACA,UAAA,EACA,gBAAA,EAKC;IACD,MAAM,UAAU,UAAU,MAAA,CAAA,GAAM,YAAA,YAAA,EAAa,UAAU,WAAW,KAAK,CAAC;IAExE,MAAM,gBAAoC,CAAC,CAAA;IAC3C,IAAI,gBAAgB;IACpB,IAAI;IAEJ,MAAM,sBAAsB;QAC1B,GAAG,gBAAA;QACH,SAAS,CAAC;eAAG,iBAAiB,OAAO;SAAA;IACvC;IAEA,MAAM,QAAQ,GAAA,CACZ,oBAAoB,OAAA,CAAQ,GAAA,CAAI,OAAO,MAAM,MAAM;QACjD,IAAI,CAAC,KAAK,WAAA,CAAa,CAAA;QAEvB,MAAM,EAAE,QAAA,EAAU,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI,MAAM,mBAC9C,YACA,MACA;QAGF,IAAI,UAAU;YACZ,IAAI,kBAAkB;gBACpB,MAAM,IAAI,YAAA,WAAA,CAAY;oBACpB,QAAQ;oBACR,SAAS,CAAA,qDAAA,CAAA;gBACX,CAAC;YACH;YAEA,mBAAmB;QACrB;QAGA,IAAI,UAAU;YACZ,cAAc,IAAA,CAAK,QAAS;YAC5B,oBAAoB,OAAA,CAAQ,MAAA,CAC1B,GACA,GACA,gBAAgB,MAAM,QAAQ;QAElC;QAEA,IAAI,WAAW;YACb,oBAAoB,OAAA,CAAQ,MAAA,CAAO,GAAG,GAAG,SAAS;YAClD,gBAAgB;QAClB;IACF,CAAC;IAGH,IAAI,eAAe;QACjB,OAAO;YAAE;QAAoB;IAC/B;IAEA,OAAO;QACL,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAc;QACpD;IACF;AACF;AAEA,SAAS,6BACP,KAAA,EACA,IAAA,EAC6B;IAC7B,MAAM,OAAO,KAAK,WAAA,EAAa,QAAQ,KAAK,YAAA,EAAc;IAC1D,MAAM,MAAM,KAAK,WAAA,EAAa,OAAO,KAAK,YAAA,EAAc;IAExD,OAAO,MAAM,IAAA,CACX,CAAC,IAAM,EAAE,WAAA,EAAa,SAAS,QAAQ,EAAE,WAAA,EAAa,QAAQ;AAElE;AAEA,SAAS,8BACP,KAAA,EACA,IAAA,EAC8B;IAC9B,MAAM,OAAO,KAAK,WAAA,EAAa,QAAQ,KAAK,YAAA,EAAc;IAC1D,MAAM,MAAM,KAAK,WAAA,EAAa,OAAO,KAAK,YAAA,EAAc;IAExD,OAAO,MAAM,IAAA,CACX,CAAC,IAAM,EAAE,YAAA,EAAc,SAAS,QAAQ,EAAE,YAAA,EAAc,QAAQ;AAEpE;AAEA,eAAe,0BACb,UAAA,EACA,IAAA,EACA,OAAA,EAKC;IACD,IAAI,KAAK,QAAA,EAAU,eAAe;QAChC,MAAM,EAAE,aAAA,EAAe,GAAG,SAAS,CAAA,GAAI,KAAK,QAAA;QAC5C,MAAM,eAAe;YACnB,cAAc;gBACZ,MAAM,KAAK,WAAA,CAAY,IAAA;gBACvB,KAAK,KAAK,WAAA,CAAY,GAAA;gBACtB,QAAQ;YACV;YACA,UAAU;gBAAE,GAAG,QAAA;gBAAU,QAAQ;YAAU;QAC7C;QAGA,OAAA,CAAA,GAAO,YAAA,mBAAA,EAAoB;YACzB;YACA,aAAa;gBAAE,GAAG,IAAA;gBAAM;YAAS;QACnC,CAAC;IACH;IAGA,MAAM,mBAAmB,8BACvB,WAAW,MAAA,EAAQ,WAAW,CAAC,CAAA,EAC/B;IAEF,IAAI,kBAAkB;QACpB,MAAM,eAAe;QAGrB,MAAM,EAAE,SAAA,EAAW,GAAG,SAAS,CAAA,GAAI,KAAK,QAAA,IAAY,CAAC;QACrD,OAAA,CAAA,GAAO,YAAA,mBAAA,EAAoB;YACzB;YACA,aAAa;gBACX,GAAG,IAAA;gBACH,UAAU;oBAAE,GAAG,QAAA;oBAAU,mBAAmB;gBAAU;YACxD;QACF,CAAC;IACH;IAGA,MAAM,iBAAiB,6BACrB,WAAW,MAAA,EAAQ,WAAW,CAAC,CAAA,EAC/B;IAEF,IAAI,gBAAgB;QAClB,MAAM,EAAE,QAAA,EAAU,SAAA,EAAW,QAAA,CAAS,CAAA,GAAI,MAAM,mBAC9C,YACA,gBACA;QAGF,IAAI,UAAU;YACZ,MAAM,IAAI,YAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,aAAA,EAAgB,eAAe,WAAA,CAAY,IAAI,CAAA,iEAAA,CAAA;YAC1D,CAAC;QACH;QAGA,IAAI,UAAW,CAAA,OAAO;YAAE;QAAU;QAElC,IAAI,UAAU;YACZ,MAAM,eAAe;YAGrB,MAAM,EAAE,WAAAA,UAAAA,EAAW,GAAG,SAAS,CAAA,GAAI,KAAK,QAAA,IAAY,CAAC;YACrD,OAAA,CAAA,GAAO,YAAA,mBAAA,EAAoB;gBACzB;gBACA,aAAa;oBACX,GAAG,IAAA;oBACH,UAAU;wBAAE,GAAG,QAAA;wBAAU,mBAAmBA;oBAAU;gBACxD;YACF,CAAC;QACH;IACF;IAEA,MAAM,IAAI,YAAA,WAAA,CAAY;QACpB,QAAQ;QACR,SAAS,CAAA,yBAAA,EAA4B,KAAK,WAAA,CAAY,IAAI,GAAG,KAAK,WAAA,CAAY,GAAA,GAAM,CAAA,CAAA,EAAI,KAAK,WAAA,CAAY,GAAG,EAAA,GAAK,EAAE,CAAA,mHAAA,CAAA;IACrH,CAAC;AACH;AAGA,eAAsB,oBACpB,QAAA,EACA,UAAA,EAKC;IACD,IAAI,CAAC,WAAW,MAAA,CAAQ,CAAA,OAAO;QAAE,gBAAgB;IAAW;IAC5D,MAAM,UAAU,UAAU,MAAA,CAAA,GAAM,YAAA,YAAA,EAAa,UAAU,WAAW,KAAK,CAAC;IAExE,MAAM,WAAW,WAAW,QAAA;IAC5B,MAAM,cAAc,SAAS,EAAA,CAAG,CAAA,CAAE;IAElC,IACE,CAAC,eACD,YAAY,IAAA,KAAS,WACrB,CAAC,YAAY,OAAA,CAAQ,IAAA,CAAK,CAAC,IAAM,EAAE,WAAW,GAC9C;QACA,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,yGAAA,CAAA;QACX,CAAC;IACH;IAEA,MAAM,gBAAoC,CAAC,CAAA;IAC3C,IAAI,cAAc;IAElB,YAAY,OAAA,GAAU,MAAM,QAAQ,GAAA,CAClC,YAAY,OAAA,CAAQ,GAAA,CAAI,OAAO,SAAS;QACtC,IAAI,CAAA,CAAA,GAAC,YAAA,aAAA,EAAc,IAAI,EAAG,CAAA,OAAO;QACjC,MAAM,WAAW,MAAM,0BACrB,YACA,MACA;QAEF,IAAI,SAAS,SAAA,EAAW;YACtB,cAAc;YACd,OAAO,SAAS,SAAA;QAClB;QAEA,cAAc,IAAA,CAAK,SAAS,YAAa;QACzC,OAAO,SAAS,WAAA;IAClB,CAAC;IAGH,IAAI,aAAa;QAEf,OAAO;YACL,qBAAqB;gBACnB,cAAc;gBACd,eACE;gBACF,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB,YAAY,OAAA,CAAQ,MAAA,CAC1C,CAAC,IAAM,CAAC,CAAC,EAAE,WAAA,EACX,MAAA;IACF,IAAI,cAAc,MAAA,KAAW,iBAAiB;QAC5C,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,SAAA,EAAY,eAAe,CAAA,gCAAA,EAAmC,cAAc,MAAM,CAAA,CAAA,CAAA;YAC3F,QAAQ;gBAAE;gBAAe,SAAS;YAAY;QAChD,CAAC;IACH;IAEA,MAAM,cAA2B;QAC/B,MAAM;QACN,SAAS;QACT,UAAU;YACR,SAAS,WAAW,MAAA,CAAO,QAAA,IAAY;QACzC;IACF;IAEA,OAAA,CAAA,GAAO,YAAA,mBAAA,EAAoB;QACzB,gBAAgB;YACd,GAAG,UAAA;YACH,QAAQ,KAAA;YACR,UAAU,CAAC;mBAAG;gBAAU,WAAW;aAAA;QACrC;QACA;IACF,CAAC;AACH;AAEA,eAAsB,sBACpB,QAAA,EACA,UAAA,EAC4B;IAC5B,MAAM,UAAU,UAAU,MAAA,CAAA,GAAM,YAAA,YAAA,EAAa,UAAU,WAAW,KAAK,CAAC;IACxE,MAAM,cAAc,WAAW,QAAA,CAAS,EAAA,CAAG,CAAA,CAAE;IAC7C,IAAI,CAAC,eAAe,YAAY,IAAA,KAAS,QAAS,CAAA,OAAO,CAAC,CAAA;IAE1D,MAAM,WAAW,YAAY,OAAA,CAAQ,MAAA,CACnC,CAAC,IAAM,EAAE,WAAA,IAAe,EAAE,QAAA,EAAU;IAGtC,OAAO,MAAM,QAAQ,GAAA,CACnB,SAAS,GAAA,CAAI,OAAO,MAAM;QACxB,MAAM,EAAE,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI,MAAM,mBACpC,YACA,GACA;QAIF,IAAI,UAAW,CAAA,OAAO;QACtB,OAAO,gBAAgB,GAAG,QAAS;IACrC,CAAC;AAEL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3912, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/generate/action.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenkitError,\n  defineAction,\n  getStreamingCallback,\n  runWithStreamingCallback,\n  stripUndefinedProps,\n  type Action,\n  type z,\n} from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from '@genkit-ai/core/tracing';\nimport {\n  injectInstructions,\n  resolveFormat,\n  resolveInstructions,\n} from '../formats/index.js';\nimport type { Formatter } from '../formats/types.js';\nimport {\n  GenerateResponse,\n  GenerateResponseChunk,\n  GenerationResponseError,\n  tagAsPreamble,\n} from '../generate.js';\nimport {\n  GenerateActionOptionsSchema,\n  GenerateResponseChunkSchema,\n  GenerateResponseSchema,\n  MessageData,\n  resolveModel,\n  type GenerateActionOptions,\n  type GenerateActionOutputConfig,\n  type GenerateRequest,\n  type GenerateRequestSchema,\n  type GenerateResponseChunkData,\n  type GenerateResponseData,\n  type ModelAction,\n  type ModelInfo,\n  type ModelMiddleware,\n  type ModelRequest,\n  type Part,\n  type Role,\n} from '../model.js';\nimport { findMatchingResource } from '../resource.js';\nimport { resolveTools, toToolDefinition, type ToolAction } from '../tool.js';\nimport {\n  assertValidToolNames,\n  resolveResumeOption,\n  resolveToolRequests,\n} from './resolve-tool-requests.js';\n\nexport type GenerateAction = Action<\n  typeof GenerateActionOptionsSchema,\n  typeof GenerateResponseSchema,\n  typeof GenerateResponseChunkSchema\n>;\n\n/** Defines (registers) a utilty generate action. */\nexport function defineGenerateAction(registry: Registry): GenerateAction {\n  return defineAction(\n    registry,\n    {\n      actionType: 'util',\n      name: 'generate',\n      inputSchema: GenerateActionOptionsSchema,\n      outputSchema: GenerateResponseSchema,\n      streamSchema: GenerateResponseChunkSchema,\n    },\n    async (request, { streamingRequested, sendChunk }) => {\n      const generateFn = () =>\n        generate(registry, {\n          rawRequest: request,\n          currentTurn: 0,\n          messageIndex: 0,\n          // Generate util action does not support middleware. Maybe when we add named/registered middleware....\n          middleware: [],\n        });\n      return streamingRequested\n        ? runWithStreamingCallback(\n            registry,\n            (c: GenerateResponseChunk) => sendChunk(c.toJSON ? c.toJSON() : c),\n            generateFn\n          )\n        : generateFn();\n    }\n  );\n}\n\n/**\n * Encapsulates all generate logic. This is similar to `generateAction` except not an action and can take middleware.\n */\nexport async function generateHelper(\n  registry: Registry,\n  options: {\n    rawRequest: GenerateActionOptions;\n    middleware?: ModelMiddleware[];\n    currentTurn?: number;\n    messageIndex?: number;\n    abortSignal?: AbortSignal;\n  }\n): Promise<GenerateResponseData> {\n  const currentTurn = options.currentTurn ?? 0;\n  const messageIndex = options.messageIndex ?? 0;\n  // do tracing\n  return await runInNewSpan(\n    registry,\n    {\n      metadata: {\n        name: 'generate',\n      },\n      labels: {\n        [SPAN_TYPE_ATTR]: 'util',\n      },\n    },\n    async (metadata) => {\n      metadata.name = 'generate';\n      metadata.input = options.rawRequest;\n      const output = await generate(registry, {\n        rawRequest: options.rawRequest,\n        middleware: options.middleware,\n        currentTurn,\n        messageIndex,\n        abortSignal: options.abortSignal,\n      });\n      metadata.output = JSON.stringify(output);\n      return output;\n    }\n  );\n}\n\n/** Take the raw request and resolve tools, model, and format into their registry action counterparts. */\nasync function resolveParameters(\n  registry: Registry,\n  request: GenerateActionOptions\n) {\n  const [model, tools, format] = await Promise.all([\n    resolveModel(registry, request.model, { warnDeprecated: true }).then(\n      (r) => r.modelAction\n    ),\n    resolveTools(registry, request.tools),\n    resolveFormat(registry, request.output),\n  ]);\n  return { model, tools, format };\n}\n\n/** Given a raw request and a formatter, apply the formatter's logic and instructions to the request. */\nfunction applyFormat(\n  rawRequest: GenerateActionOptions,\n  resolvedFormat?: Formatter\n) {\n  const outRequest = { ...rawRequest };\n  // If is schema is set but format is not explicitly set, default to `json` format.\n  if (rawRequest.output?.jsonSchema && !rawRequest.output?.format) {\n    outRequest.output = { ...rawRequest.output, format: 'json' };\n  }\n\n  const instructions = resolveInstructions(\n    resolvedFormat,\n    outRequest.output?.jsonSchema,\n    outRequest?.output?.instructions\n  );\n\n  if (resolvedFormat) {\n    if (\n      shouldInjectFormatInstructions(resolvedFormat.config, rawRequest?.output)\n    ) {\n      outRequest.messages = injectInstructions(\n        outRequest.messages,\n        instructions\n      );\n    }\n    outRequest.output = {\n      // use output config from the format\n      ...resolvedFormat.config,\n      // if anything is set explicitly, use that\n      ...outRequest.output,\n    };\n  }\n\n  return outRequest;\n}\n\nexport function shouldInjectFormatInstructions(\n  formatConfig?: Formatter['config'],\n  rawRequestConfig?: z.infer<typeof GenerateActionOutputConfig>\n) {\n  return (\n    formatConfig?.defaultInstructions !== false ||\n    rawRequestConfig?.instructions\n  );\n}\n\nfunction applyTransferPreamble(\n  rawRequest: GenerateActionOptions,\n  transferPreamble?: GenerateActionOptions\n): GenerateActionOptions {\n  if (!transferPreamble) {\n    return rawRequest;\n  }\n\n  return stripUndefinedProps({\n    ...rawRequest,\n    messages: [\n      ...tagAsPreamble(transferPreamble.messages!)!,\n      ...rawRequest.messages.filter((m) => !m.metadata?.preamble),\n    ],\n    toolChoice: transferPreamble.toolChoice || rawRequest.toolChoice,\n    tools: transferPreamble.tools || rawRequest.tools,\n    config: transferPreamble.config || rawRequest.config,\n  });\n}\n\nasync function generate(\n  registry: Registry,\n  {\n    rawRequest,\n    middleware,\n    currentTurn,\n    messageIndex,\n    abortSignal,\n  }: {\n    rawRequest: GenerateActionOptions;\n    middleware: ModelMiddleware[] | undefined;\n    currentTurn: number;\n    messageIndex: number;\n    abortSignal?: AbortSignal;\n  }\n): Promise<GenerateResponseData> {\n  const { model, tools, format } = await resolveParameters(\n    registry,\n    rawRequest\n  );\n  rawRequest = applyFormat(rawRequest, format);\n  rawRequest = await applyResources(registry, rawRequest);\n\n  // check to make sure we don't have overlapping tool names *before* generation\n  await assertValidToolNames(tools);\n\n  const {\n    revisedRequest,\n    interruptedResponse,\n    toolMessage: resumedToolMessage,\n  } = await resolveResumeOption(registry, rawRequest);\n  // NOTE: in the future we should make it possible to interrupt a restart, but\n  // at the moment it's too complicated because it's not clear how to return a\n  // response that amends history but doesn't generate a new message, so we throw\n  if (interruptedResponse) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message:\n        'One or more tools triggered an interrupt during a restarted execution.',\n      detail: { message: interruptedResponse.message },\n    });\n  }\n  rawRequest = revisedRequest!;\n\n  const request = await actionToGenerateRequest(\n    rawRequest,\n    tools,\n    format,\n    model\n  );\n\n  const previousChunks: GenerateResponseChunkData[] = [];\n\n  let chunkRole: Role = 'model';\n  // convenience method to create a full chunk from role and data, append the chunk\n  // to the previousChunks array, and increment the message index as needed\n  const makeChunk = (\n    role: Role,\n    chunk: GenerateResponseChunkData\n  ): GenerateResponseChunk => {\n    if (role !== chunkRole && previousChunks.length) messageIndex++;\n    chunkRole = role;\n\n    const prevToSend = [...previousChunks];\n    previousChunks.push(chunk);\n\n    return new GenerateResponseChunk(chunk, {\n      index: messageIndex,\n      role,\n      previousChunks: prevToSend,\n      parser: format?.handler(request.output?.schema).parseChunk,\n    });\n  };\n\n  const streamingCallback = getStreamingCallback(registry);\n\n  // if resolving the 'resume' option above generated a tool message, stream it.\n  if (resumedToolMessage && streamingCallback) {\n    streamingCallback(makeChunk('tool', resumedToolMessage));\n  }\n\n  const response = await runWithStreamingCallback(\n    registry,\n    streamingCallback &&\n      ((chunk: GenerateResponseChunkData) =>\n        streamingCallback(makeChunk('model', chunk))),\n    async () => {\n      const dispatch = async (\n        index: number,\n        req: z.infer<typeof GenerateRequestSchema>\n      ) => {\n        if (!middleware || index === middleware.length) {\n          // end of the chain, call the original model action\n          return await model(req, { abortSignal });\n        }\n\n        const currentMiddleware = middleware[index];\n        return currentMiddleware(req, async (modifiedReq) =>\n          dispatch(index + 1, modifiedReq || req)\n        );\n      };\n\n      const modelResponse = await dispatch(0, request);\n\n      if (model.__action.actionType === 'background-model') {\n        return new GenerateResponse(\n          { operation: modelResponse },\n          {\n            request,\n            parser: format?.handler(request.output?.schema).parseMessage,\n          }\n        );\n      }\n\n      return new GenerateResponse(modelResponse, {\n        request,\n        parser: format?.handler(request.output?.schema).parseMessage,\n      });\n    }\n  );\n  if (model.__action.actionType === 'background-model') {\n    return response.toJSON();\n  }\n\n  // Throw an error if the response is not usable.\n  response.assertValid();\n  const generatedMessage = response.message!; // would have thrown if no message\n\n  const toolRequests = generatedMessage.content.filter(\n    (part) => !!part.toolRequest\n  );\n\n  if (rawRequest.returnToolRequests || toolRequests.length === 0) {\n    if (toolRequests.length === 0) response.assertValidSchema(request);\n    return response.toJSON();\n  }\n\n  const maxIterations = rawRequest.maxTurns ?? 5;\n  if (currentTurn + 1 > maxIterations) {\n    throw new GenerationResponseError(\n      response,\n      `Exceeded maximum tool call iterations (${maxIterations})`,\n      'ABORTED',\n      { request }\n    );\n  }\n\n  const { revisedModelMessage, toolMessage, transferPreamble } =\n    await resolveToolRequests(registry, rawRequest, generatedMessage);\n\n  // if an interrupt message is returned, stop the tool loop and return a response\n  if (revisedModelMessage) {\n    return {\n      ...response.toJSON(),\n      finishReason: 'interrupted',\n      finishMessage: 'One or more tool calls resulted in interrupts.',\n      message: revisedModelMessage,\n    };\n  }\n\n  // if the loop will continue, stream out the tool response message...\n  streamingCallback?.(\n    makeChunk('tool', {\n      content: toolMessage!.content,\n    })\n  );\n\n  let nextRequest = {\n    ...rawRequest,\n    messages: [...rawRequest.messages, generatedMessage.toJSON(), toolMessage!],\n  };\n  nextRequest = applyTransferPreamble(nextRequest, transferPreamble);\n\n  // then recursively call for another loop\n  return await generateHelper(registry, {\n    rawRequest: nextRequest,\n    middleware: middleware,\n    currentTurn: currentTurn + 1,\n    messageIndex: messageIndex + 1,\n  });\n}\n\nasync function actionToGenerateRequest(\n  options: GenerateActionOptions,\n  resolvedTools: ToolAction[] | undefined,\n  resolvedFormat: Formatter | undefined,\n  model: ModelAction\n): Promise<GenerateRequest> {\n  const modelInfo = model.__action.metadata?.model as ModelInfo;\n  if (\n    (options.tools?.length ?? 0) > 0 &&\n    modelInfo?.supports &&\n    !modelInfo?.supports?.tools\n  ) {\n    logger.warn(\n      `The model '${model.__action.name}' does not support tools (you set: ${options.tools?.length} tools). ` +\n        'The model may not behave the way you expect.'\n    );\n  }\n  if (\n    options.toolChoice &&\n    modelInfo?.supports &&\n    !modelInfo?.supports?.toolChoice\n  ) {\n    logger.warn(\n      `The model '${model.__action.name}' does not support the 'toolChoice' option (you set: ${options.toolChoice}). ` +\n        'The model may not behave the way you expect.'\n    );\n  }\n  const out: ModelRequest = {\n    messages: options.messages,\n    config: options.config,\n    docs: options.docs,\n    tools: resolvedTools?.map(toToolDefinition) || [],\n    output: stripUndefinedProps({\n      constrained: options.output?.constrained,\n      contentType: options.output?.contentType,\n      format: options.output?.format,\n      schema: options.output?.jsonSchema,\n    }),\n  };\n  if (options.toolChoice) {\n    out.toolChoice = options.toolChoice;\n  }\n  if (out.output && !out.output.schema) delete out.output.schema;\n  return out;\n}\n\nexport function inferRoleFromParts(parts: Part[]): Role {\n  const uniqueRoles = new Set<Role>();\n  for (const part of parts) {\n    const role = getRoleFromPart(part);\n    uniqueRoles.add(role);\n    if (uniqueRoles.size > 1) {\n      throw new Error('Contents contain mixed roles');\n    }\n  }\n  return Array.from(uniqueRoles)[0];\n}\n\nfunction getRoleFromPart(part: Part): Role {\n  if (part.toolRequest !== undefined) return 'model';\n  if (part.toolResponse !== undefined) return 'tool';\n  if (part.text !== undefined) return 'user';\n  if (part.media !== undefined) return 'user';\n  if (part.data !== undefined) return 'user';\n  throw new Error('No recognized fields in content');\n}\n\nasync function applyResources(\n  registry: Registry,\n  rawRequest: GenerateActionOptions\n): Promise<GenerateActionOptions> {\n  // quick check, if no resources bail.\n  if (!rawRequest.messages.find((m) => !!m.content.find((c) => c.resource))) {\n    return rawRequest;\n  }\n\n  const updatedMessages = [] as MessageData[];\n  for (const m of rawRequest.messages) {\n    if (!m.content.find((c) => c.resource)) {\n      updatedMessages.push(m);\n      continue;\n    }\n    const updatedContent = [] as Part[];\n    for (const p of m.content) {\n      if (!p.resource) {\n        updatedContent.push(p);\n        continue;\n      }\n      const resource = await findMatchingResource(registry, p.resource);\n      if (!resource) {\n        throw new GenkitError({\n          status: 'NOT_FOUND',\n          message: `failed to find matching resource for ${p.resource.uri}`,\n        });\n      }\n      const resourceParts = await resource(p.resource);\n      updatedContent.push(...resourceParts.content);\n    }\n\n    updatedMessages.push({\n      ...m,\n      content: updatedContent,\n    });\n  }\n\n  return {\n    ...rawRequest,\n    messages: updatedMessages,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,iBAAA,CAAA;AAAA,SAAA,gBAAA;IAAA,sBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,gCAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAQO;AACP,IAAA,iBAAuB;AAEvB,IAAA,iBAA6C;AAC7C,IAAA,iBAIO;AAEP,IAAA,kBAKO;AACP,IAAA,eAkBO;AACP,IAAA,kBAAqC;AACrC,IAAA,cAAgE;AAChE,IAAA,+BAIO;AASA,SAAS,qBAAqB,QAAA,EAAoC;IACvE,OAAA,CAAA,GAAO,YAAA,YAAA,EACL,UACA;QACE,YAAY;QACZ,MAAM;QACN,aAAa,aAAA,2BAAA;QACb,cAAc,aAAA,sBAAA;QACd,cAAc,aAAA,2BAAA;IAChB,GACA,OAAO,SAAS,EAAE,kBAAA,EAAoB,SAAA,CAAU,CAAA,KAAM;QACpD,MAAM,aAAa,IACjB,SAAS,UAAU;gBACjB,YAAY;gBACZ,aAAa;gBACb,cAAc;gBAAA,sGAAA;gBAEd,YAAY,CAAC,CAAA;YACf,CAAC;QACH,OAAO,qBAAA,CAAA,GACH,YAAA,wBAAA,EACE,UACA,CAAC,IAA6B,UAAU,EAAE,MAAA,GAAS,EAAE,MAAA,CAAO,IAAI,CAAC,GACjE,cAEF,WAAW;IACjB;AAEJ;AAKA,eAAsB,eACpB,QAAA,EACA,OAAA,EAO+B;IAC/B,MAAM,cAAc,QAAQ,WAAA,IAAe;IAC3C,MAAM,eAAe,QAAQ,YAAA,IAAgB;IAE7C,OAAO,MAAA,CAAA,GAAM,eAAA,YAAA,EACX,UACA;QACE,UAAU;YACR,MAAM;QACR;QACA,QAAQ;YACN,CAAC,eAAA,cAAc,CAAA,EAAG;QACpB;IACF,GACA,OAAO,aAAa;QAClB,SAAS,IAAA,GAAO;QAChB,SAAS,KAAA,GAAQ,QAAQ,UAAA;QACzB,MAAM,SAAS,MAAM,SAAS,UAAU;YACtC,YAAY,QAAQ,UAAA;YACpB,YAAY,QAAQ,UAAA;YACpB;YACA;YACA,aAAa,QAAQ,WAAA;QACvB,CAAC;QACD,SAAS,MAAA,GAAS,KAAK,SAAA,CAAU,MAAM;QACvC,OAAO;IACT;AAEJ;AAGA,eAAe,kBACb,QAAA,EACA,OAAA,EACA;IACA,MAAM,CAAC,OAAO,OAAO,MAAM,CAAA,GAAI,MAAM,QAAQ,GAAA,CAAI;QAAA,CAAA,GAC/C,aAAA,YAAA,EAAa,UAAU,QAAQ,KAAA,EAAO;YAAE,gBAAgB;QAAK,CAAC,EAAE,IAAA,CAC9D,CAAC,IAAM,EAAE,WAAA;QACX,CAAA,GACA,YAAA,YAAA,EAAa,UAAU,QAAQ,KAAK;QAAA,CAAA,GACpC,eAAA,aAAA,EAAc,UAAU,QAAQ,MAAM;KACvC;IACD,OAAO;QAAE;QAAO;QAAO;IAAO;AAChC;AAGA,SAAS,YACP,UAAA,EACA,cAAA,EACA;IACA,MAAM,aAAa;QAAE,GAAG,UAAA;IAAW;IAEnC,IAAI,WAAW,MAAA,EAAQ,cAAc,CAAC,WAAW,MAAA,EAAQ,QAAQ;QAC/D,WAAW,MAAA,GAAS;YAAE,GAAG,WAAW,MAAA;YAAQ,QAAQ;QAAO;IAC7D;IAEA,MAAM,eAAA,CAAA,GAAe,eAAA,mBAAA,EACnB,gBACA,WAAW,MAAA,EAAQ,YACnB,YAAY,QAAQ;IAGtB,IAAI,gBAAgB;QAClB,IACE,+BAA+B,eAAe,MAAA,EAAQ,YAAY,MAAM,GACxE;YACA,WAAW,QAAA,GAAA,CAAA,GAAW,eAAA,kBAAA,EACpB,WAAW,QAAA,EACX;QAEJ;QACA,WAAW,MAAA,GAAS;YAAA,oCAAA;YAElB,GAAG,eAAe,MAAA;YAAA,0CAAA;YAElB,GAAG,WAAW,MAAA;QAChB;IACF;IAEA,OAAO;AACT;AAEO,SAAS,+BACd,YAAA,EACA,gBAAA,EACA;IACA,OACE,cAAc,wBAAwB,SACtC,kBAAkB;AAEtB;AAEA,SAAS,sBACP,UAAA,EACA,gBAAA,EACuB;IACvB,IAAI,CAAC,kBAAkB;QACrB,OAAO;IACT;IAEA,OAAA,CAAA,GAAO,YAAA,mBAAA,EAAoB;QACzB,GAAG,UAAA;QACH,UAAU;eACR,CAAA,GAAG,gBAAA,aAAA,EAAc,iBAAiB,QAAS;eACxC,WAAW,QAAA,CAAS,MAAA,CAAO,CAAC,IAAM,CAAC,EAAE,QAAA,EAAU,QAAQ;SAC5D;QACA,YAAY,iBAAiB,UAAA,IAAc,WAAW,UAAA;QACtD,OAAO,iBAAiB,KAAA,IAAS,WAAW,KAAA;QAC5C,QAAQ,iBAAiB,MAAA,IAAU,WAAW,MAAA;IAChD,CAAC;AACH;AAEA,eAAe,SACb,QAAA,EACA,EACE,UAAA,EACA,UAAA,EACA,WAAA,EACA,YAAA,EACA,WAAA,EACF,EAO+B;IAC/B,MAAM,EAAE,KAAA,EAAO,KAAA,EAAO,MAAA,CAAO,CAAA,GAAI,MAAM,kBACrC,UACA;IAEF,aAAa,YAAY,YAAY,MAAM;IAC3C,aAAa,MAAM,eAAe,UAAU,UAAU;IAGtD,MAAA,CAAA,GAAM,6BAAA,oBAAA,EAAqB,KAAK;IAEhC,MAAM,EACJ,cAAA,EACA,mBAAA,EACA,aAAa,kBAAA,EACf,GAAI,MAAA,CAAA,GAAM,6BAAA,mBAAA,EAAoB,UAAU,UAAU;IAIlD,IAAI,qBAAqB;QACvB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SACE;YACF,QAAQ;gBAAE,SAAS,oBAAoB,OAAA;YAAQ;QACjD,CAAC;IACH;IACA,aAAa;IAEb,MAAM,UAAU,MAAM,wBACpB,YACA,OACA,QACA;IAGF,MAAM,iBAA8C,CAAC,CAAA;IAErD,IAAI,YAAkB;IAGtB,MAAM,YAAY,CAChB,MACA,UAC0B;QAC1B,IAAI,SAAS,aAAa,eAAe,MAAA,CAAQ,CAAA;QACjD,YAAY;QAEZ,MAAM,aAAa,CAAC;eAAG,cAAc;SAAA;QACrC,eAAe,IAAA,CAAK,KAAK;QAEzB,OAAO,IAAI,gBAAA,qBAAA,CAAsB,OAAO;YACtC,OAAO;YACP;YACA,gBAAgB;YAChB,QAAQ,QAAQ,QAAQ,QAAQ,MAAA,EAAQ,MAAM,EAAE;QAClD,CAAC;IACH;IAEA,MAAM,oBAAA,CAAA,GAAoB,YAAA,oBAAA,EAAqB,QAAQ;IAGvD,IAAI,sBAAsB,mBAAmB;QAC3C,kBAAkB,UAAU,QAAQ,kBAAkB,CAAC;IACzD;IAEA,MAAM,WAAW,MAAA,CAAA,GAAM,YAAA,wBAAA,EACrB,UACA,qBAAA,CACG,CAAC,QACA,kBAAkB,UAAU,SAAS,KAAK,CAAC,CAAA,GAC/C,YAAY;QACV,MAAM,WAAW,OACf,OACA,QACG;YACH,IAAI,CAAC,cAAc,UAAU,WAAW,MAAA,EAAQ;gBAE9C,OAAO,MAAM,MAAM,KAAK;oBAAE;gBAAY,CAAC;YACzC;YAEA,MAAM,oBAAoB,UAAA,CAAW,KAAK,CAAA;YAC1C,OAAO,kBAAkB,KAAK,OAAO,cACnC,SAAS,QAAQ,GAAG,eAAe,GAAG;QAE1C;QAEA,MAAM,gBAAgB,MAAM,SAAS,GAAG,OAAO;QAE/C,IAAI,MAAM,QAAA,CAAS,UAAA,KAAe,oBAAoB;YACpD,OAAO,IAAI,gBAAA,gBAAA,CACT;gBAAE,WAAW;YAAc,GAC3B;gBACE;gBACA,QAAQ,QAAQ,QAAQ,QAAQ,MAAA,EAAQ,MAAM,EAAE;YAClD;QAEJ;QAEA,OAAO,IAAI,gBAAA,gBAAA,CAAiB,eAAe;YACzC;YACA,QAAQ,QAAQ,QAAQ,QAAQ,MAAA,EAAQ,MAAM,EAAE;QAClD,CAAC;IACH;IAEF,IAAI,MAAM,QAAA,CAAS,UAAA,KAAe,oBAAoB;QACpD,OAAO,SAAS,MAAA,CAAO;IACzB;IAGA,SAAS,WAAA,CAAY;IACrB,MAAM,mBAAmB,SAAS,OAAA;IAElC,MAAM,eAAe,iBAAiB,OAAA,CAAQ,MAAA,CAC5C,CAAC,OAAS,CAAC,CAAC,KAAK,WAAA;IAGnB,IAAI,WAAW,kBAAA,IAAsB,aAAa,MAAA,KAAW,GAAG;QAC9D,IAAI,aAAa,MAAA,KAAW,EAAG,CAAA,SAAS,iBAAA,CAAkB,OAAO;QACjE,OAAO,SAAS,MAAA,CAAO;IACzB;IAEA,MAAM,gBAAgB,WAAW,QAAA,IAAY;IAC7C,IAAI,cAAc,IAAI,eAAe;QACnC,MAAM,IAAI,gBAAA,uBAAA,CACR,UACA,CAAA,uCAAA,EAA0C,aAAa,CAAA,CAAA,CAAA,EACvD,WACA;YAAE;QAAQ;IAEd;IAEA,MAAM,EAAE,mBAAA,EAAqB,WAAA,EAAa,gBAAA,CAAiB,CAAA,GACzD,MAAA,CAAA,GAAM,6BAAA,mBAAA,EAAoB,UAAU,YAAY,gBAAgB;IAGlE,IAAI,qBAAqB;QACvB,OAAO;YACL,GAAG,SAAS,MAAA,CAAO,CAAA;YACnB,cAAc;YACd,eAAe;YACf,SAAS;QACX;IACF;IAGA,oBACE,UAAU,QAAQ;QAChB,SAAS,YAAa,OAAA;IACxB,CAAC;IAGH,IAAI,cAAc;QAChB,GAAG,UAAA;QACH,UAAU,CAAC;eAAG,WAAW,QAAA;YAAU,iBAAiB,MAAA,CAAO;YAAG,WAAY;SAAA;IAC5E;IACA,cAAc,sBAAsB,aAAa,gBAAgB;IAGjE,OAAO,MAAM,eAAe,UAAU;QACpC,YAAY;QACZ;QACA,aAAa,cAAc;QAC3B,cAAc,eAAe;IAC/B,CAAC;AACH;AAEA,eAAe,wBACb,OAAA,EACA,aAAA,EACA,cAAA,EACA,KAAA,EAC0B;IAC1B,MAAM,YAAY,MAAM,QAAA,CAAS,QAAA,EAAU;IAC3C,IAAA,CACG,QAAQ,KAAA,EAAO,UAAU,CAAA,IAAK,KAC/B,WAAW,YACX,CAAC,WAAW,UAAU,OACtB;QACA,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,WAAA,EAAc,MAAM,QAAA,CAAS,IAAI,CAAA,mCAAA,EAAsC,QAAQ,KAAA,EAAO,MAAM,CAAA,qDAAA,CAAA;IAGhG;IACA,IACE,QAAQ,UAAA,IACR,WAAW,YACX,CAAC,WAAW,UAAU,YACtB;QACA,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,WAAA,EAAc,MAAM,QAAA,CAAS,IAAI,CAAA,qDAAA,EAAwD,QAAQ,UAAU,CAAA,+CAAA,CAAA;IAG/G;IACA,MAAM,MAAoB;QACxB,UAAU,QAAQ,QAAA;QAClB,QAAQ,QAAQ,MAAA;QAChB,MAAM,QAAQ,IAAA;QACd,OAAO,eAAe,IAAI,YAAA,gBAAgB,KAAK,CAAC,CAAA;QAChD,QAAA,CAAA,GAAQ,YAAA,mBAAA,EAAoB;YAC1B,aAAa,QAAQ,MAAA,EAAQ;YAC7B,aAAa,QAAQ,MAAA,EAAQ;YAC7B,QAAQ,QAAQ,MAAA,EAAQ;YACxB,QAAQ,QAAQ,MAAA,EAAQ;QAC1B,CAAC;IACH;IACA,IAAI,QAAQ,UAAA,EAAY;QACtB,IAAI,UAAA,GAAa,QAAQ,UAAA;IAC3B;IACA,IAAI,IAAI,MAAA,IAAU,CAAC,IAAI,MAAA,CAAO,MAAA,CAAQ,CAAA,OAAO,IAAI,MAAA,CAAO,MAAA;IACxD,OAAO;AACT;AAEO,SAAS,mBAAmB,KAAA,EAAqB;IACtD,MAAM,cAAc,aAAA,GAAA,IAAI,IAAU;IAClC,KAAA,MAAW,QAAQ,MAAO;QACxB,MAAM,OAAO,gBAAgB,IAAI;QACjC,YAAY,GAAA,CAAI,IAAI;QACpB,IAAI,YAAY,IAAA,GAAO,GAAG;YACxB,MAAM,IAAI,MAAM,8BAA8B;QAChD;IACF;IACA,OAAO,MAAM,IAAA,CAAK,WAAW,CAAA,CAAE,CAAC,CAAA;AAClC;AAEA,SAAS,gBAAgB,IAAA,EAAkB;IACzC,IAAI,KAAK,WAAA,KAAgB,KAAA,EAAW,CAAA,OAAO;IAC3C,IAAI,KAAK,YAAA,KAAiB,KAAA,EAAW,CAAA,OAAO;IAC5C,IAAI,KAAK,IAAA,KAAS,KAAA,EAAW,CAAA,OAAO;IACpC,IAAI,KAAK,KAAA,KAAU,KAAA,EAAW,CAAA,OAAO;IACrC,IAAI,KAAK,IAAA,KAAS,KAAA,EAAW,CAAA,OAAO;IACpC,MAAM,IAAI,MAAM,iCAAiC;AACnD;AAEA,eAAe,eACb,QAAA,EACA,UAAA,EACgC;IAEhC,IAAI,CAAC,WAAW,QAAA,CAAS,IAAA,CAAK,CAAC,IAAM,CAAC,CAAC,EAAE,OAAA,CAAQ,IAAA,CAAK,CAAC,IAAM,EAAE,QAAQ,CAAC,GAAG;QACzE,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC,CAAA;IACzB,KAAA,MAAW,KAAK,WAAW,QAAA,CAAU;QACnC,IAAI,CAAC,EAAE,OAAA,CAAQ,IAAA,CAAK,CAAC,IAAM,EAAE,QAAQ,GAAG;YACtC,gBAAgB,IAAA,CAAK,CAAC;YACtB;QACF;QACA,MAAM,iBAAiB,CAAC,CAAA;QACxB,KAAA,MAAW,KAAK,EAAE,OAAA,CAAS;YACzB,IAAI,CAAC,EAAE,QAAA,EAAU;gBACf,eAAe,IAAA,CAAK,CAAC;gBACrB;YACF;YACA,MAAM,WAAW,MAAA,CAAA,GAAM,gBAAA,oBAAA,EAAqB,UAAU,EAAE,QAAQ;YAChE,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,YAAA,WAAA,CAAY;oBACpB,QAAQ;oBACR,SAAS,CAAA,qCAAA,EAAwC,EAAE,QAAA,CAAS,GAAG,EAAA;gBACjE,CAAC;YACH;YACA,MAAM,gBAAgB,MAAM,SAAS,EAAE,QAAQ;YAC/C,eAAe,IAAA,CAAK,GAAG,cAAc,OAAO;QAC9C;QAEA,gBAAgB,IAAA,CAAK;YACnB,GAAG,CAAA;YACH,SAAS;QACX,CAAC;IACH;IAEA,OAAO;QACL,GAAG,UAAA;QACH,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4248, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/generate/chunk.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError } from '@genkit-ai/core';\nimport { extractJson } from '../extract.js';\nimport type {\n  GenerateResponseChunkData,\n  Part,\n  Role,\n  ToolRequestPart,\n} from '../model.js';\n\nexport type ChunkParser<T = unknown> = (chunk: GenerateResponseChunk<T>) => T;\n\nexport class GenerateResponseChunk<T = unknown>\n  implements GenerateResponseChunkData\n{\n  /** The index of the message this chunk corresponds to, starting with `0` for the first model response of the generation. */\n  index: number;\n  /** The role of the message this chunk corresponds to. Will always be `model` or `tool`. */\n  role: Role;\n  /** The content generated in this chunk. */\n  content: Part[];\n  /** Custom model-specific data for this chunk. */\n  custom?: unknown;\n  /** Accumulated chunks for partial output extraction. */\n  previousChunks?: GenerateResponseChunkData[];\n  /** The parser to be used to parse `output` from this chunk. */\n  parser?: ChunkParser<T>;\n\n  constructor(\n    data: GenerateResponseChunkData,\n    options: {\n      previousChunks?: GenerateResponseChunkData[];\n      role: Role;\n      index: number;\n      parser?: ChunkParser<T>;\n    }\n  ) {\n    this.content = data.content || [];\n    this.custom = data.custom;\n    this.previousChunks = options.previousChunks\n      ? [...options.previousChunks]\n      : undefined;\n    this.index = options.index;\n    this.role = options.role;\n    this.parser = options.parser;\n  }\n\n  /**\n   * Concatenates all `text` parts present in the chunk with no delimiter.\n   * @returns A string of all concatenated text parts.\n   */\n  get text(): string {\n    return this.content.map((part) => part.text || '').join('');\n  }\n\n  /**\n   * Concatenates all `reasoning` parts present in the chunk with no delimiter.\n   * @returns A string of all concatenated reasoning parts.\n   */\n  get reasoning(): string {\n    return this.content.map((part) => part.reasoning || '').join('');\n  }\n\n  /**\n   * Concatenates all `text` parts of all chunks from the response thus far.\n   * @returns A string of all concatenated chunk text content.\n   */\n  get accumulatedText(): string {\n    return this.previousText + this.text;\n  }\n\n  /**\n   * Concatenates all `text` parts of all preceding chunks.\n   */\n  get previousText(): string {\n    if (!this.previousChunks)\n      throw new GenkitError({\n        status: 'FAILED_PRECONDITION',\n        message: 'Cannot compose accumulated text without previous chunks.',\n      });\n\n    return this.previousChunks\n      ?.map((c) => c.content.map((p) => p.text || '').join(''))\n      .join('');\n  }\n\n  /**\n   * Returns the first media part detected in the chunk. Useful for extracting\n   * (for example) an image from a generation expected to create one.\n   * @returns The first detected `media` part in the chunk.\n   */\n  get media(): { url: string; contentType?: string } | null {\n    return this.content.find((part) => part.media)?.media || null;\n  }\n\n  /**\n   * Returns the first detected `data` part of a chunk.\n   * @returns The first `data` part detected in the chunk (if any).\n   */\n  get data(): T | null {\n    return this.content.find((part) => part.data)?.data as T | null;\n  }\n\n  /**\n   * Returns all tool request found in this chunk.\n   * @returns Array of all tool request found in this chunk.\n   */\n  get toolRequests(): ToolRequestPart[] {\n    return this.content.filter(\n      (part) => !!part.toolRequest\n    ) as ToolRequestPart[];\n  }\n\n  /**\n   * Parses the chunk into the desired output format using the parser associated\n   * with the generate request, or falls back to naive JSON parsing otherwise.\n   */\n  get output(): T | null {\n    if (this.parser) return this.parser(this);\n    return this.data || extractJson(this.accumulatedText);\n  }\n\n  toJSON(): GenerateResponseChunkData {\n    const data = {\n      role: this.role,\n      index: this.index,\n      content: this.content,\n    } as GenerateResponseChunkData;\n    if (this.custom) {\n      data.custom = this.custom;\n    }\n    return data;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,uBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA4B;AAC5B,IAAA,iBAA4B;AAUrB,MAAM,sBAEb;IAAA,0HAAA,GAEE,MAAA;IAAA,yFAAA,GAEA,KAAA;IAAA,yCAAA,GAEA,QAAA;IAAA,+CAAA,GAEA,OAAA;IAAA,sDAAA,GAEA,eAAA;IAAA,6DAAA,GAEA,OAAA;IAEA,YACE,IAAA,EACA,OAAA,CAMA;QACA,IAAA,CAAK,OAAA,GAAU,KAAK,OAAA,IAAW,CAAC,CAAA;QAChC,IAAA,CAAK,MAAA,GAAS,KAAK,MAAA;QACnB,IAAA,CAAK,cAAA,GAAiB,QAAQ,cAAA,GAC1B,CAAC;eAAG,QAAQ,cAAc;SAAA,GAC1B,KAAA;QACJ,IAAA,CAAK,KAAA,GAAQ,QAAQ,KAAA;QACrB,IAAA,CAAK,IAAA,GAAO,QAAQ,IAAA;QACpB,IAAA,CAAK,MAAA,GAAS,QAAQ,MAAA;IACxB;IAAA;;;GAAA,GAMA,IAAI,OAAe;QACjB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,OAAS,KAAK,IAAA,IAAQ,EAAE,EAAE,IAAA,CAAK,EAAE;IAC5D;IAAA;;;GAAA,GAMA,IAAI,YAAoB;QACtB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,OAAS,KAAK,SAAA,IAAa,EAAE,EAAE,IAAA,CAAK,EAAE;IACjE;IAAA;;;GAAA,GAMA,IAAI,kBAA0B;QAC5B,OAAO,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,IAAA;IAClC;IAAA;;GAAA,GAKA,IAAI,eAAuB;QACzB,IAAI,CAAC,IAAA,CAAK,cAAA,EACR,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;QAEH,OAAO,IAAA,CAAK,cAAA,EACR,IAAI,CAAC,IAAM,EAAE,OAAA,CAAQ,GAAA,CAAI,CAAC,IAAM,EAAE,IAAA,IAAQ,EAAE,EAAE,IAAA,CAAK,EAAE,CAAC,EACvD,KAAK,EAAE;IACZ;IAAA;;;;GAAA,GAOA,IAAI,QAAsD;QACxD,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,CAAC,OAAS,KAAK,KAAK,GAAG,SAAS;IAC3D;IAAA;;;GAAA,GAMA,IAAI,OAAiB;QACnB,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,CAAC,OAAS,KAAK,IAAI,GAAG;IACjD;IAAA;;;GAAA,GAMA,IAAI,eAAkC;QACpC,OAAO,IAAA,CAAK,OAAA,CAAQ,MAAA,CAClB,CAAC,OAAS,CAAC,CAAC,KAAK,WAAA;IAErB;IAAA;;;GAAA,GAMA,IAAI,SAAmB;QACrB,IAAI,IAAA,CAAK,MAAA,CAAQ,CAAA,OAAO,IAAA,CAAK,MAAA,CAAO,IAAI;QACxC,OAAO,IAAA,CAAK,IAAA,IAAA,CAAA,GAAQ,eAAA,WAAA,EAAY,IAAA,CAAK,eAAe;IACtD;IAEA,SAAoC;QAClC,MAAM,OAAO;YACX,MAAM,IAAA,CAAK,IAAA;YACX,OAAO,IAAA,CAAK,KAAA;YACZ,SAAS,IAAA,CAAK,OAAA;QAChB;QACA,IAAI,IAAA,CAAK,MAAA,EAAQ;YACf,KAAK,MAAA,GAAS,IAAA,CAAK,MAAA;QACrB;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4369, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/generate/response.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Operation } from '@genkit-ai/core';\nimport { parseSchema } from '@genkit-ai/core/schema';\nimport {\n  GenerationBlockedError,\n  GenerationResponseError,\n} from '../generate.js';\nimport { Message, type MessageParser } from '../message.js';\nimport type {\n  GenerateRequest,\n  GenerateResponseData,\n  GenerationUsage,\n  MessageData,\n  ModelResponseData,\n  ToolRequestPart,\n} from '../model.js';\n\n/**\n * GenerateResponse is the result from a `generate()` call and contains one or\n * more generated candidate messages.\n */\nexport class GenerateResponse<O = unknown> implements ModelResponseData {\n  /** The generated message. */\n  message?: Message<O>;\n  /** The reason generation stopped for this request. */\n  finishReason: ModelResponseData['finishReason'];\n  /** Additional information about why the model stopped generating, if any. */\n  finishMessage?: string;\n  /** Usage information. */\n  usage: GenerationUsage;\n  /** Provider-specific response data. */\n  custom: unknown;\n  /** Provider-specific response data. */\n  raw: unknown;\n  /** The request that generated this response. */\n  request?: GenerateRequest;\n  /** Model generation long running operation. */\n  operation?: Operation<GenerateResponseData>;\n  /** Name of the model used. */\n  model?: string;\n  /** The parser for output parsing of this response. */\n  parser?: MessageParser<O>;\n\n  constructor(\n    response: GenerateResponseData,\n    options?: {\n      request?: GenerateRequest;\n      parser?: MessageParser<O>;\n    }\n  ) {\n    // Check for candidates in addition to message for backwards compatibility.\n    const generatedMessage =\n      response.message || response.candidates?.[0]?.message;\n    if (generatedMessage) {\n      this.message = new Message<O>(generatedMessage, {\n        parser: options?.parser,\n      });\n    }\n    this.finishReason =\n      response.finishReason || response.candidates?.[0]?.finishReason!;\n    this.finishMessage =\n      response.finishMessage || response.candidates?.[0]?.finishMessage;\n    this.usage = response.usage || {};\n    this.custom = response.custom || {};\n    this.raw = response.raw || this.custom;\n    this.request = options?.request;\n    this.operation = response?.operation;\n  }\n\n  /**\n   * Throws an error if the response does not contain valid output.\n   */\n  assertValid(): void {\n    if (this.finishReason === 'blocked') {\n      throw new GenerationBlockedError(\n        this,\n        `Generation blocked${this.finishMessage ? `: ${this.finishMessage}` : '.'}`\n      );\n    }\n\n    if (!this.message && !this.operation) {\n      throw new GenerationResponseError(\n        this,\n        `Model did not generate a message. Finish reason: '${this.finishReason}': ${this.finishMessage}`\n      );\n    }\n  }\n\n  /**\n   * Throws an error if the response does not conform to expected schema.\n   */\n  assertValidSchema(request?: GenerateRequest): void {\n    if (request?.output?.schema || this.request?.output?.schema) {\n      const o = this.output;\n      parseSchema(o, {\n        jsonSchema: request?.output?.schema || this.request?.output?.schema,\n      });\n    }\n  }\n\n  isValid(request?: GenerateRequest): boolean {\n    try {\n      this.assertValid();\n      this.assertValidSchema(request);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  /**\n   * If the generated message contains a `data` part, it is returned. Otherwise,\n   * the `output()` method extracts the first valid JSON object or array from the text\n   * contained in the selected candidate's message and returns it.\n   *\n   * @returns The structured output contained in the selected candidate.\n   */\n  get output(): O | null {\n    return this.message?.output || null;\n  }\n\n  /**\n   * Concatenates all `text` parts present in the generated message with no delimiter.\n   * @returns A string of all concatenated text parts.\n   */\n  get text(): string {\n    return this.message?.text || '';\n  }\n\n  /**\n   * Concatenates all `reasoning` parts present in the generated message with no delimiter.\n   * @returns A string of all concatenated reasoning parts.\n   */\n  get reasoning(): string {\n    return this.message?.reasoning || '';\n  }\n\n  /**\n   * Returns the first detected media part in the generated message. Useful for\n   * extracting (for example) an image from a generation expected to create one.\n   * @returns The first detected `media` part in the candidate.\n   */\n  get media(): { url: string; contentType?: string } | null {\n    return this.message?.media || null;\n  }\n\n  /**\n   * Returns the first detected `data` part of the generated message.\n   * @returns The first `data` part detected in the candidate (if any).\n   */\n  get data(): O | null {\n    return this.message?.data || null;\n  }\n\n  /**\n   * Returns all tool request found in the generated message.\n   * @returns Array of all tool request found in the candidate.\n   */\n  get toolRequests(): ToolRequestPart[] {\n    return this.message?.toolRequests || [];\n  }\n\n  /**\n   * Returns all tool requests annotated as interrupts found in the generated message.\n   * @returns A list of ToolRequestParts.\n   */\n  get interrupts(): ToolRequestPart[] {\n    return this.message?.interrupts || [];\n  }\n\n  /**\n   * Returns the message history for the request by concatenating the model\n   * response to the list of messages from the request. The result of this\n   * method can be safely serialized to JSON for persistence in a database.\n   * @returns A serializable list of messages compatible with `generate({history})`.\n   */\n  get messages(): MessageData[] {\n    if (!this.request)\n      throw new Error(\n        \"Can't construct history for response without request reference.\"\n      );\n    if (!this.message)\n      throw new Error(\n        \"Can't construct history for response without generated message.\"\n      );\n    return [...this.request?.messages, this.message.toJSON()];\n  }\n\n  toJSON(): ModelResponseData {\n    const out = {\n      message: this.message?.toJSON(),\n      finishReason: this.finishReason,\n      finishMessage: this.finishMessage,\n      usage: this.usage,\n      custom: (this.custom as { toJSON?: () => any }).toJSON?.() || this.custom,\n      request: this.request,\n      operation: this.operation,\n    };\n    if (!out.finishMessage) delete out.finishMessage;\n    if (!out.request) delete out.request;\n    if (!out.operation) delete out.operation;\n    return out;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAiBA,IAAA,gBAA4B;AAC5B,IAAA,kBAGO;AACP,IAAA,iBAA4C;AAcrC,MAAM,iBAA2D;IAAA,2BAAA,GAEtE,QAAA;IAAA,oDAAA,GAEA,aAAA;IAAA,2EAAA,GAEA,cAAA;IAAA,uBAAA,GAEA,MAAA;IAAA,qCAAA,GAEA,OAAA;IAAA,qCAAA,GAEA,IAAA;IAAA,8CAAA,GAEA,QAAA;IAAA,6CAAA,GAEA,UAAA;IAAA,4BAAA,GAEA,MAAA;IAAA,oDAAA,GAEA,OAAA;IAEA,YACE,QAAA,EACA,OAAA,CAIA;QAEA,MAAM,mBACJ,SAAS,OAAA,IAAW,SAAS,UAAA,EAAA,CAAa,CAAC,CAAA,EAAG;QAChD,IAAI,kBAAkB;YACpB,IAAA,CAAK,OAAA,GAAU,IAAI,eAAA,OAAA,CAAW,kBAAkB;gBAC9C,QAAQ,SAAS;YACnB,CAAC;QACH;QACA,IAAA,CAAK,YAAA,GACH,SAAS,YAAA,IAAgB,SAAS,UAAA,EAAA,CAAa,CAAC,CAAA,EAAG;QACrD,IAAA,CAAK,aAAA,GACH,SAAS,aAAA,IAAiB,SAAS,UAAA,EAAA,CAAa,CAAC,CAAA,EAAG;QACtD,IAAA,CAAK,KAAA,GAAQ,SAAS,KAAA,IAAS,CAAC;QAChC,IAAA,CAAK,MAAA,GAAS,SAAS,MAAA,IAAU,CAAC;QAClC,IAAA,CAAK,GAAA,GAAM,SAAS,GAAA,IAAO,IAAA,CAAK,MAAA;QAChC,IAAA,CAAK,OAAA,GAAU,SAAS;QACxB,IAAA,CAAK,SAAA,GAAY,UAAU;IAC7B;IAAA;;GAAA,GAKA,cAAoB;QAClB,IAAI,IAAA,CAAK,YAAA,KAAiB,WAAW;YACnC,MAAM,IAAI,gBAAA,sBAAA,CACR,IAAA,EACA,CAAA,kBAAA,EAAqB,IAAA,CAAK,aAAA,GAAgB,CAAA,EAAA,EAAK,IAAA,CAAK,aAAa,EAAA,GAAK,GAAG,EAAA;QAE7E;QAEA,IAAI,CAAC,IAAA,CAAK,OAAA,IAAW,CAAC,IAAA,CAAK,SAAA,EAAW;YACpC,MAAM,IAAI,gBAAA,uBAAA,CACR,IAAA,EACA,CAAA,kDAAA,EAAqD,IAAA,CAAK,YAAY,CAAA,GAAA,EAAM,IAAA,CAAK,aAAa,EAAA;QAElG;IACF;IAAA;;GAAA,GAKA,kBAAkB,OAAA,EAAiC;QACjD,IAAI,SAAS,QAAQ,UAAU,IAAA,CAAK,OAAA,EAAS,QAAQ,QAAQ;YAC3D,MAAM,IAAI,IAAA,CAAK,MAAA;YACf,CAAA,GAAA,cAAA,WAAA,EAAY,GAAG;gBACb,YAAY,SAAS,QAAQ,UAAU,IAAA,CAAK,OAAA,EAAS,QAAQ;YAC/D,CAAC;QACH;IACF;IAEA,QAAQ,OAAA,EAAoC;QAC1C,IAAI;YACF,IAAA,CAAK,WAAA,CAAY;YACjB,IAAA,CAAK,iBAAA,CAAkB,OAAO;YAC9B,OAAO;QACT,EAAA,OAAS,GAAG;YACV,OAAO;QACT;IACF;IAAA;;;;;;GAAA,GASA,IAAI,SAAmB;QACrB,OAAO,IAAA,CAAK,OAAA,EAAS,UAAU;IACjC;IAAA;;;GAAA,GAMA,IAAI,OAAe;QACjB,OAAO,IAAA,CAAK,OAAA,EAAS,QAAQ;IAC/B;IAAA;;;GAAA,GAMA,IAAI,YAAoB;QACtB,OAAO,IAAA,CAAK,OAAA,EAAS,aAAa;IACpC;IAAA;;;;GAAA,GAOA,IAAI,QAAsD;QACxD,OAAO,IAAA,CAAK,OAAA,EAAS,SAAS;IAChC;IAAA;;;GAAA,GAMA,IAAI,OAAiB;QACnB,OAAO,IAAA,CAAK,OAAA,EAAS,QAAQ;IAC/B;IAAA;;;GAAA,GAMA,IAAI,eAAkC;QACpC,OAAO,IAAA,CAAK,OAAA,EAAS,gBAAgB,CAAC,CAAA;IACxC;IAAA;;;GAAA,GAMA,IAAI,aAAgC;QAClC,OAAO,IAAA,CAAK,OAAA,EAAS,cAAc,CAAC,CAAA;IACtC;IAAA;;;;;GAAA,GAQA,IAAI,WAA0B;QAC5B,IAAI,CAAC,IAAA,CAAK,OAAA,EACR,MAAM,IAAI,MACR;QAEJ,IAAI,CAAC,IAAA,CAAK,OAAA,EACR,MAAM,IAAI,MACR;QAEJ,OAAO,CAAC;eAAG,IAAA,CAAK,OAAA,EAAS;YAAU,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,CAAC;SAAA;IAC1D;IAEA,SAA4B;QAC1B,MAAM,MAAM;YACV,SAAS,IAAA,CAAK,OAAA,EAAS,OAAO;YAC9B,cAAc,IAAA,CAAK,YAAA;YACnB,eAAe,IAAA,CAAK,aAAA;YACpB,OAAO,IAAA,CAAK,KAAA;YACZ,QAAS,IAAA,CAAK,MAAA,CAAkC,MAAA,GAAS,KAAK,IAAA,CAAK,MAAA;YACnE,SAAS,IAAA,CAAK,OAAA;YACd,WAAW,IAAA,CAAK,SAAA;QAClB;QACA,IAAI,CAAC,IAAI,aAAA,CAAe,CAAA,OAAO,IAAI,aAAA;QACnC,IAAI,CAAC,IAAI,OAAA,CAAS,CAAA,OAAO,IAAI,OAAA;QAC7B,IAAI,CAAC,IAAI,SAAA,CAAW,CAAA,OAAO,IAAI,SAAA;QAC/B,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4539, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/generate.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  assertUnstable,\n  GenkitError,\n  isAction,\n  isDetachedAction,\n  Operation,\n  runWithContext,\n  runWithStreamingCallback,\n  sentinelNoopStreamingCallback,\n  type Action,\n  type ActionContext,\n  type StreamingCallback,\n  type z,\n} from '@genkit-ai/core';\nimport { Channel } from '@genkit-ai/core/async';\nimport { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport type { DocumentData } from './document.js';\nimport {\n  injectInstructions,\n  resolveFormat,\n  resolveInstructions,\n} from './formats/index.js';\nimport {\n  generateHelper,\n  shouldInjectFormatInstructions,\n} from './generate/action.js';\nimport { GenerateResponseChunk } from './generate/chunk.js';\nimport { GenerateResponse } from './generate/response.js';\nimport { Message } from './message.js';\nimport {\n  GenerateResponseData,\n  resolveModel,\n  type GenerateActionOptions,\n  type GenerateRequest,\n  type GenerationCommonConfigSchema,\n  type MessageData,\n  type ModelArgument,\n  type ModelMiddleware,\n  type Part,\n  type ToolRequestPart,\n  type ToolResponsePart,\n} from './model.js';\nimport { isExecutablePrompt } from './prompt.js';\nimport {\n  isDynamicTool,\n  resolveTools,\n  toToolDefinition,\n  type ToolArgument,\n} from './tool.js';\nexport { GenerateResponse, GenerateResponseChunk };\n\n/** Specifies how tools should be called by the model. */\nexport type ToolChoice = 'auto' | 'required' | 'none';\n\nexport interface OutputOptions<O extends z.ZodTypeAny = z.ZodTypeAny> {\n  format?: string;\n  contentType?: string;\n  instructions?: boolean | string;\n  schema?: O;\n  jsonSchema?: any;\n  constrained?: boolean;\n}\n\n/** ResumeOptions configure how to resume generation after an interrupt. */\nexport interface ResumeOptions {\n  /**\n   * respond should contain a single or list of `toolResponse` parts corresponding\n   * to interrupt `toolRequest` parts from the most recent model message. Each\n   * entry must have a matching `name` and `ref` (if supplied) for its `toolRequest`\n   * counterpart.\n   *\n   * Tools have a `.respond` helper method to construct a reply ToolResponse and validate\n   * the data against its schema. Call `myTool.respond(interruptToolRequest, yourReplyData)`.\n   */\n  respond?: ToolResponsePart | ToolResponsePart[];\n  /**\n   * restart will run a tool again with additionally supplied metadata passed through as\n   * a `resumed` option in the second argument. This allows for scenarios like conditionally\n   * requesting confirmation of an LLM's tool request.\n   *\n   * Tools have a `.restart` helper method to construct a restart ToolRequest. Call\n   * `myTool.restart(interruptToolRequest, resumeMetadata)`.\n   *\n   */\n  restart?: ToolRequestPart | ToolRequestPart[];\n  /** Additional metadata to annotate the created tool message with in the \"resume\" key. */\n  metadata?: Record<string, any>;\n}\n\nexport interface GenerateOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /** A model name (e.g. `vertexai/gemini-1.0-pro`) or reference. */\n  model?: ModelArgument<CustomOptions>;\n  /** The system prompt to be included in the generate request. Can be a string for a simple text prompt or one or more parts for multi-modal prompts (subject to model support). */\n  system?: string | Part | Part[];\n  /** The prompt for which to generate a response. Can be a string for a simple text prompt or one or more parts for multi-modal prompts. */\n  prompt?: string | Part | Part[];\n  /** Retrieved documents to be used as context for this generation. */\n  docs?: DocumentData[];\n  /** Conversation messages (history) for multi-turn prompting when supported by the underlying model. */\n  messages?: (MessageData & { content: Part[] | string | (string | Part)[] })[];\n  /** List of registered tool names or actions to treat as a tool for this generation if supported by the underlying model. */\n  tools?: ToolArgument[];\n  /** Specifies how tools should be called by the model.  */\n  toolChoice?: ToolChoice;\n  /** Configuration for the generation request. */\n  config?: z.infer<CustomOptions>;\n  /** Configuration for the desired output of the request. Defaults to the model's default output if unspecified. */\n  output?: OutputOptions<O>;\n  /**\n   * resume provides convenient capabilities for continuing generation\n   * after an interrupt is triggered. Example:\n   *\n   * ```ts\n   * const myInterrupt = ai.defineInterrupt({...});\n   *\n   * const response = await ai.generate({\n   *   tools: [myInterrupt],\n   *   prompt: \"Call myInterrupt\",\n   * });\n   *\n   * const interrupt = response.interrupts[0];\n   *\n   * const resumedResponse = await ai.generate({\n   *   messages: response.messages,\n   *   resume: myInterrupt.respond(interrupt, {note: \"this is the reply data\"}),\n   * });\n   * ```\n   *\n   * @beta\n   */\n  resume?: ResumeOptions;\n  /** When true, return tool calls for manual processing instead of automatically resolving them. */\n  returnToolRequests?: boolean;\n  /** Maximum number of tool call iterations that can be performed in a single generate call (default 5). */\n  maxTurns?: number;\n  /** When provided, models supporting streaming will call the provided callback with chunks as generation progresses. */\n  onChunk?: StreamingCallback<GenerateResponseChunk>;\n  /**\n   * When provided, models supporting streaming will call the provided callback with chunks as generation progresses.\n   *\n   * @deprecated use {@link onChunk} instead.\n   */\n  streamingCallback?: StreamingCallback<GenerateResponseChunk>;\n  /** Middleware to be used with this model call. */\n  use?: ModelMiddleware[];\n  /** Additional context (data, like e.g. auth) to be passed down to tools, prompts and other sub actions. */\n  context?: ActionContext;\n  /** Abort signal for the generate request. */\n  abortSignal?: AbortSignal;\n}\n\nexport async function toGenerateRequest(\n  registry: Registry,\n  options: GenerateOptions\n): Promise<GenerateRequest> {\n  const messages: MessageData[] = [];\n  if (options.system) {\n    messages.push({\n      role: 'system',\n      content: Message.parseContent(options.system),\n    });\n  }\n  if (options.messages) {\n    messages.push(...options.messages.map((m) => Message.parseData(m)));\n  }\n  if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: Message.parseContent(options.prompt),\n    });\n  }\n  if (messages.length === 0) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'at least one message is required in generate request',\n    });\n  }\n  if (\n    options.resume &&\n    !(\n      messages.at(-1)?.role === 'model' &&\n      messages.at(-1)?.content.find((p) => !!p.toolRequest)\n    )\n  ) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message: `Last message must be a 'model' role with at least one tool request to 'resume' generation.`,\n      detail: messages.at(-1),\n    });\n  }\n  let tools: Action<any, any>[] | undefined;\n  if (options.tools) {\n    tools = await resolveTools(registry, options.tools);\n  }\n\n  const resolvedSchema = toJsonSchema({\n    schema: options.output?.schema,\n    jsonSchema: options.output?.jsonSchema,\n  });\n\n  const resolvedFormat = await resolveFormat(registry, options.output);\n  const instructions = resolveInstructions(\n    resolvedFormat,\n    resolvedSchema,\n    options?.output?.instructions\n  );\n\n  const out = {\n    messages: shouldInjectFormatInstructions(\n      resolvedFormat?.config,\n      options.output\n    )\n      ? injectInstructions(messages, instructions)\n      : messages,\n    config: options.config,\n    docs: options.docs,\n    tools: tools?.map(toToolDefinition) || [],\n    output: {\n      ...(resolvedFormat?.config || {}),\n      ...options.output,\n      schema: resolvedSchema,\n    },\n  } as GenerateRequest;\n  if (!out?.output?.schema) delete out?.output?.schema;\n  return out;\n}\n\nexport class GenerationResponseError extends GenkitError {\n  detail: {\n    response: GenerateResponse;\n    [otherDetails: string]: any;\n  };\n\n  constructor(\n    response: GenerateResponse<any>,\n    message: string,\n    status?: GenkitError['status'],\n    detail?: Record<string, any>\n  ) {\n    super({\n      status: status || 'FAILED_PRECONDITION',\n      message,\n    });\n    this.detail = { response, ...detail };\n  }\n}\n\nasync function toolsToActionRefs(\n  registry: Registry,\n  toolOpt?: ToolArgument[]\n): Promise<string[] | undefined> {\n  if (!toolOpt) return;\n\n  const tools: string[] = [];\n\n  for (const t of toolOpt) {\n    if (typeof t === 'string') {\n      tools.push(await resolveFullToolName(registry, t));\n    } else if (isAction(t) || isDynamicTool(t)) {\n      tools.push(`/${t.__action.metadata?.type}/${t.__action.name}`);\n    } else if (isExecutablePrompt(t)) {\n      const promptToolAction = await t.asTool();\n      tools.push(`/prompt/${promptToolAction.__action.name}`);\n    } else {\n      throw new Error(`Unable to determine type of tool: ${JSON.stringify(t)}`);\n    }\n  }\n  return tools;\n}\n\nfunction messagesFromOptions(options: GenerateOptions): MessageData[] {\n  const messages: MessageData[] = [];\n  if (options.system) {\n    messages.push({\n      role: 'system',\n      content: Message.parseContent(options.system),\n    });\n  }\n  if (options.messages) {\n    messages.push(...options.messages);\n  }\n  if (options.prompt) {\n    messages.push({\n      role: 'user',\n      content: Message.parseContent(options.prompt),\n    });\n  }\n  if (messages.length === 0) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'at least one message is required in generate request',\n    });\n  }\n  return messages;\n}\n\n/** A GenerationBlockedError is thrown when a generation is blocked. */\nexport class GenerationBlockedError extends GenerationResponseError {}\n\n/**\n * Generate calls a generative model based on the provided prompt and configuration. If\n * `history` is provided, the generation will include a conversation history in its\n * request. If `tools` are provided, the generate method will automatically resolve\n * tool calls returned from the model unless `returnToolRequests` is set to `true`.\n *\n * See `GenerateOptions` for detailed information about available options.\n *\n * @param options The options for this generation request.\n * @returns The generated response based on the provided parameters.\n */\nexport async function generate<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options:\n    | GenerateOptions<O, CustomOptions>\n    | PromiseLike<GenerateOptions<O, CustomOptions>>\n): Promise<GenerateResponse<z.infer<O>>> {\n  const resolvedOptions: GenerateOptions<O, CustomOptions> = {\n    ...(await Promise.resolve(options)),\n  };\n  const resolvedFormat = await resolveFormat(registry, resolvedOptions.output);\n\n  registry = maybeRegisterDynamicTools(registry, resolvedOptions);\n\n  const params = await toGenerateActionOptions(registry, resolvedOptions);\n\n  const tools = await toolsToActionRefs(registry, resolvedOptions.tools);\n  return await runWithStreamingCallback(\n    registry,\n    stripNoop(resolvedOptions.onChunk ?? resolvedOptions.streamingCallback),\n    async () => {\n      const response = await runWithContext(\n        registry,\n        resolvedOptions.context,\n        () =>\n          generateHelper(registry, {\n            rawRequest: params,\n            middleware: resolvedOptions.use,\n            abortSignal: resolvedOptions.abortSignal,\n          })\n      );\n      const request = await toGenerateRequest(registry, {\n        ...resolvedOptions,\n        tools,\n      });\n      return new GenerateResponse<O>(response, {\n        request: response.request ?? request,\n        parser: resolvedFormat?.handler(request.output?.schema).parseMessage,\n      });\n    }\n  );\n}\n\nexport async function generateOperation<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options:\n    | GenerateOptions<O, CustomOptions>\n    | PromiseLike<GenerateOptions<O, CustomOptions>>\n): Promise<Operation<GenerateResponseData>> {\n  assertUnstable(registry, 'beta', 'generateOperation is a beta feature.');\n\n  options = await options;\n  const resolvedModel = await resolveModel(registry, options.model);\n  if (\n    !resolvedModel.modelAction.__action.metadata?.model.supports?.longRunning\n  ) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `Model '${resolvedModel.modelAction.__action.name}' does not support long running operations.`,\n    });\n  }\n\n  const { operation } = await generate(registry, options);\n  if (!operation) {\n    throw new GenkitError({\n      status: 'FAILED_PRECONDITION',\n      message: `Model '${resolvedModel.modelAction.__action.name}' did not return an operation.`,\n    });\n  }\n  return operation;\n}\n\nfunction maybeRegisterDynamicTools<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(registry: Registry, options: GenerateOptions<O, CustomOptions>): Registry {\n  let hasDynamicTools = false;\n  options?.tools?.forEach((t) => {\n    if (isDynamicTool(t)) {\n      if (isDetachedAction(t)) {\n        t = t.attach(registry);\n      }\n      if (!hasDynamicTools) {\n        hasDynamicTools = true;\n        // Create a temporary registry with dynamic tools for the duration of this\n        // generate request.\n        registry = Registry.withParent(registry);\n      }\n      registry.registerAction('tool', t as Action);\n    }\n  });\n  return registry;\n}\n\nexport async function toGenerateActionOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options: GenerateOptions<O, CustomOptions>\n): Promise<GenerateActionOptions> {\n  const resolvedModel = await resolveModel(registry, options.model);\n  const tools = await toolsToActionRefs(registry, options.tools);\n  const messages: MessageData[] = messagesFromOptions(options);\n\n  const resolvedSchema = toJsonSchema({\n    schema: options.output?.schema,\n    jsonSchema: options.output?.jsonSchema,\n  });\n\n  // If is schema is set but format is not explicitly set, default to `json` format.\n  if (\n    (options.output?.schema || options.output?.jsonSchema) &&\n    !options.output?.format\n  ) {\n    options.output.format = 'json';\n  }\n\n  const params: GenerateActionOptions = {\n    model: resolvedModel.modelAction.__action.name,\n    docs: options.docs,\n    messages: messages,\n    tools,\n    toolChoice: options.toolChoice,\n    config: {\n      version: resolvedModel.version,\n      ...stripUndefinedOptions(resolvedModel.config),\n      ...stripUndefinedOptions(options.config),\n    },\n    output: options.output && {\n      ...options.output,\n      format: options.output.format,\n      jsonSchema: resolvedSchema,\n    },\n    // coerce reply and restart into arrays for the action schema\n    resume: options.resume && {\n      respond: [options.resume.respond || []].flat(),\n      restart: [options.resume.restart || []].flat(),\n      metadata: options.resume.metadata,\n    },\n    returnToolRequests: options.returnToolRequests,\n    maxTurns: options.maxTurns,\n  };\n  // if config is empty and it was not explicitly passed in, we delete it, don't want {}\n  if (Object.keys(params.config).length === 0 && !options.config) {\n    delete params.config;\n  }\n  return params;\n}\n\n/**\n * Check if the callback is a noop callback and return undefined -- downstream models\n * expect undefined if no streaming is requested.\n */\nfunction stripNoop<T>(\n  callback: StreamingCallback<T> | undefined\n): StreamingCallback<T> | undefined {\n  if (callback === sentinelNoopStreamingCallback) {\n    return undefined;\n  }\n  return callback;\n}\n\nfunction stripUndefinedOptions(input?: any): any {\n  if (!input) return input;\n  const copy = { ...input };\n  Object.keys(input).forEach((key) => {\n    if (copy[key] === undefined) {\n      delete copy[key];\n    }\n  });\n  return copy;\n}\n\nasync function resolveFullToolName(\n  registry: Registry,\n  name: string\n): Promise<string> {\n  if (await registry.lookupAction(`/tool/${name}`)) {\n    return `/tool/${name}`;\n  } else if (await registry.lookupAction(`/prompt/${name}`)) {\n    return `/prompt/${name}`;\n  } else {\n    throw new Error(`Unable to determine type of of tool: ${name}`);\n  }\n}\n\nexport type GenerateStreamOptions<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n> = Omit<GenerateOptions<O, CustomOptions>, 'streamingCallback'>;\n\nexport interface GenerateStreamResponse<O extends z.ZodTypeAny = z.ZodTypeAny> {\n  get stream(): AsyncIterable<GenerateResponseChunk>;\n  get response(): Promise<GenerateResponse<O>>;\n}\n\nexport function generateStream<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  CustomOptions extends z.ZodTypeAny = typeof GenerationCommonConfigSchema,\n>(\n  registry: Registry,\n  options:\n    | GenerateOptions<O, CustomOptions>\n    | PromiseLike<GenerateOptions<O, CustomOptions>>\n): GenerateStreamResponse<O> {\n  const channel = new Channel<GenerateResponseChunk>();\n\n  const generated = Promise.resolve(options).then((resolvedOptions) =>\n    generate<O, CustomOptions>(registry, {\n      ...resolvedOptions,\n      onChunk: (chunk) => channel.send(chunk),\n    })\n  );\n  generated.then(\n    () => channel.close(),\n    (err) => channel.error(err)\n  );\n\n  return {\n    response: generated,\n    stream: channel,\n  };\n}\n\nexport function tagAsPreamble(msgs?: MessageData[]): MessageData[] | undefined {\n  if (!msgs) {\n    return undefined;\n  }\n  return msgs.map((m) => ({\n    ...m,\n    metadata: {\n      ...m.metadata,\n      preamble: true,\n    },\n  }));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,kBAAA,IAAA,gBAAA,gBAAA;IAAA,uBAAA,IAAA,aAAA,qBAAA;IAAA,wBAAA,IAAA;IAAA,yBAAA,IAAA;IAAA,UAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,yBAAA,IAAA;IAAA,mBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAaO;AACP,IAAA,eAAwB;AACxB,IAAA,kBAAyB;AACzB,IAAA,gBAA6B;AAE7B,IAAA,iBAIO;AACP,IAAA,gBAGO;AACP,IAAA,eAAsC;AACtC,IAAA,kBAAiC;AACjC,IAAA,iBAAwB;AACxB,IAAA,eAYO;AACP,IAAA,gBAAmC;AACnC,IAAA,cAKO;AA0GP,eAAsB,kBACpB,QAAA,EACA,OAAA,EAC0B;IAC1B,MAAM,WAA0B,CAAC,CAAA;IACjC,IAAI,QAAQ,MAAA,EAAQ;QAClB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAA,OAAA,CAAQ,YAAA,CAAa,QAAQ,MAAM;QAC9C,CAAC;IACH;IACA,IAAI,QAAQ,QAAA,EAAU;QACpB,SAAS,IAAA,CAAK,GAAG,QAAQ,QAAA,CAAS,GAAA,CAAI,CAAC,IAAM,eAAA,OAAA,CAAQ,SAAA,CAAU,CAAC,CAAC,CAAC;IACpE;IACA,IAAI,QAAQ,MAAA,EAAQ;QAClB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAA,OAAA,CAAQ,YAAA,CAAa,QAAQ,MAAM;QAC9C,CAAC;IACH;IACA,IAAI,SAAS,MAAA,KAAW,GAAG;QACzB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;IACH;IACA,IACE,QAAQ,MAAA,IACR,CAAA,CACE,SAAS,EAAA,CAAG,CAAA,CAAE,GAAG,SAAS,WAC1B,SAAS,EAAA,CAAG,CAAA,CAAE,GAAG,QAAQ,KAAK,CAAC,IAAM,CAAC,CAAC,EAAE,WAAW,CAAA,GAEtD;QACA,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,0FAAA,CAAA;YACT,QAAQ,SAAS,EAAA,CAAG,CAAA,CAAE;QACxB,CAAC;IACH;IACA,IAAI;IACJ,IAAI,QAAQ,KAAA,EAAO;QACjB,QAAQ,MAAA,CAAA,GAAM,YAAA,YAAA,EAAa,UAAU,QAAQ,KAAK;IACpD;IAEA,MAAM,iBAAA,CAAA,GAAiB,cAAA,YAAA,EAAa;QAClC,QAAQ,QAAQ,MAAA,EAAQ;QACxB,YAAY,QAAQ,MAAA,EAAQ;IAC9B,CAAC;IAED,MAAM,iBAAiB,MAAA,CAAA,GAAM,eAAA,aAAA,EAAc,UAAU,QAAQ,MAAM;IACnE,MAAM,eAAA,CAAA,GAAe,eAAA,mBAAA,EACnB,gBACA,gBACA,SAAS,QAAQ;IAGnB,MAAM,MAAM;QACV,UAAA,CAAA,GAAU,cAAA,8BAAA,EACR,gBAAgB,QAChB,QAAQ,MAAA,IACV,CAAA,GACI,eAAA,kBAAA,EAAmB,UAAU,YAAY,IACzC;QACJ,QAAQ,QAAQ,MAAA;QAChB,MAAM,QAAQ,IAAA;QACd,OAAO,OAAO,IAAI,YAAA,gBAAgB,KAAK,CAAC,CAAA;QACxC,QAAQ;YACN,GAAI,gBAAgB,UAAU,CAAC,CAAA;YAC/B,GAAG,QAAQ,MAAA;YACX,QAAQ;QACV;IACF;IACA,IAAI,CAAC,KAAK,QAAQ,OAAQ,CAAA,OAAO,KAAK,QAAQ;IAC9C,OAAO;AACT;AAEO,MAAM,gCAAgC,YAAA,WAAA,CAAY;IACvD,OAAA;IAKA,YACE,QAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,CACA;QACA,KAAA,CAAM;YACJ,QAAQ,UAAU;YAClB;QACF,CAAC;QACD,IAAA,CAAK,MAAA,GAAS;YAAE;YAAU,GAAG,MAAA;QAAO;IACtC;AACF;AAEA,eAAe,kBACb,QAAA,EACA,OAAA,EAC+B;IAC/B,IAAI,CAAC,QAAS,CAAA;IAEd,MAAM,QAAkB,CAAC,CAAA;IAEzB,KAAA,MAAW,KAAK,QAAS;QACvB,IAAI,OAAO,MAAM,UAAU;YACzB,MAAM,IAAA,CAAK,MAAM,oBAAoB,UAAU,CAAC,CAAC;QACnD,OAAA,IAAA,CAAA,GAAW,YAAA,QAAA,EAAS,CAAC,KAAA,CAAA,GAAK,YAAA,aAAA,EAAc,CAAC,GAAG;YAC1C,MAAM,IAAA,CAAK,CAAA,CAAA,EAAI,EAAE,QAAA,CAAS,QAAA,EAAU,IAAI,CAAA,CAAA,EAAI,EAAE,QAAA,CAAS,IAAI,EAAE;QAC/D,OAAA,IAAA,CAAA,GAAW,cAAA,kBAAA,EAAmB,CAAC,GAAG;YAChC,MAAM,mBAAmB,MAAM,EAAE,MAAA,CAAO;YACxC,MAAM,IAAA,CAAK,CAAA,QAAA,EAAW,iBAAiB,QAAA,CAAS,IAAI,EAAE;QACxD,OAAO;YACL,MAAM,IAAI,MAAM,CAAA,kCAAA,EAAqC,KAAK,SAAA,CAAU,CAAC,CAAC,EAAE;QAC1E;IACF;IACA,OAAO;AACT;AAEA,SAAS,oBAAoB,OAAA,EAAyC;IACpE,MAAM,WAA0B,CAAC,CAAA;IACjC,IAAI,QAAQ,MAAA,EAAQ;QAClB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAA,OAAA,CAAQ,YAAA,CAAa,QAAQ,MAAM;QAC9C,CAAC;IACH;IACA,IAAI,QAAQ,QAAA,EAAU;QACpB,SAAS,IAAA,CAAK,GAAG,QAAQ,QAAQ;IACnC;IACA,IAAI,QAAQ,MAAA,EAAQ;QAClB,SAAS,IAAA,CAAK;YACZ,MAAM;YACN,SAAS,eAAA,OAAA,CAAQ,YAAA,CAAa,QAAQ,MAAM;QAC9C,CAAC;IACH;IACA,IAAI,SAAS,MAAA,KAAW,GAAG;QACzB,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;IACH;IACA,OAAO;AACT;AAGO,MAAM,+BAA+B,wBAAwB;AAAC;AAarE,eAAsB,SAIpB,QAAA,EACA,OAAA,EAGuC;IACvC,MAAM,kBAAqD;QACzD,GAAI,MAAM,QAAQ,OAAA,CAAQ,OAAO,CAAA;IACnC;IACA,MAAM,iBAAiB,MAAA,CAAA,GAAM,eAAA,aAAA,EAAc,UAAU,gBAAgB,MAAM;IAE3E,WAAW,0BAA0B,UAAU,eAAe;IAE9D,MAAM,SAAS,MAAM,wBAAwB,UAAU,eAAe;IAEtE,MAAM,QAAQ,MAAM,kBAAkB,UAAU,gBAAgB,KAAK;IACrE,OAAO,MAAA,CAAA,GAAM,YAAA,wBAAA,EACX,UACA,UAAU,gBAAgB,OAAA,IAAW,gBAAgB,iBAAiB,GACtE,YAAY;QACV,MAAM,WAAW,MAAA,CAAA,GAAM,YAAA,cAAA,EACrB,UACA,gBAAgB,OAAA,EAChB,IAAA,CAAA,GACE,cAAA,cAAA,EAAe,UAAU;gBACvB,YAAY;gBACZ,YAAY,gBAAgB,GAAA;gBAC5B,aAAa,gBAAgB,WAAA;YAC/B,CAAC;QAEL,MAAM,UAAU,MAAM,kBAAkB,UAAU;YAChD,GAAG,eAAA;YACH;QACF,CAAC;QACD,OAAO,IAAI,gBAAA,gBAAA,CAAoB,UAAU;YACvC,SAAS,SAAS,OAAA,IAAW;YAC7B,QAAQ,gBAAgB,QAAQ,QAAQ,MAAA,EAAQ,MAAM,EAAE;QAC1D,CAAC;IACH;AAEJ;AAEA,eAAsB,kBAIpB,QAAA,EACA,OAAA,EAG0C;IAC1C,CAAA,GAAA,YAAA,cAAA,EAAe,UAAU,QAAQ,sCAAsC;IAEvE,UAAU,MAAM;IAChB,MAAM,gBAAgB,MAAA,CAAA,GAAM,aAAA,YAAA,EAAa,UAAU,QAAQ,KAAK;IAChE,IACE,CAAC,cAAc,WAAA,CAAY,QAAA,CAAS,QAAA,EAAU,MAAM,UAAU,aAC9D;QACA,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,OAAA,EAAU,cAAc,WAAA,CAAY,QAAA,CAAS,IAAI,CAAA,2CAAA,CAAA;QAC5D,CAAC;IACH;IAEA,MAAM,EAAE,SAAA,CAAU,CAAA,GAAI,MAAM,SAAS,UAAU,OAAO;IACtD,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,YAAA,WAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,OAAA,EAAU,cAAc,WAAA,CAAY,QAAA,CAAS,IAAI,CAAA,8BAAA,CAAA;QAC5D,CAAC;IACH;IACA,OAAO;AACT;AAEA,SAAS,0BAGP,QAAA,EAAoB,OAAA,EAAsD;IAC1E,IAAI,kBAAkB;IACtB,SAAS,OAAO,QAAQ,CAAC,MAAM;QAC7B,IAAA,CAAA,GAAI,YAAA,aAAA,EAAc,CAAC,GAAG;YACpB,IAAA,CAAA,GAAI,YAAA,gBAAA,EAAiB,CAAC,GAAG;gBACvB,IAAI,EAAE,MAAA,CAAO,QAAQ;YACvB;YACA,IAAI,CAAC,iBAAiB;gBACpB,kBAAkB;gBAGlB,WAAW,gBAAA,QAAA,CAAS,UAAA,CAAW,QAAQ;YACzC;YACA,SAAS,cAAA,CAAe,QAAQ,CAAW;QAC7C;IACF,CAAC;IACD,OAAO;AACT;AAEA,eAAsB,wBAIpB,QAAA,EACA,OAAA,EACgC;IAChC,MAAM,gBAAgB,MAAA,CAAA,GAAM,aAAA,YAAA,EAAa,UAAU,QAAQ,KAAK;IAChE,MAAM,QAAQ,MAAM,kBAAkB,UAAU,QAAQ,KAAK;IAC7D,MAAM,WAA0B,oBAAoB,OAAO;IAE3D,MAAM,iBAAA,CAAA,GAAiB,cAAA,YAAA,EAAa;QAClC,QAAQ,QAAQ,MAAA,EAAQ;QACxB,YAAY,QAAQ,MAAA,EAAQ;IAC9B,CAAC;IAGD,IAAA,CACG,QAAQ,MAAA,EAAQ,UAAU,QAAQ,MAAA,EAAQ,UAAA,KAC3C,CAAC,QAAQ,MAAA,EAAQ,QACjB;QACA,QAAQ,MAAA,CAAO,MAAA,GAAS;IAC1B;IAEA,MAAM,SAAgC;QACpC,OAAO,cAAc,WAAA,CAAY,QAAA,CAAS,IAAA;QAC1C,MAAM,QAAQ,IAAA;QACd;QACA;QACA,YAAY,QAAQ,UAAA;QACpB,QAAQ;YACN,SAAS,cAAc,OAAA;YACvB,GAAG,sBAAsB,cAAc,MAAM,CAAA;YAC7C,GAAG,sBAAsB,QAAQ,MAAM,CAAA;QACzC;QACA,QAAQ,QAAQ,MAAA,IAAU;YACxB,GAAG,QAAQ,MAAA;YACX,QAAQ,QAAQ,MAAA,CAAO,MAAA;YACvB,YAAY;QACd;QAAA,6DAAA;QAEA,QAAQ,QAAQ,MAAA,IAAU;YACxB,SAAS;gBAAC,QAAQ,MAAA,CAAO,OAAA,IAAW,CAAC,CAAC;aAAA,CAAE,IAAA,CAAK;YAC7C,SAAS;gBAAC,QAAQ,MAAA,CAAO,OAAA,IAAW,CAAC,CAAC;aAAA,CAAE,IAAA,CAAK;YAC7C,UAAU,QAAQ,MAAA,CAAO,QAAA;QAC3B;QACA,oBAAoB,QAAQ,kBAAA;QAC5B,UAAU,QAAQ,QAAA;IACpB;IAEA,IAAI,OAAO,IAAA,CAAK,OAAO,MAAM,EAAE,MAAA,KAAW,KAAK,CAAC,QAAQ,MAAA,EAAQ;QAC9D,OAAO,OAAO,MAAA;IAChB;IACA,OAAO;AACT;AAMA,SAAS,UACP,QAAA,EACkC;IAClC,IAAI,aAAa,YAAA,6BAAA,EAA+B;QAC9C,OAAO,KAAA;IACT;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,KAAA,EAAkB;IAC/C,IAAI,CAAC,MAAO,CAAA,OAAO;IACnB,MAAM,OAAO;QAAE,GAAG,KAAA;IAAM;IACxB,OAAO,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAC,QAAQ;QAClC,IAAI,IAAA,CAAK,GAAG,CAAA,KAAM,KAAA,GAAW;YAC3B,OAAO,IAAA,CAAK,GAAG,CAAA;QACjB;IACF,CAAC;IACD,OAAO;AACT;AAEA,eAAe,oBACb,QAAA,EACA,IAAA,EACiB;IACjB,IAAI,MAAM,SAAS,YAAA,CAAa,CAAA,MAAA,EAAS,IAAI,EAAE,GAAG;QAChD,OAAO,CAAA,MAAA,EAAS,IAAI,EAAA;IACtB,OAAA,IAAW,MAAM,SAAS,YAAA,CAAa,CAAA,QAAA,EAAW,IAAI,EAAE,GAAG;QACzD,OAAO,CAAA,QAAA,EAAW,IAAI,EAAA;IACxB,OAAO;QACL,MAAM,IAAI,MAAM,CAAA,qCAAA,EAAwC,IAAI,EAAE;IAChE;AACF;AAYO,SAAS,eAId,QAAA,EACA,OAAA,EAG2B;IAC3B,MAAM,UAAU,IAAI,aAAA,OAAA,CAA+B;IAEnD,MAAM,YAAY,QAAQ,OAAA,CAAQ,OAAO,EAAE,IAAA,CAAK,CAAC,kBAC/C,SAA2B,UAAU;YACnC,GAAG,eAAA;YACH,SAAS,CAAC,QAAU,QAAQ,IAAA,CAAK,KAAK;QACxC,CAAC;IAEH,UAAU,IAAA,CACR,IAAM,QAAQ,KAAA,CAAM,GACpB,CAAC,MAAQ,QAAQ,KAAA,CAAM,GAAG;IAG5B,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF;AAEO,SAAS,cAAc,IAAA,EAAiD;IAC7E,IAAI,CAAC,MAAM;QACT,OAAO,KAAA;IACT;IACA,OAAO,KAAK,GAAA,CAAI,CAAC,IAAA,CAAO;YACtB,GAAG,CAAA;YACH,UAAU;gBACR,GAAG,EAAE,QAAA;gBACL,UAAU;YACZ;QACF,CAAA,CAAE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4872, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/retriever.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenkitError, defineAction, z, type Action } from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { Document, DocumentDataSchema, type DocumentData } from './document.js';\nimport type { EmbedderInfo } from './embedder.js';\n\nexport {\n  Document,\n  DocumentDataSchema,\n  type DocumentData,\n  type MediaPart,\n  type Part,\n  type TextPart,\n} from './document.js';\n\n/**\n * Retriever implementation function signature.\n */\nexport type RetrieverFn<RetrieverOptions extends z.ZodTypeAny> = (\n  query: Document,\n  queryOpts: z.infer<RetrieverOptions>\n) => Promise<RetrieverResponse>;\n\n/**\n * Indexer implementation function signature.\n */\nexport type IndexerFn<IndexerOptions extends z.ZodTypeAny> = (\n  docs: Array<Document>,\n  indexerOpts: z.infer<IndexerOptions>\n) => Promise<void>;\n\nconst RetrieverRequestSchema = z.object({\n  query: DocumentDataSchema,\n  options: z.any().optional(),\n});\n\nconst RetrieverResponseSchema = z.object({\n  documents: z.array(DocumentDataSchema),\n  // TODO: stats, etc.\n});\ntype RetrieverResponse = z.infer<typeof RetrieverResponseSchema>;\n\nconst IndexerRequestSchema = z.object({\n  documents: z.array(DocumentDataSchema),\n  options: z.any().optional(),\n});\n\n/**\n * Zod schema of retriever info metadata.\n */\nexport const RetrieverInfoSchema = z.object({\n  label: z.string().optional(),\n  /** Supported model capabilities. */\n  supports: z\n    .object({\n      /** Model can process media as part of the prompt (multimodal input). */\n      media: z.boolean().optional(),\n    })\n    .optional(),\n});\nexport type RetrieverInfo = z.infer<typeof RetrieverInfoSchema>;\n\n/**\n * A retriever action type.\n */\nexport type RetrieverAction<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<typeof RetrieverRequestSchema, typeof RetrieverResponseSchema> & {\n    __configSchema?: CustomOptions;\n  };\n\n/**\n * An indexer action type.\n */\nexport type IndexerAction<IndexerOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<typeof IndexerRequestSchema, z.ZodVoid> & {\n    __configSchema?: IndexerOptions;\n  };\n\nfunction retrieverWithMetadata<\n  RetrieverOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  retriever: Action<\n    typeof RetrieverRequestSchema,\n    typeof RetrieverResponseSchema\n  >,\n  configSchema?: RetrieverOptions\n): RetrieverAction<RetrieverOptions> {\n  const withMeta = retriever as RetrieverAction<RetrieverOptions>;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\nfunction indexerWithMetadata<\n  IndexerOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  indexer: Action<typeof IndexerRequestSchema, z.ZodVoid>,\n  configSchema?: IndexerOptions\n): IndexerAction<IndexerOptions> {\n  const withMeta = indexer as IndexerAction<IndexerOptions>;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\n/**\n *  Creates a retriever action for the provided {@link RetrieverFn} implementation.\n */\nexport function defineRetriever<\n  OptionsType extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    name: string;\n    configSchema?: OptionsType;\n    info?: RetrieverInfo;\n  },\n  runner: RetrieverFn<OptionsType>\n) {\n  const retriever = defineAction(\n    registry,\n    {\n      actionType: 'retriever',\n      name: options.name,\n      inputSchema: options.configSchema\n        ? RetrieverRequestSchema.extend({\n            options: options.configSchema.optional(),\n          })\n        : RetrieverRequestSchema,\n      outputSchema: RetrieverResponseSchema,\n      metadata: {\n        type: 'retriever',\n        info: options.info,\n        retriever: {\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n        },\n      },\n    },\n    (i) => runner(new Document(i.query), i.options)\n  );\n  const rwm = retrieverWithMetadata(\n    retriever as Action<\n      typeof RetrieverRequestSchema,\n      typeof RetrieverResponseSchema\n    >,\n    options.configSchema\n  );\n  return rwm;\n}\n\n/**\n *  Creates an indexer action for the provided {@link IndexerFn} implementation.\n */\nexport function defineIndexer<IndexerOptions extends z.ZodTypeAny>(\n  registry: Registry,\n  options: {\n    name: string;\n    embedderInfo?: EmbedderInfo;\n    configSchema?: IndexerOptions;\n  },\n  runner: IndexerFn<IndexerOptions>\n) {\n  const indexer = defineAction(\n    registry,\n    {\n      actionType: 'indexer',\n      name: options.name,\n      inputSchema: options.configSchema\n        ? IndexerRequestSchema.extend({\n            options: options.configSchema.optional(),\n          })\n        : IndexerRequestSchema,\n      outputSchema: z.void(),\n      metadata: {\n        type: 'indexer',\n        embedderInfo: options.embedderInfo,\n        indexer: {\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n        },\n      },\n    },\n    (i) =>\n      runner(\n        i.documents.map((dd) => new Document(dd)),\n        i.options\n      )\n  );\n  const iwm = indexerWithMetadata(\n    indexer as Action<typeof IndexerRequestSchema, z.ZodVoid>,\n    options.configSchema\n  );\n  return iwm;\n}\n\nexport interface RetrieverParams<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  retriever: RetrieverArgument<CustomOptions>;\n  query: string | DocumentData;\n  options?: z.infer<CustomOptions>;\n}\n\n/**\n * A type that can be used to pass a retriever as an argument, either using a reference or an action.\n */\nexport type RetrieverArgument<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = RetrieverAction<CustomOptions> | RetrieverReference<CustomOptions> | string;\n\n/**\n * Retrieves documents from a {@link RetrieverArgument} based on the provided query.\n */\nexport async function retrieve<CustomOptions extends z.ZodTypeAny>(\n  registry: Registry,\n  params: RetrieverParams<CustomOptions>\n): Promise<Array<Document>> {\n  let retriever: RetrieverAction<CustomOptions>;\n  if (typeof params.retriever === 'string') {\n    retriever = await registry.lookupAction(`/retriever/${params.retriever}`);\n  } else if (Object.hasOwnProperty.call(params.retriever, 'info')) {\n    retriever = await registry.lookupAction(\n      `/retriever/${params.retriever.name}`\n    );\n  } else {\n    retriever = params.retriever as RetrieverAction<CustomOptions>;\n  }\n  if (!retriever) {\n    throw new Error('Unable to resolve the retriever');\n  }\n  const response = await retriever({\n    query:\n      typeof params.query === 'string'\n        ? Document.fromText(params.query)\n        : params.query,\n    options: params.options,\n  });\n\n  return response.documents.map((d) => new Document(d));\n}\n\n/**\n * A type that can be used to pass an indexer as an argument, either using a reference or an action.\n */\nexport type IndexerArgument<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  IndexerReference<CustomOptions> | IndexerAction<CustomOptions> | string;\n\n/**\n * Options passed to the index function.\n */\nexport interface IndexerParams<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  indexer: IndexerArgument<CustomOptions>;\n  documents: Array<DocumentData>;\n  options?: z.infer<CustomOptions>;\n}\n\n/**\n * Indexes documents using a {@link IndexerArgument}.\n */\nexport async function index<CustomOptions extends z.ZodTypeAny>(\n  registry: Registry,\n  params: IndexerParams<CustomOptions>\n): Promise<void> {\n  let indexer: IndexerAction<CustomOptions>;\n  if (typeof params.indexer === 'string') {\n    indexer = await registry.lookupAction(`/indexer/${params.indexer}`);\n  } else if (Object.hasOwnProperty.call(params.indexer, 'info')) {\n    indexer = await registry.lookupAction(`/indexer/${params.indexer.name}`);\n  } else {\n    indexer = params.indexer as IndexerAction<CustomOptions>;\n  }\n  if (!indexer) {\n    throw new Error('Unable to utilize the provided indexer');\n  }\n  return await indexer({\n    documents: params.documents,\n    options: params.options,\n  });\n}\n\n/**\n * Zod schema of common retriever options.\n */\nexport const CommonRetrieverOptionsSchema = z.object({\n  k: z.number().describe('Number of documents to retrieve').optional(),\n});\n\n/**\n * A retriver reference object.\n */\nexport interface RetrieverReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: RetrieverInfo;\n}\n\n/**\n * Helper method to configure a {@link RetrieverReference} to a plugin.\n */\nexport function retrieverRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: RetrieverReference<CustomOptionsSchema>\n): RetrieverReference<CustomOptionsSchema> {\n  return { ...options };\n}\n\n// Reuse the same schema for both indexers and retrievers -- for now.\nexport const IndexerInfoSchema = RetrieverInfoSchema;\n\n/**\n * Indexer metadata.\n */\nexport type IndexerInfo = z.infer<typeof IndexerInfoSchema>;\n\nexport interface IndexerReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: IndexerInfo;\n}\n\n/**\n * Helper method to configure a {@link IndexerReference} to a plugin.\n */\nexport function indexerRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: IndexerReference<CustomOptionsSchema>\n): IndexerReference<CustomOptionsSchema> {\n  return { ...options };\n}\n\nfunction itemToDocument<R>(\n  item: any,\n  options: SimpleRetrieverOptions\n): Document {\n  if (!item)\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: `Items returned from simple retriever must be non-null.`,\n    });\n  if (typeof item === 'string') return Document.fromText(item);\n  if (typeof options.content === 'function') {\n    const transformed = options.content(item);\n    return typeof transformed === 'string'\n      ? Document.fromText(transformed)\n      : new Document({ content: transformed });\n  }\n  if (typeof options.content === 'string' && typeof item === 'object')\n    return Document.fromText(item[options.content]);\n  throw new GenkitError({\n    status: 'INVALID_ARGUMENT',\n    message: `Cannot convert item to document without content option. Item: ${JSON.stringify(item)}`,\n  });\n}\n\nfunction itemToMetadata(\n  item: any,\n  options: SimpleRetrieverOptions\n): Document['metadata'] {\n  if (typeof item === 'string') return undefined;\n  if (Array.isArray(options.metadata) && typeof item === 'object') {\n    const out: Record<string, any> = {};\n    options.metadata.forEach((key) => (out[key] = item[key]));\n    return out;\n  }\n  if (typeof options.metadata === 'function') return options.metadata(item);\n  if (!options.metadata && typeof item === 'object') {\n    const out = { ...item };\n    if (typeof options.content === 'string') delete out[options.content];\n    return out;\n  }\n  throw new GenkitError({\n    status: 'INVALID_ARGUMENT',\n    message: `Unable to extract metadata from item with supplied options. Item: ${JSON.stringify(item)}`,\n  });\n}\n\n/**\n * Simple retriever options.\n */\nexport interface SimpleRetrieverOptions<\n  C extends z.ZodTypeAny = z.ZodTypeAny,\n  R = any,\n> {\n  /** The name of the retriever you're creating. */\n  name: string;\n  /** A Zod schema containing any configuration info available beyond the query. */\n  configSchema?: C;\n  /**\n   * Specifies how to extract content from the returned items.\n   *\n   * - If a string, specifies the key of the returned item to extract as content.\n   * - If a function, allows you to extract content as text or a document part.\n   **/\n  content?: string | ((item: R) => Document['content'] | string);\n  /**\n   * Specifies how to extract metadata from the returned items.\n   *\n   * - If an array of strings, specifies list of keys to extract from returned objects.\n   * - If a function, allows you to use custom behavior to extract metadata from returned items.\n   */\n  metadata?: string[] | ((item: R) => Document['metadata']);\n}\n\n/**\n * defineSimpleRetriever makes it easy to map existing data into documents that\n * can be used for prompt augmentation.\n *\n * @param options Configuration options for the retriever.\n * @param handler A function that queries a datastore and returns items from which to extract documents.\n * @returns A Genkit retriever.\n */\nexport function defineSimpleRetriever<\n  C extends z.ZodTypeAny = z.ZodTypeAny,\n  R = any,\n>(\n  registry: Registry,\n  options: SimpleRetrieverOptions<C, R>,\n  handler: (query: Document, config: z.infer<C>) => Promise<R[]>\n) {\n  return defineRetriever(\n    registry,\n    {\n      name: options.name,\n      configSchema: options.configSchema,\n    },\n    async (query, config) => {\n      const result = await handler(query, config);\n      return {\n        documents: result.map((item) => {\n          const doc = itemToDocument(item, options);\n          if (typeof item !== 'string')\n            doc.metadata = itemToMetadata(item, options);\n          return doc;\n        }),\n      };\n    }\n  );\n}\n"], "names": ["import_document"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,oBAAA,CAAA;AAAA,SAAA,mBAAA;IAAA,8BAAA,IAAA;IAAA,UAAA,IAAA,iBAAA,QAAA;IAAA,oBAAA,IAAA,iBAAA,kBAAA;IAAA,mBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,OAAA,IAAA;IAAA,YAAA,IAAA;IAAA,UAAA,IAAA;IAAA,cAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA0D;AAE1D,IAAA,gBAA6B;AAC7B,IAAA,kBAAgE;AAGhE,IAAAA,mBAOO;AAkBP,MAAM,yBAAyB,YAAA,CAAA,CAAE,MAAA,CAAO;IACtC,OAAO,gBAAA,kBAAA;IACP,SAAS,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;AAC5B,CAAC;AAED,MAAM,0BAA0B,YAAA,CAAA,CAAE,MAAA,CAAO;IACvC,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,kBAAkB;AAEvC,CAAC;AAGD,MAAM,uBAAuB,YAAA,CAAA,CAAE,MAAA,CAAO;IACpC,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,kBAAkB;IACrC,SAAS,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;AAC5B,CAAC;AAKM,MAAM,sBAAsB,YAAA,CAAA,CAAE,MAAA,CAAO;IAC1C,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,kCAAA,GAE3B,UAAU,YAAA,CAAA,CACP,MAAA,CAAO;QAAA,sEAAA,GAEN,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC9B,CAAC,EACA,QAAA,CAAS;AACd,CAAC;AAmBD,SAAS,sBAGP,SAAA,EAIA,YAAA,EACmC;IACnC,MAAM,WAAW;IACjB,SAAS,cAAA,GAAiB;IAC1B,OAAO;AACT;AAEA,SAAS,oBAGP,OAAA,EACA,YAAA,EAC+B;IAC/B,MAAM,WAAW;IACjB,SAAS,cAAA,GAAiB;IAC1B,OAAO;AACT;AAKO,SAAS,gBAGd,QAAA,EACA,OAAA,EAKA,MAAA,EACA;IACA,MAAM,YAAA,CAAA,GAAY,YAAA,YAAA,EAChB,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa,QAAQ,YAAA,GACjB,uBAAuB,MAAA,CAAO;YAC5B,SAAS,QAAQ,YAAA,CAAa,QAAA,CAAS;QACzC,CAAC,IACD;QACJ,cAAc;QACd,UAAU;YACR,MAAM;YACN,MAAM,QAAQ,IAAA;YACd,WAAW;gBACT,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;YACN;QACF;IACF,GACA,CAAC,IAAM,OAAO,IAAI,gBAAA,QAAA,CAAS,EAAE,KAAK,GAAG,EAAE,OAAO;IAEhD,MAAM,MAAM,sBACV,WAIA,QAAQ,YAAA;IAEV,OAAO;AACT;AAKO,SAAS,cACd,QAAA,EACA,OAAA,EAKA,MAAA,EACA;IACA,MAAM,UAAA,CAAA,GAAU,YAAA,YAAA,EACd,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa,QAAQ,YAAA,GACjB,qBAAqB,MAAA,CAAO;YAC1B,SAAS,QAAQ,YAAA,CAAa,QAAA,CAAS;QACzC,CAAC,IACD;QACJ,cAAc,YAAA,CAAA,CAAE,IAAA,CAAK;QACrB,UAAU;YACR,MAAM;YACN,cAAc,QAAQ,YAAA;YACtB,SAAS;gBACP,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;YACN;QACF;IACF,GACA,CAAC,IACC,OACE,EAAE,SAAA,CAAU,GAAA,CAAI,CAAC,KAAO,IAAI,gBAAA,QAAA,CAAS,EAAE,CAAC,GACxC,EAAE,OAAA;IAGR,MAAM,MAAM,oBACV,SACA,QAAQ,YAAA;IAEV,OAAO;AACT;AAoBA,eAAsB,SACpB,QAAA,EACA,MAAA,EAC0B;IAC1B,IAAI;IACJ,IAAI,OAAO,OAAO,SAAA,KAAc,UAAU;QACxC,YAAY,MAAM,SAAS,YAAA,CAAa,CAAA,WAAA,EAAc,OAAO,SAAS,EAAE;IAC1E,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,SAAA,EAAW,MAAM,GAAG;QAC/D,YAAY,MAAM,SAAS,YAAA,CACzB,CAAA,WAAA,EAAc,OAAO,SAAA,CAAU,IAAI,EAAA;IAEvC,OAAO;QACL,YAAY,OAAO,SAAA;IACrB;IACA,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,MAAM,iCAAiC;IACnD;IACA,MAAM,WAAW,MAAM,UAAU;QAC/B,OACE,OAAO,OAAO,KAAA,KAAU,WACpB,gBAAA,QAAA,CAAS,QAAA,CAAS,OAAO,KAAK,IAC9B,OAAO,KAAA;QACb,SAAS,OAAO,OAAA;IAClB,CAAC;IAED,OAAO,SAAS,SAAA,CAAU,GAAA,CAAI,CAAC,IAAM,IAAI,gBAAA,QAAA,CAAS,CAAC,CAAC;AACtD;AAsBA,eAAsB,MACpB,QAAA,EACA,MAAA,EACe;IACf,IAAI;IACJ,IAAI,OAAO,OAAO,OAAA,KAAY,UAAU;QACtC,UAAU,MAAM,SAAS,YAAA,CAAa,CAAA,SAAA,EAAY,OAAO,OAAO,EAAE;IACpE,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,OAAA,EAAS,MAAM,GAAG;QAC7D,UAAU,MAAM,SAAS,YAAA,CAAa,CAAA,SAAA,EAAY,OAAO,OAAA,CAAQ,IAAI,EAAE;IACzE,OAAO;QACL,UAAU,OAAO,OAAA;IACnB;IACA,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM,wCAAwC;IAC1D;IACA,OAAO,MAAM,QAAQ;QACnB,WAAW,OAAO,SAAA;QAClB,SAAS,OAAO,OAAA;IAClB,CAAC;AACH;AAKO,MAAM,+BAA+B,YAAA,CAAA,CAAE,MAAA,CAAO;IACnD,GAAG,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,iCAAiC,EAAE,QAAA,CAAS;AACrE,CAAC;AAcM,SAAS,aAGd,OAAA,EACyC;IACzC,OAAO;QAAE,GAAG,OAAA;IAAQ;AACtB;AAGO,MAAM,oBAAoB;AAgB1B,SAAS,WAGd,OAAA,EACuC;IACvC,OAAO;QAAE,GAAG,OAAA;IAAQ;AACtB;AAEA,SAAS,eACP,IAAA,EACA,OAAA,EACU;IACV,IAAI,CAAC,MACH,MAAM,IAAI,YAAA,WAAA,CAAY;QACpB,QAAQ;QACR,SAAS,CAAA,sDAAA,CAAA;IACX,CAAC;IACH,IAAI,OAAO,SAAS,SAAU,CAAA,OAAO,gBAAA,QAAA,CAAS,QAAA,CAAS,IAAI;IAC3D,IAAI,OAAO,QAAQ,OAAA,KAAY,YAAY;QACzC,MAAM,cAAc,QAAQ,OAAA,CAAQ,IAAI;QACxC,OAAO,OAAO,gBAAgB,WAC1B,gBAAA,QAAA,CAAS,QAAA,CAAS,WAAW,IAC7B,IAAI,gBAAA,QAAA,CAAS;YAAE,SAAS;QAAY,CAAC;IAC3C;IACA,IAAI,OAAO,QAAQ,OAAA,KAAY,YAAY,OAAO,SAAS,UACzD,OAAO,gBAAA,QAAA,CAAS,QAAA,CAAS,IAAA,CAAK,QAAQ,OAAO,CAAC;IAChD,MAAM,IAAI,YAAA,WAAA,CAAY;QACpB,QAAQ;QACR,SAAS,CAAA,8DAAA,EAAiE,KAAK,SAAA,CAAU,IAAI,CAAC,EAAA;IAChG,CAAC;AACH;AAEA,SAAS,eACP,IAAA,EACA,OAAA,EACsB;IACtB,IAAI,OAAO,SAAS,SAAU,CAAA,OAAO,KAAA;IACrC,IAAI,MAAM,OAAA,CAAQ,QAAQ,QAAQ,KAAK,OAAO,SAAS,UAAU;QAC/D,MAAM,MAA2B,CAAC;QAClC,QAAQ,QAAA,CAAS,OAAA,CAAQ,CAAC,MAAS,GAAA,CAAI,GAAG,CAAA,GAAI,IAAA,CAAK,GAAG,CAAE;QACxD,OAAO;IACT;IACA,IAAI,OAAO,QAAQ,QAAA,KAAa,WAAY,CAAA,OAAO,QAAQ,QAAA,CAAS,IAAI;IACxE,IAAI,CAAC,QAAQ,QAAA,IAAY,OAAO,SAAS,UAAU;QACjD,MAAM,MAAM;YAAE,GAAG,IAAA;QAAK;QACtB,IAAI,OAAO,QAAQ,OAAA,KAAY,SAAU,CAAA,OAAO,GAAA,CAAI,QAAQ,OAAO,CAAA;QACnE,OAAO;IACT;IACA,MAAM,IAAI,YAAA,WAAA,CAAY;QACpB,QAAQ;QACR,SAAS,CAAA,kEAAA,EAAqE,KAAK,SAAA,CAAU,IAAI,CAAC,EAAA;IACpG,CAAC;AACH;AAqCO,SAAS,sBAId,QAAA,EACA,OAAA,EACA,OAAA,EACA;IACA,OAAO,gBACL,UACA;QACE,MAAM,QAAQ,IAAA;QACd,cAAc,QAAQ,YAAA;IACxB,GACA,OAAO,OAAO,WAAW;QACvB,MAAM,SAAS,MAAM,QAAQ,OAAO,MAAM;QAC1C,OAAO;YACL,WAAW,OAAO,GAAA,CAAI,CAAC,SAAS;gBAC9B,MAAM,MAAM,eAAe,MAAM,OAAO;gBACxC,IAAI,OAAO,SAAS,UAClB,IAAI,QAAA,GAAW,eAAe,MAAM,OAAO;gBAC7C,OAAO;YACT,CAAC;QACH;IACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/reranker.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { defineAction, z, type Action } from '@genkit-ai/core';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { PartSchema, type Part } from './document.js';\nimport {\n  Document,\n  DocumentDataSchema,\n  type DocumentData,\n} from './retriever.js';\n\nexport type RerankerFn<RerankerOptions extends z.ZodTypeAny> = (\n  query: Document,\n  documents: Document[],\n  queryOpts: z.infer<RerankerOptions>\n) => Promise<RerankerResponse>;\n\n/**\n * Zod schema for a reranked document metadata.\n */\nexport const RankedDocumentMetadataSchema = z\n  .object({\n    score: z.number(), // Enforces that 'score' must be a number\n  })\n  .passthrough(); // Allows other properties in 'metadata' with any type\n\nexport const RankedDocumentDataSchema = z.object({\n  content: z.array(PartSchema),\n  metadata: RankedDocumentMetadataSchema,\n});\n\nexport type RankedDocumentData = z.infer<typeof RankedDocumentDataSchema>;\n\nexport class RankedDocument extends Document implements RankedDocumentData {\n  content: Part[];\n  metadata: { score: number } & Record<string, any>;\n\n  constructor(data: RankedDocumentData) {\n    super(data);\n    this.content = data.content;\n    this.metadata = data.metadata;\n  }\n  /**\n   * Returns the score of the document.\n   * @returns The score of the document.\n   */\n  score(): number {\n    return this.metadata.score;\n  }\n}\n\nconst RerankerRequestSchema = z.object({\n  query: DocumentDataSchema,\n  documents: z.array(DocumentDataSchema),\n  options: z.any().optional(),\n});\n\nconst RerankerResponseSchema = z.object({\n  documents: z.array(RankedDocumentDataSchema),\n});\ntype RerankerResponse = z.infer<typeof RerankerResponseSchema>;\n\nexport const RerankerInfoSchema = z.object({\n  label: z.string().optional(),\n  /** Supported model capabilities. */\n  supports: z\n    .object({\n      /** Model can process media as part of the prompt (multimodal input). */\n      media: z.boolean().optional(),\n    })\n    .optional(),\n});\nexport type RerankerInfo = z.infer<typeof RerankerInfoSchema>;\n\nexport type RerankerAction<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  Action<typeof RerankerRequestSchema, typeof RerankerResponseSchema> & {\n    __configSchema?: CustomOptions;\n  };\n\nfunction rerankerWithMetadata<\n  RerankerOptions extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  reranker: Action<typeof RerankerRequestSchema, typeof RerankerResponseSchema>,\n  configSchema?: RerankerOptions\n): RerankerAction<RerankerOptions> {\n  const withMeta = reranker as RerankerAction<RerankerOptions>;\n  withMeta.__configSchema = configSchema;\n  return withMeta;\n}\n\n/**\n *  Creates a reranker action for the provided {@link RerankerFn} implementation.\n */\nexport function defineReranker<OptionsType extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  options: {\n    name: string;\n    configSchema?: OptionsType;\n    info?: RerankerInfo;\n  },\n  runner: RerankerFn<OptionsType>\n) {\n  const reranker = defineAction(\n    registry,\n    {\n      actionType: 'reranker',\n      name: options.name,\n      inputSchema: options.configSchema\n        ? RerankerRequestSchema.extend({\n            options: options.configSchema.optional(),\n          })\n        : RerankerRequestSchema,\n      outputSchema: RerankerResponseSchema,\n      metadata: {\n        type: 'reranker',\n        info: options.info,\n        reranker: {\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n        },\n      },\n    },\n    (i) =>\n      runner(\n        new Document(i.query),\n        i.documents.map((d) => new Document(d)),\n        i.options\n      )\n  );\n  const rwm = rerankerWithMetadata(\n    reranker as Action<\n      typeof RerankerRequestSchema,\n      typeof RerankerResponseSchema\n    >,\n    options.configSchema\n  );\n  return rwm;\n}\n\nexport interface RerankerParams<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  reranker: RerankerArgument<CustomOptions>;\n  query: string | DocumentData;\n  documents: DocumentData[];\n  options?: z.infer<CustomOptions>;\n}\n\nexport type RerankerArgument<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> = RerankerAction<CustomOptions> | RerankerReference<CustomOptions> | string;\n\n/**\n * Reranks documents from a {@link RerankerArgument} based on the provided query.\n */\nexport async function rerank<CustomOptions extends z.ZodTypeAny>(\n  registry: Registry,\n  params: RerankerParams<CustomOptions>\n): Promise<Array<RankedDocument>> {\n  let reranker: RerankerAction<CustomOptions>;\n  if (typeof params.reranker === 'string') {\n    reranker = await registry.lookupAction(`/reranker/${params.reranker}`);\n  } else if (Object.hasOwnProperty.call(params.reranker, 'info')) {\n    reranker = await registry.lookupAction(`/reranker/${params.reranker.name}`);\n  } else {\n    reranker = params.reranker as RerankerAction<CustomOptions>;\n  }\n  if (!reranker) {\n    throw new Error('Unable to resolve the reranker');\n  }\n  const response = await reranker({\n    query:\n      typeof params.query === 'string'\n        ? Document.fromText(params.query)\n        : params.query,\n    documents: params.documents,\n    options: params.options,\n  });\n\n  return response.documents.map((d) => new RankedDocument(d));\n}\n\nexport const CommonRerankerOptionsSchema = z.object({\n  k: z.number().describe('Number of documents to rerank').optional(),\n});\n\nexport interface RerankerReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: RerankerInfo;\n}\n\n/**\n * Helper method to configure a {@link RerankerReference} to a plugin.\n */\nexport function rerankerRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: RerankerReference<CustomOptionsSchema>\n): RerankerReference<CustomOptionsSchema> {\n  return { ...options };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,6BAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,0BAAA,IAAA;IAAA,8BAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,QAAA,IAAA;IAAA,aAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA6C;AAE7C,IAAA,gBAA6B;AAC7B,IAAA,kBAAsC;AACtC,IAAA,mBAIO;AAWA,MAAM,+BAA+B,YAAA,CAAA,CACzC,MAAA,CAAO;IACN,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO;AAClB,CAAC,EACA,WAAA,CAAY;AAER,MAAM,2BAA2B,YAAA,CAAA,CAAE,MAAA,CAAO;IAC/C,SAAS,YAAA,CAAA,CAAE,KAAA,CAAM,gBAAA,UAAU;IAC3B,UAAU;AACZ,CAAC;AAIM,MAAM,uBAAuB,iBAAA,QAAA,CAAuC;IACzE,QAAA;IACA,SAAA;IAEA,YAAY,IAAA,CAA0B;QACpC,KAAA,CAAM,IAAI;QACV,IAAA,CAAK,OAAA,GAAU,KAAK,OAAA;QACpB,IAAA,CAAK,QAAA,GAAW,KAAK,QAAA;IACvB;IAAA;;;GAAA,GAKA,QAAgB;QACd,OAAO,IAAA,CAAK,QAAA,CAAS,KAAA;IACvB;AACF;AAEA,MAAM,wBAAwB,YAAA,CAAA,CAAE,MAAA,CAAO;IACrC,OAAO,iBAAA,kBAAA;IACP,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,iBAAA,kBAAkB;IACrC,SAAS,YAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;AAC5B,CAAC;AAED,MAAM,yBAAyB,YAAA,CAAA,CAAE,MAAA,CAAO;IACtC,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,wBAAwB;AAC7C,CAAC;AAGM,MAAM,qBAAqB,YAAA,CAAA,CAAE,MAAA,CAAO;IACzC,OAAO,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,kCAAA,GAE3B,UAAU,YAAA,CAAA,CACP,MAAA,CAAO;QAAA,sEAAA,GAEN,OAAO,YAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC9B,CAAC,EACA,QAAA,CAAS;AACd,CAAC;AAQD,SAAS,qBAGP,QAAA,EACA,YAAA,EACiC;IACjC,MAAM,WAAW;IACjB,SAAS,cAAA,GAAiB;IAC1B,OAAO;AACT;AAKO,SAAS,eACd,QAAA,EACA,OAAA,EAKA,MAAA,EACA;IACA,MAAM,WAAA,CAAA,GAAW,YAAA,YAAA,EACf,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa,QAAQ,YAAA,GACjB,sBAAsB,MAAA,CAAO;YAC3B,SAAS,QAAQ,YAAA,CAAa,QAAA,CAAS;QACzC,CAAC,IACD;QACJ,cAAc;QACd,UAAU;YACR,MAAM;YACN,MAAM,QAAQ,IAAA;YACd,UAAU;gBACR,eAAe,QAAQ,YAAA,GAAA,CAAA,GACnB,cAAA,YAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;YACN;QACF;IACF,GACA,CAAC,IACC,OACE,IAAI,iBAAA,QAAA,CAAS,EAAE,KAAK,GACpB,EAAE,SAAA,CAAU,GAAA,CAAI,CAAC,IAAM,IAAI,iBAAA,QAAA,CAAS,CAAC,CAAC,GACtC,EAAE,OAAA;IAGR,MAAM,MAAM,qBACV,UAIA,QAAQ,YAAA;IAEV,OAAO;AACT;AAkBA,eAAsB,OACpB,QAAA,EACA,MAAA,EACgC;IAChC,IAAI;IACJ,IAAI,OAAO,OAAO,QAAA,KAAa,UAAU;QACvC,WAAW,MAAM,SAAS,YAAA,CAAa,CAAA,UAAA,EAAa,OAAO,QAAQ,EAAE;IACvE,OAAA,IAAW,OAAO,cAAA,CAAe,IAAA,CAAK,OAAO,QAAA,EAAU,MAAM,GAAG;QAC9D,WAAW,MAAM,SAAS,YAAA,CAAa,CAAA,UAAA,EAAa,OAAO,QAAA,CAAS,IAAI,EAAE;IAC5E,OAAO;QACL,WAAW,OAAO,QAAA;IACpB;IACA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,gCAAgC;IAClD;IACA,MAAM,WAAW,MAAM,SAAS;QAC9B,OACE,OAAO,OAAO,KAAA,KAAU,WACpB,iBAAA,QAAA,CAAS,QAAA,CAAS,OAAO,KAAK,IAC9B,OAAO,KAAA;QACb,WAAW,OAAO,SAAA;QAClB,SAAS,OAAO,OAAA;IAClB,CAAC;IAED,OAAO,SAAS,SAAA,CAAU,GAAA,CAAI,CAAC,IAAM,IAAI,eAAe,CAAC,CAAC;AAC5D;AAEO,MAAM,8BAA8B,YAAA,CAAA,CAAE,MAAA,CAAO;IAClD,GAAG,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,+BAA+B,EAAE,QAAA,CAAS;AACnE,CAAC;AAWM,SAAS,YAGd,OAAA,EACwC;IACxC,OAAO;QAAE,GAAG,OAAA;IAAQ;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5250, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z, type Action } from '@genkit-ai/core';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\n\nexport const LlmStatsSchema = z.object({\n  latencyMs: z.number().optional(),\n  inputTokenCount: z.number().optional(),\n  outputTokenCount: z.number().optional(),\n});\n\nexport type LlmStats = z.infer<typeof LlmStatsSchema>;\n\nexport const ToolSchema = z.object({\n  name: z.string(),\n  description: z.string().optional(),\n  schema: z.any(),\n});\n\nexport type Tool = z.infer<typeof ToolSchema>;\n\nexport const ToolCallSchema = z.object({\n  toolName: z.string(),\n  arguments: z.any(),\n});\n\nexport type ToolCall = z.infer<typeof ToolCallSchema>;\n\nexport const LlmResponseSchema = z.object({\n  completion: z.string(),\n  toolCalls: z.array(ToolCallSchema).optional(),\n  stats: LlmStatsSchema,\n});\n\nexport type LlmResponse = z.infer<typeof LlmResponseSchema>;\n\n/**\n * Converts actions to tool definition sent to model inputs.\n */\nexport function toToolWireFormat(\n  actions?: Action<any, any>[]\n): z.infer<typeof ToolSchema>[] | undefined {\n  if (!actions) return undefined;\n  return actions.map((a) => {\n    return {\n      name: a.__action.name,\n      description: a.__action.description,\n      schema: {\n        input: toJsonSchema({\n          schema: a.__action.inputSchema,\n          jsonSchema: a.__action.inputJsonSchema,\n        }),\n        output: toJsonSchema({\n          schema: a.__action.outputSchema,\n          jsonSchema: a.__action.outputJsonSchema,\n        }),\n      },\n    };\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,mBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,YAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,cAA+B;AAC/B,IAAA,gBAA6B;AAEtB,MAAM,iBAAiB,YAAA,CAAA,CAAE,MAAA,CAAO;IACrC,WAAW,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC/B,iBAAiB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACrC,kBAAkB,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AACxC,CAAC;AAIM,MAAM,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO;IACjC,MAAM,YAAA,CAAA,CAAE,MAAA,CAAO;IACf,aAAa,YAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,QAAQ,YAAA,CAAA,CAAE,GAAA,CAAI;AAChB,CAAC;AAIM,MAAM,iBAAiB,YAAA,CAAA,CAAE,MAAA,CAAO;IACrC,UAAU,YAAA,CAAA,CAAE,MAAA,CAAO;IACnB,WAAW,YAAA,CAAA,CAAE,GAAA,CAAI;AACnB,CAAC;AAIM,MAAM,oBAAoB,YAAA,CAAA,CAAE,MAAA,CAAO;IACxC,YAAY,YAAA,CAAA,CAAE,MAAA,CAAO;IACrB,WAAW,YAAA,CAAA,CAAE,KAAA,CAAM,cAAc,EAAE,QAAA,CAAS;IAC5C,OAAO;AACT,CAAC;AAOM,SAAS,iBACd,OAAA,EAC0C;IAC1C,IAAI,CAAC,QAAS,CAAA,OAAO,KAAA;IACrB,OAAO,QAAQ,GAAA,CAAI,CAAC,MAAM;QACxB,OAAO;YACL,MAAM,EAAE,QAAA,CAAS,IAAA;YACjB,aAAa,EAAE,QAAA,CAAS,WAAA;YACxB,QAAQ;gBACN,OAAA,CAAA,GAAO,cAAA,YAAA,EAAa;oBAClB,QAAQ,EAAE,QAAA,CAAS,WAAA;oBACnB,YAAY,EAAE,QAAA,CAAS,eAAA;gBACzB,CAAC;gBACD,QAAA,CAAA,GAAQ,cAAA,YAAA,EAAa;oBACnB,QAAQ,EAAE,QAAA,CAAS,YAAA;oBACnB,YAAY,EAAE,QAAA,CAAS,gBAAA;gBACzB,CAAC;YACH;QACF;IACF,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5335, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { checkOperation } from './check-operation.js';\nexport {\n  Document,\n  DocumentDataSchema,\n  type DocumentData,\n  type ToolRequest,\n  type ToolResponse,\n} from './document.js';\nexport {\n  embed,\n  embedderActionMetadata,\n  embedderRef,\n  type EmbedderAction,\n  type EmbedderArgument,\n  type EmbedderInfo,\n  type EmbedderParams,\n  type EmbedderReference,\n  type Embedding,\n} from './embedder.js';\nexport {\n  BaseDataPointSchema,\n  EvalStatusEnum,\n  evaluate,\n  evaluatorRef,\n  type EvalResponses,\n  type EvaluatorAction,\n  type EvaluatorInfo,\n  type EvaluatorParams,\n  type EvaluatorReference,\n} from './evaluator.js';\nexport {\n  GenerateResponse,\n  GenerateResponseChunk,\n  GenerationBlockedError,\n  GenerationResponseError,\n  generate,\n  generateOperation,\n  generateStream,\n  tagAsPreamble,\n  toGenerateRequest,\n  type GenerateOptions,\n  type GenerateStreamOptions,\n  type GenerateStreamResponse,\n  type OutputOptions,\n  type ResumeOptions,\n  type ToolChoice,\n} from './generate.js';\nexport { Message } from './message.js';\nexport {\n  GenerateResponseChunkSchema,\n  GenerationCommonConfigSchema,\n  MessageSchema,\n  ModelRequestSchema,\n  ModelResponseSchema,\n  PartSchema,\n  RoleSchema,\n  modelActionMetadata,\n  modelRef,\n  type GenerateRequest,\n  type GenerateRequestData,\n  type GenerateResponseChunkData,\n  type GenerateResponseData,\n  type GenerationUsage,\n  type MediaPart,\n  type MessageData,\n  type ModelArgument,\n  type ModelReference,\n  type ModelRequest,\n  type ModelResponseData,\n  type Part,\n  type Role,\n  type ToolRequestPart,\n  type ToolResponsePart,\n} from './model.js';\nexport {\n  defineHelper,\n  definePartial,\n  definePrompt,\n  isExecutablePrompt,\n  loadPromptFolder,\n  prompt,\n  type ExecutablePrompt,\n  type PromptAction,\n  type PromptConfig,\n  type PromptGenerateOptions,\n} from './prompt.js';\nexport {\n  rerank,\n  rerankerRef,\n  type RankedDocument,\n  type RerankerAction,\n  type RerankerArgument,\n  type RerankerInfo,\n  type RerankerParams,\n  type RerankerReference,\n} from './reranker.js';\nexport {\n  ResourceInputSchema,\n  ResourceOutputSchema,\n  defineResource,\n  type ResourceAction,\n  type ResourceFn,\n  type ResourceInput,\n  type ResourceOptions,\n  type ResourceOutput,\n} from './resource.js';\nexport {\n  index,\n  indexerRef,\n  retrieve,\n  retrieverRef,\n  type IndexerAction,\n  type IndexerArgument,\n  type IndexerInfo,\n  type IndexerParams,\n  type IndexerReference,\n  type RetrieverAction,\n  type RetrieverArgument,\n  type RetrieverInfo,\n  type RetrieverParams,\n  type RetrieverReference,\n} from './retriever.js';\nexport {\n  ToolInterruptError,\n  asTool,\n  defineInterrupt,\n  defineTool,\n  type InterruptConfig,\n  type ToolAction,\n  type ToolArgument,\n  type ToolConfig,\n} from './tool.js';\nexport * from './types.js';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,qBAAA,IAAA,iBAAA,mBAAA;IAAA,UAAA,IAAA,gBAAA,QAAA;IAAA,oBAAA,IAAA,gBAAA,kBAAA;IAAA,gBAAA,IAAA,iBAAA,cAAA;IAAA,kBAAA,IAAA,gBAAA,gBAAA;IAAA,uBAAA,IAAA,gBAAA,qBAAA;IAAA,6BAAA,IAAA,aAAA,2BAAA;IAAA,wBAAA,IAAA,gBAAA,sBAAA;IAAA,8BAAA,IAAA,aAAA,4BAAA;IAAA,yBAAA,IAAA,gBAAA,uBAAA;IAAA,SAAA,IAAA,eAAA,OAAA;IAAA,eAAA,IAAA,aAAA,aAAA;IAAA,oBAAA,IAAA,aAAA,kBAAA;IAAA,qBAAA,IAAA,aAAA,mBAAA;IAAA,YAAA,IAAA,aAAA,UAAA;IAAA,qBAAA,IAAA,gBAAA,mBAAA;IAAA,sBAAA,IAAA,gBAAA,oBAAA;IAAA,YAAA,IAAA,aAAA,UAAA;IAAA,oBAAA,IAAA,YAAA,kBAAA;IAAA,QAAA,IAAA,YAAA,MAAA;IAAA,gBAAA,IAAA,uBAAA,cAAA;IAAA,cAAA,IAAA,cAAA,YAAA;IAAA,iBAAA,IAAA,YAAA,eAAA;IAAA,eAAA,IAAA,cAAA,aAAA;IAAA,cAAA,IAAA,cAAA,YAAA;IAAA,gBAAA,IAAA,gBAAA,cAAA;IAAA,YAAA,IAAA,YAAA,UAAA;IAAA,OAAA,IAAA,gBAAA,KAAA;IAAA,wBAAA,IAAA,gBAAA,sBAAA;IAAA,aAAA,IAAA,gBAAA,WAAA;IAAA,UAAA,IAAA,iBAAA,QAAA;IAAA,cAAA,IAAA,iBAAA,YAAA;IAAA,UAAA,IAAA,gBAAA,QAAA;IAAA,mBAAA,IAAA,gBAAA,iBAAA;IAAA,gBAAA,IAAA,gBAAA,cAAA;IAAA,OAAA,IAAA,iBAAA,KAAA;IAAA,YAAA,IAAA,iBAAA,UAAA;IAAA,oBAAA,IAAA,cAAA,kBAAA;IAAA,kBAAA,IAAA,cAAA,gBAAA;IAAA,qBAAA,IAAA,aAAA,mBAAA;IAAA,UAAA,IAAA,aAAA,QAAA;IAAA,QAAA,IAAA,cAAA,MAAA;IAAA,QAAA,IAAA,gBAAA,MAAA;IAAA,aAAA,IAAA,gBAAA,WAAA;IAAA,UAAA,IAAA,iBAAA,QAAA;IAAA,cAAA,IAAA,iBAAA,YAAA;IAAA,eAAA,IAAA,gBAAA,aAAA;IAAA,mBAAA,IAAA,gBAAA,iBAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,yBAA+B;AAC/B,IAAA,kBAMO;AACP,IAAA,kBAUO;AACP,IAAA,mBAUO;AACP,IAAA,kBAgBO;AACP,IAAA,iBAAwB;AACxB,IAAA,eAyBO;AACP,IAAA,gBAWO;AACP,IAAA,kBASO;AACP,IAAA,kBASO;AACP,IAAA,mBAeO;AACP,IAAA,cASO;AACP,WAAA,eAAc,6IApJd,OAAA,OAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5482, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bai%401.14.0/node_modules/%40genkit-ai/ai/src/model.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ActionFnArg,\n  BackgroundAction,\n  GenkitError,\n  Operation,\n  OperationSchema,\n  defineAction,\n  defineBackgroundAction,\n  getStreamingCallback,\n  z,\n  type Action,\n  type ActionMetadata,\n  type SimpleMiddleware,\n  type StreamingCallback,\n} from '@genkit-ai/core';\nimport { logger } from '@genkit-ai/core/logging';\nimport type { Registry } from '@genkit-ai/core/registry';\nimport { toJsonSchema } from '@genkit-ai/core/schema';\nimport { performance } from 'node:perf_hooks';\nimport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n} from './document.js';\nimport {\n  CandidateData,\n  GenerateRequest,\n  GenerateRequestSchema,\n  GenerateResponseChunkData,\n  GenerateResponseChunkSchema,\n  GenerateResponseData,\n  GenerateResponseSchema,\n  GenerationUsage,\n  MessageData,\n  ModelInfo,\n  Part,\n} from './model-types.js';\nimport {\n  augmentWithContext,\n  simulateConstrainedGeneration,\n  validateSupport,\n} from './model/middleware.js';\nexport { defineGenerateAction } from './generate/action.js';\nexport * from './model-types.js';\nexport {\n  CustomPartSchema,\n  DataPartSchema,\n  MediaPartSchema,\n  TextPartSchema,\n  ToolRequestPartSchema,\n  ToolResponsePartSchema,\n  simulateConstrainedGeneration,\n  type CustomPart,\n  type DataPart,\n  type MediaPart,\n  type TextPart,\n  type ToolRequestPart,\n  type ToolResponsePart,\n};\n\nexport type ModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = Action<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema,\n  typeof GenerateResponseChunkSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type BackgroundModelAction<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = BackgroundAction<\n  typeof GenerateRequestSchema,\n  typeof GenerateResponseSchema\n> & {\n  __configSchema: CustomOptionsSchema;\n};\n\nexport type ModelMiddleware = SimpleMiddleware<\n  z.infer<typeof GenerateRequestSchema>,\n  z.infer<typeof GenerateResponseSchema>\n>;\n\nexport type DefineModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name: string;\n  /** Known version names for this model, e.g. `gemini-1.0-pro-001`. */\n  versions?: string[];\n  /** Capabilities this model supports. */\n  supports?: ModelInfo['supports'];\n  /** Custom options schema for this model. */\n  configSchema?: CustomOptionsSchema;\n  /** Descriptive name for this model e.g. 'Google AI - Gemini Pro'. */\n  label?: string;\n  /** Middleware to be used with this model. */\n  use?: ModelMiddleware[];\n};\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: {\n    apiVersion: 'v2';\n  } & DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: ActionFnArg<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\n/**\n * Defines a new model and adds it to the registry.\n */\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineModelOptions<CustomOptionsSchema>,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    streamingCallback?: StreamingCallback<GenerateResponseChunkData>\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema>;\n\nexport function defineModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: any,\n  runner: (\n    request: GenerateRequest<CustomOptionsSchema>,\n    options: any\n  ) => Promise<GenerateResponseData>\n): ModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineAction(\n    registry,\n    {\n      actionType: 'model',\n      name: options.name,\n      description: label,\n      inputSchema: GenerateRequestSchema,\n      outputSchema: GenerateResponseSchema,\n      metadata: {\n        model: {\n          label,\n          customOptions: options.configSchema\n            ? toJsonSchema({ schema: options.configSchema })\n            : undefined,\n          versions: options.versions,\n          supports: options.supports,\n        },\n      },\n      use: middleware,\n    },\n    (input, ctx) => {\n      const startTimeMs = performance.now();\n      const secondParam =\n        options.apiVersion === 'v2'\n          ? ctx\n          : getStreamingCallback(registry) ||\n            (ctx.streamingRequested && ctx.sendChunk) ||\n            undefined;\n      return runner(input, secondParam).then((response) => {\n        const timedResponse = {\n          ...response,\n          latencyMs: performance.now() - startTimeMs,\n        };\n        return timedResponse;\n      });\n    }\n  );\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act as ModelAction<CustomOptionsSchema>;\n}\n\nexport type DefineBackgroundModelOptions<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n> = DefineModelOptions<CustomOptionsSchema> & {\n  start: (\n    request: GenerateRequest<CustomOptionsSchema>\n  ) => Promise<Operation<GenerateResponseData>>;\n  check: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n  cancel?: (\n    operation: Operation<GenerateResponseData>\n  ) => Promise<Operation<GenerateResponseData>>;\n};\n\n/**\n * Defines a new model that runs in the background.\n */\nexport function defineBackgroundModel<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  options: DefineBackgroundModelOptions<CustomOptionsSchema>\n): BackgroundModelAction<CustomOptionsSchema> {\n  const label = options.label || options.name;\n  const middleware = getModelMiddleware(options);\n  const act = defineBackgroundAction(registry, {\n    actionType: 'background-model',\n    name: options.name,\n    description: label,\n    inputSchema: GenerateRequestSchema,\n    outputSchema: GenerateResponseSchema,\n    metadata: {\n      model: {\n        label,\n        customOptions: options.configSchema\n          ? toJsonSchema({ schema: options.configSchema })\n          : undefined,\n        versions: options.versions,\n        supports: options.supports,\n      },\n    },\n    use: middleware,\n    async start(request) {\n      const startTimeMs = performance.now();\n      const response = await options.start(request);\n      Object.assign(response, {\n        latencyMs: performance.now() - startTimeMs,\n      });\n      return response;\n    },\n    async check(op) {\n      return options.check(op);\n    },\n    cancel: options.cancel\n      ? async (op) => {\n          if (!options.cancel) {\n            throw new GenkitError({\n              status: 'UNIMPLEMENTED',\n              message: 'cancel not implemented',\n            });\n          }\n          return options.cancel(op);\n        }\n      : undefined,\n  }) as BackgroundModelAction<CustomOptionsSchema>;\n  Object.assign(act, {\n    __configSchema: options.configSchema || z.unknown(),\n  });\n  return act;\n}\n\nfunction getModelMiddleware(options: {\n  use?: ModelMiddleware[];\n  name: string;\n  supports?: ModelInfo['supports'];\n}) {\n  const middleware: ModelMiddleware[] = [\n    ...(options.use || []),\n    validateSupport(options),\n  ];\n  if (!options?.supports?.context) middleware.push(augmentWithContext());\n  const constratedSimulator = simulateConstrainedGeneration();\n  middleware.push((req, next) => {\n    if (\n      !options?.supports?.constrained ||\n      options?.supports?.constrained === 'none' ||\n      (options?.supports?.constrained === 'no-tools' &&\n        (req.tools?.length ?? 0) > 0)\n    ) {\n      return constratedSimulator(req, next);\n    }\n    return next(req);\n  });\n\n  return middleware;\n}\n\nexport interface ModelReference<CustomOptions extends z.ZodTypeAny> {\n  name: string;\n  configSchema?: CustomOptions;\n  info?: ModelInfo;\n  version?: string;\n  config?: z.infer<CustomOptions>;\n\n  withConfig(cfg: z.infer<CustomOptions>): ModelReference<CustomOptions>;\n  withVersion(version: string): ModelReference<CustomOptions>;\n}\n\n/**\n * Packages model information into ActionMetadata object.\n */\nexport function modelActionMetadata({\n  name,\n  info,\n  configSchema,\n  background,\n}: {\n  name: string;\n  info?: ModelInfo;\n  configSchema?: z.ZodTypeAny;\n  background?: boolean;\n}): ActionMetadata {\n  return {\n    actionType: background ? 'background-model' : 'model',\n    name: name,\n    inputJsonSchema: toJsonSchema({ schema: GenerateRequestSchema }),\n    outputJsonSchema: background\n      ? toJsonSchema({ schema: OperationSchema })\n      : toJsonSchema({ schema: GenerateResponseSchema }),\n    metadata: {\n      model: {\n        ...info,\n        customOptions: configSchema\n          ? toJsonSchema({ schema: configSchema })\n          : undefined,\n      },\n    },\n  } as ActionMetadata;\n}\n\n/** Cretes a model reference. */\nexport function modelRef<\n  CustomOptionsSchema extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  options: Omit<\n    ModelReference<CustomOptionsSchema>,\n    'withConfig' | 'withVersion'\n  >\n): ModelReference<CustomOptionsSchema> {\n  const ref: Partial<ModelReference<CustomOptionsSchema>> = { ...options };\n  ref.withConfig = (\n    cfg: z.infer<CustomOptionsSchema>\n  ): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      config: cfg,\n    });\n  };\n  ref.withVersion = (version: string): ModelReference<CustomOptionsSchema> => {\n    return modelRef({\n      ...options,\n      version,\n    });\n  };\n  return ref as ModelReference<CustomOptionsSchema>;\n}\n\n/** Container for counting usage stats for a single input/output {Part} */\ntype PartCounts = {\n  characters: number;\n  images: number;\n  videos: number;\n  audio: number;\n};\n\n/**\n * Calculates basic usage statistics from the given model inputs and outputs.\n */\nexport function getBasicUsageStats(\n  input: MessageData[],\n  response: MessageData | CandidateData[]\n): GenerationUsage {\n  const inputCounts = getPartCounts(input.flatMap((md) => md.content));\n  const outputCounts = getPartCounts(\n    Array.isArray(response)\n      ? response.flatMap((c) => c.message.content)\n      : response.content\n  );\n  return {\n    inputCharacters: inputCounts.characters,\n    inputImages: inputCounts.images,\n    inputVideos: inputCounts.videos,\n    inputAudioFiles: inputCounts.audio,\n    outputCharacters: outputCounts.characters,\n    outputImages: outputCounts.images,\n    outputVideos: outputCounts.videos,\n    outputAudioFiles: outputCounts.audio,\n  };\n}\n\nfunction getPartCounts(parts: Part[]): PartCounts {\n  return parts.reduce(\n    (counts, part) => {\n      const isImage =\n        part.media?.contentType?.startsWith('image') ||\n        part.media?.url?.startsWith('data:image');\n      const isVideo =\n        part.media?.contentType?.startsWith('video') ||\n        part.media?.url?.startsWith('data:video');\n      const isAudio =\n        part.media?.contentType?.startsWith('audio') ||\n        part.media?.url?.startsWith('data:audio');\n      return {\n        characters: counts.characters + (part.text?.length || 0),\n        images: counts.images + (isImage ? 1 : 0),\n        videos: counts.videos + (isVideo ? 1 : 0),\n        audio: counts.audio + (isAudio ? 1 : 0),\n      };\n    },\n    { characters: 0, images: 0, videos: 0, audio: 0 }\n  );\n}\n\nexport type ModelArgument<CustomOptions extends z.ZodTypeAny = z.ZodTypeAny> =\n  | ModelAction<CustomOptions>\n  | ModelReference<CustomOptions>\n  | string;\n\nexport interface ResolvedModel<\n  CustomOptions extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  modelAction: ModelAction;\n  config?: z.infer<CustomOptions>;\n  version?: string;\n}\n\nexport async function resolveModel<C extends z.ZodTypeAny = z.ZodTypeAny>(\n  registry: Registry,\n  model: ModelArgument<C> | undefined,\n  options?: { warnDeprecated?: boolean }\n): Promise<ResolvedModel<C>> {\n  let out: ResolvedModel<C>;\n  let modelId: string;\n\n  if (!model) {\n    model = await registry.lookupValue('defaultModel', 'defaultModel');\n  }\n  if (!model) {\n    throw new GenkitError({\n      status: 'INVALID_ARGUMENT',\n      message: 'Must supply a `model` to `generate()` calls.',\n    });\n  }\n  if (typeof model === 'string') {\n    modelId = model;\n    out = { modelAction: await lookupModel(registry, model) };\n  } else if (model.hasOwnProperty('__action')) {\n    modelId = (model as ModelAction).__action.name;\n    out = { modelAction: model as ModelAction };\n  } else {\n    const ref = model as ModelReference<any>;\n    modelId = ref.name;\n    out = {\n      modelAction: await lookupModel(registry, ref.name),\n      config: {\n        ...ref.config,\n      },\n      version: ref.version,\n    };\n  }\n\n  if (!out.modelAction) {\n    throw new GenkitError({\n      status: 'NOT_FOUND',\n      message: `Model '${modelId}' not found`,\n    });\n  }\n\n  if (\n    options?.warnDeprecated &&\n    out.modelAction.__action.metadata?.model?.stage === 'deprecated'\n  ) {\n    logger.warn(\n      `Model '${out.modelAction.__action.name}' is deprecated and may be removed in a future release.`\n    );\n  }\n\n  return out;\n}\n\nasync function lookupModel(\n  registry: Registry,\n  model: string\n): Promise<ModelAction> {\n  return (\n    (await registry.lookupAction(`/model/${model}`)) ||\n    (await registry.lookupAction(`/background-model/${model}`))\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAgBA;;;;;AAeA,SAAS,cAAc;;AAEvB,SAAS,oBAAoB;AAC7B,SAAS,mBAAmB;AAC5B;AAcA;AAaA;AAKA,SAAS,4BAA4B;;;;;;;;;;AAwF9B,SAAS,YAGd,QAAA,EACA,OAAA,EACA,MAAA,EAIkC;IAClC,MAAM,QAAQ,QAAQ,KAAA,IAAS,QAAQ,IAAA;IACvC,MAAM,aAAa,mBAAmB,OAAO;IAC7C,MAAM,uPAAM,eAAA,EACV,UACA;QACE,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa;QACb,6OAAa,wBAAA;QACb,8OAAc,yBAAA;QACd,UAAU;YACR,OAAO;gBACL;gBACA,eAAe,QAAQ,YAAA,oPACnB,eAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;gBACJ,UAAU,QAAQ,QAAA;gBAClB,UAAU,QAAQ,QAAA;YACpB;QACF;QACA,KAAK;IACP,GACA,CAAC,OAAO,QAAQ;QACd,MAAM,4IAAc,cAAA,CAAY,GAAA,CAAI;QACpC,MAAM,cACJ,QAAQ,UAAA,KAAe,OACnB,uPACA,uBAAA,EAAqB,QAAQ,KAC5B,IAAI,kBAAA,IAAsB,IAAI,SAAA,IAC/B,KAAA;QACN,OAAO,OAAO,OAAO,WAAW,EAAE,IAAA,CAAK,CAAC,aAAa;YACnD,MAAM,gBAAgB;gBACpB,GAAG,QAAA;gBACH,yIAAW,cAAA,CAAY,GAAA,CAAI,IAAI;YACjC;YACA,OAAO;QACT,CAAC;IACH;IAEF,OAAO,MAAA,CAAO,KAAK;QACjB,gBAAgB,QAAQ,YAAA,yNAAgB,IAAA,CAAE,OAAA,CAAQ;IACpD,CAAC;IACD,OAAO;AACT;AAmBO,SAAS,sBAGd,QAAA,EACA,OAAA,EAC4C;IAC5C,MAAM,QAAQ,QAAQ,KAAA,IAAS,QAAQ,IAAA;IACvC,MAAM,aAAa,mBAAmB,OAAO;IAC7C,MAAM,oPAAM,yBAAA,EAAuB,UAAU;QAC3C,YAAY;QACZ,MAAM,QAAQ,IAAA;QACd,aAAa;QACb,6OAAa,wBAAA;QACb,8OAAc,yBAAA;QACd,UAAU;YACR,OAAO;gBACL;gBACA,eAAe,QAAQ,YAAA,oPACnB,eAAA,EAAa;oBAAE,QAAQ,QAAQ,YAAA;gBAAa,CAAC,IAC7C,KAAA;gBACJ,UAAU,QAAQ,QAAA;gBAClB,UAAU,QAAQ,QAAA;YACpB;QACF;QACA,KAAK;QACL,MAAM,OAAM,OAAA,EAAS;YACnB,MAAM,4IAAc,cAAA,CAAY,GAAA,CAAI;YACpC,MAAM,WAAW,MAAM,QAAQ,KAAA,CAAM,OAAO;YAC5C,OAAO,MAAA,CAAO,UAAU;gBACtB,yIAAW,cAAA,CAAY,GAAA,CAAI,IAAI;YACjC,CAAC;YACD,OAAO;QACT;QACA,MAAM,OAAM,EAAA,EAAI;YACd,OAAO,QAAQ,KAAA,CAAM,EAAE;QACzB;QACA,QAAQ,QAAQ,MAAA,GACZ,OAAO,OAAO;YACZ,IAAI,CAAC,QAAQ,MAAA,EAAQ;gBACnB,MAAM,+NAAI,cAAA,CAAY;oBACpB,QAAQ;oBACR,SAAS;gBACX,CAAC;YACH;YACA,OAAO,QAAQ,MAAA,CAAO,EAAE;QAC1B,IACA,KAAA;IACN,CAAC;IACD,OAAO,MAAA,CAAO,KAAK;QACjB,gBAAgB,QAAQ,YAAA,yNAAgB,IAAA,CAAE,OAAA,CAAQ;IACpD,CAAC;IACD,OAAO;AACT;AAEA,SAAS,mBAAmB,OAAA,EAIzB;IACD,MAAM,aAAgC;WAChC,QAAQ,GAAA,IAAO,CAAC,CAAA;SACpB,0PAAA,EAAgB,OAAO;KACzB;IACA,IAAI,CAAC,SAAS,UAAU,QAAS,CAAA,WAAW,IAAA,0OAAK,qBAAA,CAAmB,CAAC;IACrE,MAAM,0BAAsB,qQAAA,CAA8B;IAC1D,WAAW,IAAA,CAAK,CAAC,KAAK,SAAS;QAC7B,IACE,CAAC,SAAS,UAAU,eACpB,SAAS,UAAU,gBAAgB,UAClC,SAAS,UAAU,gBAAgB,cAAA,CACjC,IAAI,KAAA,EAAO,UAAU,CAAA,IAAK,GAC7B;YACA,OAAO,oBAAoB,KAAK,IAAI;QACtC;QACA,OAAO,KAAK,GAAG;IACjB,CAAC;IAED,OAAO;AACT;AAgBO,SAAS,oBAAoB,EAClC,IAAA,EACA,IAAA,EACA,YAAA,EACA,UAAA,EACF,EAKmB;IACjB,OAAO;QACL,YAAY,aAAa,qBAAqB;QAC9C;QACA,kQAAiB,eAAA,EAAa;YAAE,wOAAQ,wBAAA;QAAsB,CAAC;QAC/D,kBAAkB,8PACd,eAAA,EAAa;YAAE,QAAQ,4PAAA;QAAgB,CAAC,qPACxC,eAAA,EAAa;YAAE,wOAAQ,yBAAA;QAAuB,CAAC;QACnD,UAAU;YACR,OAAO;gBACL,GAAG,IAAA;gBACH,eAAe,gQACX,eAAA,EAAa;oBAAE,QAAQ;gBAAa,CAAC,IACrC,KAAA;YACN;QACF;IACF;AACF;AAGO,SAAS,SAGd,OAAA,EAIqC;IACrC,MAAM,MAAoD;QAAE,GAAG,OAAA;IAAQ;IACvE,IAAI,UAAA,GAAa,CACf,QACwC;QACxC,OAAO,SAAS;YACd,GAAG,OAAA;YACH,QAAQ;QACV,CAAC;IACH;IACA,IAAI,WAAA,GAAc,CAAC,YAAyD;QAC1E,OAAO,SAAS;YACd,GAAG,OAAA;YACH;QACF,CAAC;IACH;IACA,OAAO;AACT;AAaO,SAAS,mBACd,KAAA,EACA,QAAA,EACiB;IACjB,MAAM,cAAc,cAAc,MAAM,OAAA,CAAQ,CAAC,KAAO,GAAG,OAAO,CAAC;IACnE,MAAM,eAAe,cACnB,MAAM,OAAA,CAAQ,QAAQ,IAClB,SAAS,OAAA,CAAQ,CAAC,IAAM,EAAE,OAAA,CAAQ,OAAO,IACzC,SAAS,OAAA;IAEf,OAAO;QACL,iBAAiB,YAAY,UAAA;QAC7B,aAAa,YAAY,MAAA;QACzB,aAAa,YAAY,MAAA;QACzB,iBAAiB,YAAY,KAAA;QAC7B,kBAAkB,aAAa,UAAA;QAC/B,cAAc,aAAa,MAAA;QAC3B,cAAc,aAAa,MAAA;QAC3B,kBAAkB,aAAa,KAAA;IACjC;AACF;AAEA,SAAS,cAAc,KAAA,EAA2B;IAChD,OAAO,MAAM,MAAA,CACX,CAAC,QAAQ,SAAS;QAChB,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,MAAM,UACJ,KAAK,KAAA,EAAO,aAAa,WAAW,OAAO,KAC3C,KAAK,KAAA,EAAO,KAAK,WAAW,YAAY;QAC1C,OAAO;YACL,YAAY,OAAO,UAAA,GAAA,CAAc,KAAK,IAAA,EAAM,UAAU,CAAA;YACtD,QAAQ,OAAO,MAAA,GAAA,CAAU,UAAU,IAAI,CAAA;YACvC,QAAQ,OAAO,MAAA,GAAA,CAAU,UAAU,IAAI,CAAA;YACvC,OAAO,OAAO,KAAA,GAAA,CAAS,UAAU,IAAI,CAAA;QACvC;IACF,GACA;QAAE,YAAY;QAAG,QAAQ;QAAG,QAAQ;QAAG,OAAO;IAAE;AAEpD;AAeA,eAAsB,aACpB,QAAA,EACA,KAAA,EACA,OAAA,EAC2B;IAC3B,IAAI;IACJ,IAAI;IAEJ,IAAI,CAAC,OAAO;QACV,QAAQ,MAAM,SAAS,WAAA,CAAY,gBAAgB,cAAc;IACnE;IACA,IAAI,CAAC,OAAO;QACV,MAAM,+NAAI,cAAA,CAAY;YACpB,QAAQ;YACR,SAAS;QACX,CAAC;IACH;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,UAAU;QACV,MAAM;YAAE,aAAa,MAAM,YAAY,UAAU,KAAK;QAAE;IAC1D,OAAA,IAAW,MAAM,cAAA,CAAe,UAAU,GAAG;QAC3C,UAAW,MAAsB,QAAA,CAAS,IAAA;QAC1C,MAAM;YAAE,aAAa;QAAqB;IAC5C,OAAO;QACL,MAAM,MAAM;QACZ,UAAU,IAAI,IAAA;QACd,MAAM;YACJ,aAAa,MAAM,YAAY,UAAU,IAAI,IAAI;YACjD,QAAQ;gBACN,GAAG,IAAI,MAAA;YACT;YACA,SAAS,IAAI,OAAA;QACf;IACF;IAEA,IAAI,CAAC,IAAI,WAAA,EAAa;QACpB,MAAM,+NAAI,cAAA,CAAY;YACpB,QAAQ;YACR,SAAS,CAAA,OAAA,EAAU,OAAO,CAAA,WAAA,CAAA;QAC5B,CAAC;IACH;IAEA,IACE,SAAS,kBACT,IAAI,WAAA,CAAY,QAAA,CAAS,QAAA,EAAU,OAAO,UAAU,cACpD;QACA,6NAAA,CAAA,SAAA,CAAO,IAAA,CACL,CAAA,OAAA,EAAU,IAAI,WAAA,CAAY,QAAA,CAAS,IAAI,CAAA,uDAAA,CAAA;IAE3C;IAEA,OAAO;AACT;AAEA,eAAe,YACb,QAAA,EACA,KAAA,EACsB;IACtB,OACG,MAAM,SAAS,YAAA,CAAa,CAAA,OAAA,EAAU,KAAK,EAAE,KAC7C,MAAM,SAAS,YAAA,CAAa,CAAA,kBAAA,EAAqB,KAAK,EAAE;AAE7D", "ignoreList": [0], "debugId": null}}]}