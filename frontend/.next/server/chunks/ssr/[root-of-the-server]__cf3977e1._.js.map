{"version": 3, "sources": [], "sections": [{"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\nimport {googleAI} from '@genkit-ai/googleai';\n\nexport const ai = genkit({\n  plugins: [googleAI()],\n  model: 'googleai/gemini-2.0-flash',\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,2LAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,0QAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/ai/flows/writing-improvement-suggestions.ts"], "sourcesContent": ["'use server';\n\n/**\n * @fileOverview A writing improvement suggestion AI agent.\n *\n * - writingImprovementSuggestions - A function that handles the writing improvement process.\n * - WritingImprovementSuggestionsInput - The input type for the writingImprovementSuggestions function.\n * - WritingImprovementSuggestionsOutput - The return type for the writingImprovementSuggestions function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst WritingImprovementSuggestionsInputSchema = z.object({\n  text: z\n    .string()\n    .describe('The text to analyze and provide improvement suggestions for.'),\n});\nexport type WritingImprovementSuggestionsInput = z.infer<\n  typeof WritingImprovementSuggestionsInputSchema\n>;\n\nconst WritingImprovementSuggestionsOutputSchema = z.object({\n  suggestions: z\n    .string()\n    .describe(\n      'A comprehensive report that highlights commonly used words, average sentence length, and peak writing times to aid self-assessment, along with suggestions for improvement in vocabulary, sentence structure, and pacing.'\n    ),\n});\nexport type WritingImprovementSuggestionsOutput = z.infer<\n  typeof WritingImprovementSuggestionsOutputSchema\n>;\n\nexport async function writingImprovementSuggestions(\n  input: WritingImprovementSuggestionsInput\n): Promise<WritingImprovementSuggestionsOutput> {\n  return writingImprovementSuggestionsFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'writingImprovementSuggestionsPrompt',\n  input: {schema: WritingImprovementSuggestionsInputSchema},\n  output: {schema: WritingImprovementSuggestionsOutputSchema},\n  prompt: `You are an AI writing assistant that analyzes text and provides suggestions for improvement.\n\nAnalyze the following text and provide a comprehensive report that highlights commonly used words, average sentence length, and writing style to aid self-assessment. Also, give suggestions for improvement in vocabulary, sentence structure, and pacing.\n\nText: {{{text}}}`,\n});\n\nconst writingImprovementSuggestionsFlow = ai.defineFlow(\n  {\n    name: 'writingImprovementSuggestionsFlow',\n    inputSchema: WritingImprovementSuggestionsInputSchema,\n    outputSchema: WritingImprovementSuggestionsOutputSchema,\n  },\n  async input => {\n    const {output} = await prompt(input);\n    return output!;\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,2CAA2C,2LAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxD,MAAM,2LAAA,CAAA,IAAC,CACJ,MAAM,GACN,QAAQ,CAAC;AACd;AAKA,MAAM,4CAA4C,2LAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzD,aAAa,2LAAA,CAAA,IAAC,CACX,MAAM,GACN,QAAQ,CACP;AAEN;AAKO,eAAe,8BACpB,KAAyC;IAEzC,OAAO,kCAAkC;AAC3C;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAwC;IACxD,QAAQ;QAAC,QAAQ;IAAyC;IAC1D,QAAQ,CAAC;;;;gBAIK,CAAC;AACjB;AAEA,MAAM,oCAAoC,mHAAA,CAAA,KAAE,CAAC,UAAU,CACrD;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAM;IACJ,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,OAAO;AACT;;;IA1BoB;;AAAA,sZAAA", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/ai/flows/character-generator.ts"], "sourcesContent": ["\n'use server';\n\n/**\n * @fileOverview Character generator AI agent.\n *\n * - generateCharacter - A function that handles character generation.\n * - GenerateCharacterOutput - The return type for the generateCharacter function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst GenerateCharacterOutputSchema = z.object({\n  name: z.string().describe('The full name of the character.'),\n  age: z.string().describe('The age of the character.'),\n  appearance: z.string().describe(\"A description of the character's physical appearance.\"),\n  personality: z.string().describe(\"A description of the character's personality traits and mannerisms.\"),\n  background: z.string().describe(\"A summary of the character's backstory and history.\"),\n});\nexport type GenerateCharacterOutput = z.infer<typeof GenerateCharacterOutputSchema>;\n\nexport async function generateCharacter(): Promise<GenerateCharacterOutput> {\n  return generateCharacterFlow();\n}\n\nconst prompt = ai.definePrompt({\n  name: 'generateCharacterPrompt',\n  output: {schema: GenerateCharacterOutputSchema},\n  prompt: `You are an expert character designer for novels. Generate a unique and interesting character for a fantasy novel. \n  \n  Provide a name, age, appearance, personality, and background for the character. The character should feel realistic and have depth.\n  \n  Do not create characters that are overly stereotypical. Give them some unique quirks or flaws.`,\n});\n\nconst generateCharacterFlow = ai.defineFlow(\n  {\n    name: 'generateCharacterFlow',\n    outputSchema: GenerateCharacterOutputSchema,\n  },\n  async () => {\n    const {output} = await prompt();\n    return output!;\n  }\n);\n"], "names": [], "mappings": ";;;;;AAGA;;;;;CAKC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,gCAAgC,2LAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,KAAK,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACzB,YAAY,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,aAAa,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,YAAY,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAClC;AAGO,eAAe;IACpB,OAAO;AACT;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,QAAQ;QAAC,QAAQ;IAA6B;IAC9C,QAAQ,CAAC;;;;gGAIqF,CAAC;AACjG;AAEA,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,cAAc;AAChB,GACA;IACE,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IACvB,OAAO;AACT;;;IAtBoB;;AAAA,sZAAA", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/ai/flows/novel-outline-generator.ts"], "sourcesContent": ["'use server';\n\n/**\n * @fileOverview Novel outline generator AI agent.\n *\n * - generateNovelOutline - A function that handles the novel outline generation process.\n * - GenerateNovelOutlineInput - The input type for the generateNovelOutline function.\n * - GenerateNovelOutlineOutput - The return type for the generateNovelOutline function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst GenerateNovelOutlineInputSchema = z.object({\n  prompt: z.string().describe('A brief prompt describing the novel plot.'),\n});\nexport type GenerateNovelOutlineInput = z.infer<typeof GenerateNovelOutlineInputSchema>;\n\nconst GenerateNovelOutlineOutputSchema = z.object({\n  outline: z.string().describe('A novel outline with chapters and scenes.'),\n});\nexport type GenerateNovelOutlineOutput = z.infer<typeof GenerateNovelOutlineOutputSchema>;\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<GenerateNovelOutlineOutput> {\n  return generateNovelOutlineFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'generateNovelOutlinePrompt',\n  input: {schema: GenerateNovelOutlineInputSchema},\n  output: {schema: GenerateNovelOutlineOutputSchema},\n  prompt: `You are a novel writing expert. Generate a detailed novel outline with chapters and scenes based on the following prompt:\\n\\nPrompt: {{{prompt}}}`,\n});\n\nconst generateNovelOutlineFlow = ai.defineFlow(\n  {\n    name: 'generateNovelOutlineFlow',\n    inputSchema: GenerateNovelOutlineInputSchema,\n    outputSchema: GenerateNovelOutlineOutputSchema,\n  },\n  async input => {\n    const {output} = await prompt(input);\n    return output!;\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,kCAAkC,2LAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,QAAQ,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC9B;AAGA,MAAM,mCAAmC,2LAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,SAAS,2LAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC/B;AAGO,eAAe,qBAAqB,KAAgC;IACzE,OAAO,yBAAyB;AAClC;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAA+B;IAC/C,QAAQ;QAAC,QAAQ;IAAgC;IACjD,QAAQ,CAAC,iJAAiJ,CAAC;AAC7J;AAEA,MAAM,2BAA2B,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC5C;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAM;IACJ,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,OAAO;AACT;;;IApBoB;;AAAA,sZAAA", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\n\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\nlet userIsLoggedIn = false;\nlet userProjects: Project[] = [];\n\nexport async function login(data: any) {\n  console.log(\"Login attempt:\", data);\n  // In a real app, you'd verify credentials against a database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: '<EMAIL>' } };\n}\n\nexport async function logout() {\n  userIsLoggedIn = false;\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  console.log(\"Registration attempt:\", data);\n  // In a real app, you'd create a new user in the database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: data.email } };\n}\n\nexport async function getAuthState() {\n    return { isLoggedIn: userIsLoggedIn };\n}\n\nexport async function saveProject(project: Project) {\n  if (!userIsLoggedIn) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n  // In a real app, you'd save this to a database like Firestore.\n  console.log(\"Saving project:\", project.name);\n  const existingIndex = userProjects.findIndex(p => p.id === project.id);\n  if (existingIndex > -1) {\n    userProjects[existingIndex] = project;\n  } else {\n    userProjects.push(project);\n  }\n  return { success: true, message: \"Project saved successfully!\" };\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const result = await writingImprovementSuggestions(input);\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(error);\n    return { success: false, error: \"Failed to get analysis. Please try again.\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const character = await genChar();\n        return { success: true, character };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate character. Please try again.\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const outline = await genNovelOutline(input);\n        return { success: true, outline };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate novel outline. Please try again.\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AACA;;;;;;;AAGA,8BAA8B;AAC9B,IAAI,iBAAiB;AACrB,IAAI,eAA0B,EAAE;AAEzB,eAAe,MAAM,IAAS;IACnC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,8DAA8D;IAC9D,iBAAiB;IACjB,OAAO;QAAE,SAAS;QAAM,MAAM;YAAE,MAAM;YAAa,OAAO;QAAmB;IAAE;AACjF;AAEO,eAAe;IACpB,iBAAiB;IACjB,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,eAAe,SAAS,IAAS;IACtC,QAAQ,GAAG,CAAC,yBAAyB;IACrC,0DAA0D;IAC1D,iBAAiB;IACjB,OAAO;QAAE,SAAS;QAAM,MAAM;YAAE,MAAM;YAAa,OAAO,KAAK,KAAK;QAAC;IAAE;AACzE;AAEO,eAAe;IAClB,OAAO;QAAE,YAAY;IAAe;AACxC;AAEO,eAAe,YAAY,OAAgB;IAChD,IAAI,CAAC,gBAAgB;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IACA,+DAA+D;IAC/D,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,IAAI;IAC3C,MAAM,gBAAgB,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;IACrE,IAAI,gBAAgB,CAAC,GAAG;QACtB,YAAY,CAAC,cAAc,GAAG;IAChC,OAAO;QACL,aAAa,IAAI,CAAC;IACpB;IACA,OAAO;QAAE,SAAS;QAAM,SAAS;IAA8B;AACjE;AAGO,eAAe,mBAAmB,KAAyC;IAKhF,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,gCAA6B,AAAD,EAAE;QACnD,OAAO;YAAE,SAAS;YAAM,aAAa,OAAO,WAAW;QAAC;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;AACF;AAGO,eAAe;IAKlB,IAAI;QACA,MAAM,YAAY,MAAM,CAAA,GAAA,4IAAA,CAAA,oBAAO,AAAD;QAC9B,OAAO;YAAE,SAAS;YAAM;QAAU;IACtC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkD;IACtF;AACJ;AAEO,eAAe,qBAAqB,KAAgC;IAKvE,IAAI;QACA,MAAM,UAAU,MAAM,CAAA,GAAA,mJAAA,CAAA,uBAAe,AAAD,EAAE;QACtC,OAAO;YAAE,SAAS;YAAM;QAAQ;IACpC,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsD;IAC1F;AACJ;;;IAhFsB;IAOA;IAKA;IAOA;IAIA;IAgBA;IAeA;IAcA;;AApEA,sZAAA;AAOA,sZAAA;AAKA,sZAAA;AAOA,sZAAA;AAIA,sZAAA;AAgBA,sZAAA;AAeA,sZAAA;AAcA,sZAAA", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/.next-internal/server/app/login/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getAuthState as '006c95642497d0ef7b443912f13eb64d1d84fe07ff'} from 'ACTIONS_MODULE0'\nexport {login as '40145d5f48e9b2cd3ad23766b6412323fb6073c40e'} from 'ACTIONS_MODULE0'\nexport {logout as '004ae6244db5a42653133b3c5adbe531b9296f9472'} from 'ACTIONS_MODULE0'\nexport {register as '40609dfbd242bb3d3cc09f0325f2192c5f2939a0c8'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/login/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/login/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/login/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,4ZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/login/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/login/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/login/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,4ZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsQ,GACnS,oCACA", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}