{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/build/webpack/loaders/next-flight-loader/server-reference.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nexport { registerServerReference } from 'react-server-dom-webpack/server.edge'\n"], "names": ["registerServerReference"], "mappings": "AAAA,oDAAoD,GAAA;;;;+BAC3CA,2BAAAA;;;eAAAA,YAAAA,uBAAuB;;;4BAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/lib/trace/constants.ts"], "sourcesContent": ["/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n"], "names": ["AppRenderSpan", "AppRouteRouteHandlersSpan", "BaseServerSpan", "LoadComponentsSpan", "LogSpanAllowList", "MiddlewareSpan", "NextNodeServerSpan", "NextServerSpan", "NextVanillaSpanAllowlist", "NodeSpan", "RenderSpan", "ResolveMetadataSpan", "RouterSpan", "StartServerSpan"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0J1BA,aAAa,EAAA;eAAbA;;IAEAC,yBAAyB,EAAA;eAAzBA;;IATAC,cAAc,EAAA;eAAdA;;IACAC,kBAAkB,EAAA;eAAlBA;;IARWC,gBAAgB,EAAA;eAAhBA;;IAkBXC,cAAc,EAAA;eAAdA;;IARAC,kBAAkB,EAAA;eAAlBA;;IADAC,cAAc,EAAA;eAAdA;;IA9BWC,wBAAwB,EAAA;eAAxBA;;IAoCXC,QAAQ,EAAA;eAARA;;IAHAC,UAAU,EAAA;eAAVA;;IAKAC,mBAAmB,EAAA;eAAnBA;;IAJAC,UAAU,EAAA;eAAVA;;IAFAC,eAAe,EAAA;eAAfA;;;AArJF,IAAKX,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;;;;;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAeL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;WAAAA;EAAAA,sBAAAA,CAAAA;AAKL,IAAKI,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAOL,IAAKD,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BH,wDAAwD;;;;;WA5BrDA;EAAAA,sBAAAA,CAAAA;AAmCL,IAAKO,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;WAAAA;EAAAA,mBAAAA,CAAAA;AAIL,IAAKH,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;;;;;WAAAA;EAAAA,cAAAA,CAAAA;AAQL,IAAKV,gBAAAA,WAAAA,GAAAA,SAAAA,aAAAA;;;;;WAAAA;EAAAA,iBAAAA,CAAAA;AAOL,IAAKY,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;WAAAA;EAAAA,cAAAA,CAAAA;AAIL,IAAKH,WAAAA,WAAAA,GAAAA,SAAAA,QAAAA;;WAAAA;EAAAA,YAAAA,CAAAA;AAIL,IAAKR,4BAAAA,WAAAA,GAAAA,SAAAA,yBAAAA;;WAAAA;EAAAA,6BAAAA,CAAAA;AAIL,IAAKU,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;WAAAA;EAAAA,uBAAAA,CAAAA;AAKL,IAAKN,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;WAAAA;EAAAA,kBAAAA,CAAAA;AAmBE,MAAMG,2BAA2B;;;;;;;;;;;;;;;;;CAiBvC;AAIM,MAAMJ,mBAAmB;;;;CAI/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+BACeA,cAAAA;;;eAAAA;;;AAAT,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/lib/trace/tracer.ts"], "sourcesContent": ["import type { FetchEventResult } from '../../web/types'\nimport type { TextMapSetter } from '@opentelemetry/api'\nimport type { SpanTypes } from './constants'\nimport { LogSpanAllowList, NextVanillaSpanAllowlist } from './constants'\n\nimport type {\n  ContextAPI,\n  Span,\n  SpanOptions,\n  Tracer,\n  AttributeValue,\n  TextMapGetter,\n} from 'next/dist/compiled/@opentelemetry/api'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\nlet api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n  api = require('@opentelemetry/api')\n} else {\n  try {\n    api = require('@opentelemetry/api')\n  } catch (err) {\n    api = require('next/dist/compiled/@opentelemetry/api')\n  }\n}\n\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } =\n  api\n\nexport class BubbledError extends Error {\n  constructor(\n    public readonly bubble?: boolean,\n    public readonly result?: FetchEventResult\n  ) {\n    super()\n  }\n}\n\nexport function isBubbledError(error: unknown): error is BubbledError {\n  if (typeof error !== 'object' || error === null) return false\n  return error instanceof BubbledError\n}\n\nconst closeSpanWithError = (span: Span, error?: Error) => {\n  if (isBubbledError(error) && error.bubble) {\n    span.setAttribute('next.bubble', true)\n  } else {\n    if (error) {\n      span.recordException(error)\n    }\n    span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message })\n  }\n  span.end()\n}\n\ntype TracerSpanOptions = Omit<SpanOptions, 'attributes'> & {\n  parentSpan?: Span\n  spanName?: string\n  attributes?: Partial<Record<AttributeNames, AttributeValue | undefined>>\n  hideSpan?: boolean\n}\n\ninterface NextTracer {\n  getContext(): ContextAPI\n\n  /**\n   * Instruments a function by automatically creating a span activated on its\n   * scope.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its second parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   *\n   */\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n\n  /**\n   * Wrap a function to automatically create a span activated on its\n   * scope when it's called.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its last parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   */\n  wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n\n  /**\n   * Starts and returns a new Span representing a logical unit of work.\n   *\n   * This method do NOT modify the current Context by default. In result, any inner span will not\n   * automatically set its parent context to the span created by this method unless manually activate\n   * context via `tracer.getContext().with`. `trace`, or `wrap` is generally recommended as it gracefully\n   * handles context activation. (ref: https://github.com/open-telemetry/opentelemetry-js/issues/1923)\n   */\n  startSpan(type: SpanTypes): Span\n  startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n\n  /**\n   * Returns currently activated span if current context is in the scope of the span.\n   * Returns undefined otherwise.\n   */\n  getActiveScopeSpan(): Span | undefined\n\n  /**\n   * Returns trace propagation data for the currently active context. The format is equal to data provided\n   * through the OpenTelemetry propagator API.\n   */\n  getTracePropagationData(): ClientTraceDataEntry[]\n}\n\ntype NextAttributeNames =\n  | 'next.route'\n  | 'next.page'\n  | 'next.rsc'\n  | 'next.segment'\n  | 'next.span_name'\n  | 'next.span_type'\n  | 'next.clientComponentLoadCount'\ntype OTELAttributeNames = `http.${string}` | `net.${string}`\ntype AttributeNames = NextAttributeNames | OTELAttributeNames\n\n/** we use this map to propagate attributes from nested spans to the top span */\nconst rootSpanAttributesStore = new Map<\n  number,\n  Map<AttributeNames, AttributeValue | undefined>\n>()\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId')\nlet lastSpanId = 0\nconst getSpanId = () => lastSpanId++\n\nexport interface ClientTraceDataEntry {\n  key: string\n  value: string\n}\n\nconst clientTraceDataSetter: TextMapSetter<ClientTraceDataEntry[]> = {\n  set(carrier, key, value) {\n    carrier.push({\n      key,\n      value,\n    })\n  },\n}\n\nclass NextTracerImpl implements NextTracer {\n  /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */\n  private getTracerInstance(): Tracer {\n    return trace.getTracer('next.js', '0.0.1')\n  }\n\n  public getContext(): ContextAPI {\n    return context\n  }\n\n  public getTracePropagationData(): ClientTraceDataEntry[] {\n    const activeContext = context.active()\n    const entries: ClientTraceDataEntry[] = []\n    propagation.inject(activeContext, entries, clientTraceDataSetter)\n    return entries\n  }\n\n  public getActiveScopeSpan(): Span | undefined {\n    return trace.getSpan(context?.active())\n  }\n\n  public withPropagatedContext<T, C>(\n    carrier: C,\n    fn: () => T,\n    getter?: TextMapGetter<C>\n  ): T {\n    const activeContext = context.active()\n    if (trace.getSpanContext(activeContext)) {\n      // Active span is already set, too late to propagate.\n      return fn()\n    }\n    const remoteContext = propagation.extract(activeContext, carrier, getter)\n    return context.with(remoteContext, fn)\n  }\n\n  // Trace, wrap implementation is inspired by datadog trace implementation\n  // (https://datadoghq.dev/dd-trace-js/interfaces/tracer.html#trace).\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(...args: Array<any>) {\n    const [type, fnOrOptions, fnOrEmpty] = args\n\n    // coerce options form overload\n    const {\n      fn,\n      options,\n    }: {\n      fn: (span?: Span, done?: (error?: Error) => any) => T | Promise<T>\n      options: TracerSpanOptions\n    } =\n      typeof fnOrOptions === 'function'\n        ? {\n            fn: fnOrOptions,\n            options: {},\n          }\n        : {\n            fn: fnOrEmpty,\n            options: { ...fnOrOptions },\n          }\n\n    const spanName = options.spanName ?? type\n\n    if (\n      (!NextVanillaSpanAllowlist.includes(type) &&\n        process.env.NEXT_OTEL_VERBOSE !== '1') ||\n      options.hideSpan\n    ) {\n      return fn()\n    }\n\n    // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n    let spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    let isRootSpan = false\n\n    if (!spanContext) {\n      spanContext = context?.active() ?? ROOT_CONTEXT\n      isRootSpan = true\n    } else if (trace.getSpanContext(spanContext)?.isRemote) {\n      isRootSpan = true\n    }\n\n    const spanId = getSpanId()\n\n    options.attributes = {\n      'next.span_name': spanName,\n      'next.span_type': type,\n      ...options.attributes,\n    }\n\n    return context.with(spanContext.setValue(rootSpanIdKey, spanId), () =>\n      this.getTracerInstance().startActiveSpan(\n        spanName,\n        options,\n        (span: Span) => {\n          const startTime =\n            'performance' in globalThis && 'measure' in performance\n              ? globalThis.performance.now()\n              : undefined\n\n          const onCleanup = () => {\n            rootSpanAttributesStore.delete(spanId)\n            if (\n              startTime &&\n              process.env.NEXT_OTEL_PERFORMANCE_PREFIX &&\n              LogSpanAllowList.includes(type || ('' as any))\n            ) {\n              performance.measure(\n                `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(\n                  type.split('.').pop() || ''\n                ).replace(\n                  /[A-Z]/g,\n                  (match: string) => '-' + match.toLowerCase()\n                )}`,\n                {\n                  start: startTime,\n                  end: performance.now(),\n                }\n              )\n            }\n          }\n\n          if (isRootSpan) {\n            rootSpanAttributesStore.set(\n              spanId,\n              new Map(\n                Object.entries(options.attributes ?? {}) as [\n                  AttributeNames,\n                  AttributeValue | undefined,\n                ][]\n              )\n            )\n          }\n          try {\n            if (fn.length > 1) {\n              return fn(span, (err) => closeSpanWithError(span, err))\n            }\n\n            const result = fn(span)\n            if (isThenable(result)) {\n              // If there's error make sure it throws\n              return result\n                .then((res) => {\n                  span.end()\n                  // Need to pass down the promise result,\n                  // it could be react stream response with error { error, stream }\n                  return res\n                })\n                .catch((err) => {\n                  closeSpanWithError(span, err)\n                  throw err\n                })\n                .finally(onCleanup)\n            } else {\n              span.end()\n              onCleanup()\n            }\n\n            return result\n          } catch (err: any) {\n            closeSpanWithError(span, err)\n            onCleanup()\n            throw err\n          }\n        }\n      )\n    )\n  }\n\n  public wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap(...args: Array<any>) {\n    const tracer = this\n    const [name, options, fn] =\n      args.length === 3 ? args : [args[0], {}, args[1]]\n\n    if (\n      !NextVanillaSpanAllowlist.includes(name) &&\n      process.env.NEXT_OTEL_VERBOSE !== '1'\n    ) {\n      return fn\n    }\n\n    return function (this: any) {\n      let optionsObj = options\n      if (typeof optionsObj === 'function' && typeof fn === 'function') {\n        optionsObj = optionsObj.apply(this, arguments)\n      }\n\n      const lastArgId = arguments.length - 1\n      const cb = arguments[lastArgId]\n\n      if (typeof cb === 'function') {\n        const scopeBoundCb = tracer.getContext().bind(context.active(), cb)\n        return tracer.trace(name, optionsObj, (_span, done) => {\n          arguments[lastArgId] = function (err: any) {\n            done?.(err)\n            return scopeBoundCb.apply(this, arguments)\n          }\n\n          return fn.apply(this, arguments)\n        })\n      } else {\n        return tracer.trace(name, optionsObj, () => fn.apply(this, arguments))\n      }\n    }\n  }\n\n  public startSpan(type: SpanTypes): Span\n  public startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n  public startSpan(...args: Array<any>): Span {\n    const [type, options]: [string, TracerSpanOptions | undefined] = args as any\n\n    const spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    return this.getTracerInstance().startSpan(type, options, spanContext)\n  }\n\n  private getSpanContext(parentSpan?: Span) {\n    const spanContext = parentSpan\n      ? trace.setSpan(context.active(), parentSpan)\n      : undefined\n\n    return spanContext\n  }\n\n  public getRootSpanAttributes() {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    return rootSpanAttributesStore.get(spanId)\n  }\n\n  public setRootSpanAttribute(key: AttributeNames, value: AttributeValue) {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    const attributes = rootSpanAttributesStore.get(spanId)\n    if (attributes) {\n      attributes.set(key, value)\n    }\n  }\n}\n\nconst getTracer = (() => {\n  const tracer = new NextTracerImpl()\n\n  return () => tracer\n})()\n\nexport { getTracer, SpanStatusCode, SpanKind }\nexport type { NextTracer, Span, SpanOptions, ContextAPI, TracerSpanOptions }\n"], "names": ["BubbledError", "SpanKind", "SpanStatusCode", "getTracer", "isBubbledError", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "Error", "constructor", "bubble", "result", "error", "closeSpanWithError", "span", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "clientTraceDataSetter", "set", "carrier", "key", "value", "push", "NextTracerImpl", "getTracerInstance", "getContext", "getTracePropagationData", "activeContext", "active", "entries", "inject", "getActiveScopeSpan", "getSpan", "withPropagatedContext", "fn", "getter", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "LogSpanAllowList", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "Object", "length", "isThenable", "then", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get", "setRootSpanAttribute"], "mappings": ";;;;;;;;;;;;;;;;;;IAqCaA,YAAY,EAAA;eAAZA;;IA+auBC,QAAQ,EAAA;eAARA;;IAAhBC,cAAc,EAAA;eAAdA;;IAAXC,SAAS,EAAA;eAATA;;IAtaOC,cAAc,EAAA;eAAdA;;;2BA3C2C;4BAUhC;AAE3B,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAI;QACFH,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEX,cAAc,EAAED,QAAQ,EAAEa,YAAY,EAAE,GAC3ET;AAEK,MAAML,qBAAqBe;IAChCC,YACkBC,MAAgB,EAChBC,MAAyB,CACzC;QACA,KAAK,IAAA,IAAA,CAHWD,MAAAA,GAAAA,QAAAA,IAAAA,CACAC,MAAAA,GAAAA;IAGlB;AACF;AAEO,SAASd,eAAee,KAAc;IAC3C,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM,OAAO;IACxD,OAAOA,iBAAiBnB;AAC1B;AAEA,MAAMoB,qBAAqB,CAACC,MAAYF;IACtC,IAAIf,eAAee,UAAUA,MAAMF,MAAM,EAAE;QACzCI,KAAKC,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIH,OAAO;YACTE,KAAKE,eAAe,CAACJ;QACvB;QACAE,KAAKG,SAAS,CAAC;YAAEC,MAAMvB,eAAewB,KAAK;YAAEC,OAAO,EAAER,SAAAA,OAAAA,KAAAA,IAAAA,MAAOQ,OAAO;QAAC;IACvE;IACAN,KAAKO,GAAG;AACV;AA2GA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAOxB,MAAME,wBAA+D;IACnEC,KAAIC,OAAO,EAAEC,GAAG,EAAEC,KAAK;QACrBF,QAAQG,IAAI,CAAC;YACXF;YACAC;QACF;IACF;AACF;AAEA,MAAME;IACJ;;;;GAIC,GACOC,oBAA4B;QAClC,OAAO7B,MAAMV,SAAS,CAAC,WAAW;IACpC;IAEOwC,aAAyB;QAC9B,OAAOhC;IACT;IAEOiC,0BAAkD;QACvD,MAAMC,gBAAgBlC,QAAQmC,MAAM;QACpC,MAAMC,UAAkC,EAAE;QAC1CnC,YAAYoC,MAAM,CAACH,eAAeE,SAASZ;QAC3C,OAAOY;IACT;IAEOE,qBAAuC;QAC5C,OAAOpC,MAAMqC,OAAO,CAACvC,WAAAA,OAAAA,KAAAA,IAAAA,QAASmC,MAAM;IACtC;IAEOK,sBACLd,OAAU,EACVe,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMR,gBAAgBlC,QAAQmC,MAAM;QACpC,IAAIjC,MAAMyC,cAAc,CAACT,gBAAgB;YACvC,qDAAqD;YACrD,OAAOO;QACT;QACA,MAAMG,gBAAgB3C,YAAY4C,OAAO,CAACX,eAAeR,SAASgB;QAClE,OAAO1C,QAAQ8C,IAAI,CAACF,eAAeH;IACrC;IAsBOvC,MAAS,GAAG6C,IAAgB,EAAE;YAwCxB7C;QAvCX,MAAM,CAAC8C,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJN,EAAE,EACFU,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACER,IAAIQ;YACJE,SAAS,CAAC;QACZ,IACA;YACEV,IAAIS;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACG,CAACK,WAAAA,wBAAwB,CAACC,QAAQ,CAACN,SAClCrD,QAAQC,GAAG,CAAC2D,iBAAiB,KAAK,OACpCJ,QAAQK,QAAQ,EAChB;YACA,OAAOf;QACT;QAEA,mHAAmH;QACnH,IAAIgB,cAAc,IAAI,CAACd,cAAc,CACnCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASO,UAAU,KAAI,IAAI,CAACpB,kBAAkB;QAEhD,IAAIqB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAczD,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASmC,MAAM,EAAA,KAAMhC;YACnCwD,aAAa;QACf,OAAO,IAAA,CAAIzD,wBAAAA,MAAMyC,cAAc,CAACc,YAAAA,KAAAA,OAAAA,KAAAA,IAArBvD,sBAAmC0D,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAStC;QAEf4B,QAAQW,UAAU,GAAG;YACnB,kBAAkBV;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQW,UAAU;QACvB;QAEA,OAAO9D,QAAQ8C,IAAI,CAACW,YAAYM,QAAQ,CAAC3C,eAAeyC,SAAS,IAC/D,IAAI,CAAC9B,iBAAiB,GAAGiC,eAAe,CACtCZ,UACAD,SACA,CAACzC;gBACC,MAAMuD,YACJ,iBAAiBC,cAAc,aAAaC,cACxCD,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChBpD,wBAAwBqD,MAAM,CAACV;oBAC/B,IACEI,aACAtE,QAAQC,GAAG,CAAC4E,4BAA4B,IACxCC,WAAAA,gBAAgB,CAACnB,QAAQ,CAACN,QAAS,KACnC;wBACAmB,YAAYO,OAAO,CACjB,GAAG/E,QAAQC,GAAG,CAAC4E,4BAA4B,CAAC,MAAM,EAChDxB,CAAAA,KAAK2B,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,KACzC,EACH;4BACEC,OAAOf;4BACPhD,KAAKkD,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdzC,wBAAwBO,GAAG,CACzBoC,QACA,IAAI1C,IACF8D,OAAO7C,OAAO,CAACe,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIrB,GAAGyC,MAAM,GAAG,GAAG;wBACjB,OAAOzC,GAAG/B,MAAM,CAACX,MAAQU,mBAAmBC,MAAMX;oBACpD;oBAEA,MAAMQ,SAASkC,GAAG/B;oBAClB,IAAIyE,CAAAA,GAAAA,YAAAA,UAAU,EAAC5E,SAAS;wBACtB,uCAAuC;wBACvC,OAAOA,OACJ6E,IAAI,CAAC,CAACC;4BACL3E,KAAKO,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAOoE;wBACT,GACCC,KAAK,CAAC,CAACvF;4BACNU,mBAAmBC,MAAMX;4BACzB,MAAMA;wBACR,GACCwF,OAAO,CAACjB;oBACb,OAAO;wBACL5D,KAAKO,GAAG;wBACRqD;oBACF;oBAEA,OAAO/D;gBACT,EAAE,OAAOR,KAAU;oBACjBU,mBAAmBC,MAAMX;oBACzBuE;oBACA,MAAMvE;gBACR;YACF;IAGN;IAaOyF,KAAK,GAAGzC,IAAgB,EAAE;QAC/B,MAAM0C,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMvC,SAASV,GAAG,GACvBM,KAAKmC,MAAM,KAAK,IAAInC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACM,WAAAA,wBAAwB,CAACC,QAAQ,CAACoC,SACnC/F,QAAQC,GAAG,CAAC2D,iBAAiB,KAAK,KAClC;YACA,OAAOd;QACT;QAEA,OAAO;YACL,IAAIkD,aAAaxC;YACjB,IAAI,OAAOwC,eAAe,cAAc,OAAOlD,OAAO,YAAY;gBAChEkD,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUX,MAAM,GAAG;YACrC,MAAMa,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOzD,UAAU,GAAGiE,IAAI,CAACjG,QAAQmC,MAAM,IAAI4D;gBAChE,OAAON,OAAOvF,KAAK,CAACwF,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAU/F,GAAQ;wBACvCoG,QAAAA,OAAAA,KAAAA,IAAAA,KAAOpG;wBACP,OAAOiG,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOpD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOvF,KAAK,CAACwF,MAAMC,YAAY,IAAMlD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGrD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMU,cAAc,IAAI,CAACd,cAAc,CACrCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASO,UAAU,KAAI,IAAI,CAACpB,kBAAkB;QAEhD,OAAO,IAAI,CAACP,iBAAiB,GAAGqE,SAAS,CAACpD,MAAMG,SAASM;IAC3D;IAEQd,eAAee,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBxD,MAAMmG,OAAO,CAACrG,QAAQmC,MAAM,IAAIuB,cAChCW;QAEJ,OAAOZ;IACT;IAEO6C,wBAAwB;QAC7B,MAAMzC,SAAS7D,QAAQmC,MAAM,GAAGoE,QAAQ,CAACnF;QACzC,OAAOF,wBAAwBsF,GAAG,CAAC3C;IACrC;IAEO4C,qBAAqB9E,GAAmB,EAAEC,KAAqB,EAAE;QACtE,MAAMiC,SAAS7D,QAAQmC,MAAM,GAAGoE,QAAQ,CAACnF;QACzC,MAAM0C,aAAa5C,wBAAwBsF,GAAG,CAAC3C;QAC/C,IAAIC,YAAY;YACdA,WAAWrC,GAAG,CAACE,KAAKC;QACtB;IACF;AACF;AAEA,MAAMpC,YAAa,CAAA;IACjB,MAAMiG,SAAS,IAAI3D;IAEnB,OAAO,IAAM2D;AACf,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/lib/detached-promise.ts"], "sourcesContent": ["/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n"], "names": ["Detached<PERSON>romise", "constructor", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+<PERSON><PERSON><PERSON><PERSON>,mBAAAA;;;eAAAA;;;AAAN,MAAMA;IAKXC,aAAc;QACZ,IAAIC;QACJ,IAAIC;QAEJ,6DAA6D;QAC7D,IAAI,CAACC,OAAO,GAAG,IAAIC,QAAW,CAACC,KAAKC;YAClCL,UAAUI;YACVH,SAASI;QACX;QAEA,yEAAyE;QACzE,iBAAiB;QACjB,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/stream-utils/encodedTags.ts"], "sourcesContent": ["export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n} as const\n"], "names": ["ENCODED_TAGS", "OPENING", "HTML", "Uint8Array", "BODY", "CLOSED", "HEAD", "BODY_AND_HTML"], "mappings": ";;;;+BAAaA,gBAAAA;;;eAAAA;;;AAAN,MAAMA,eAAe;IAC1B,iHAAiH;IACjHC,SAAS;QACP,QAAQ;QACRC,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI;QAC7C,QAAQ;QACRC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;SAAI;IAC9C;IACAE,QAAQ;QACN,UAAU;QACVC,MAAM,IAAIH,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAI;YAAK;SAAG;QACpD,UAAU;QACVC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;SAAG;QACpD,UAAU;QACVD,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAG;QACrD,iBAAiB;QACjBI,eAAe,IAAIJ,WAAW;YAC5B;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAC5D;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/stream-utils/uint8array-helpers.ts"], "sourcesContent": ["/**\n * Find the starting index of Uint8Array `b` within Uint8Array `a`.\n */\nexport function indexOfUint8Array(a: Uint8Array, b: Uint8Array) {\n  if (b.length === 0) return 0\n  if (a.length === 0 || b.length > a.length) return -1\n\n  // start iterating through `a`\n  for (let i = 0; i <= a.length - b.length; i++) {\n    let completeMatch = true\n    // from index `i`, iterate through `b` and check for mismatch\n    for (let j = 0; j < b.length; j++) {\n      // if the values do not match, then this isn't a complete match, exit `b` iteration early and iterate to next index of `a`.\n      if (a[i + j] !== b[j]) {\n        completeMatch = false\n        break\n      }\n    }\n\n    if (completeMatch) {\n      return i\n    }\n  }\n\n  return -1\n}\n\n/**\n * Check if two Uint8Arrays are strictly equivalent.\n */\nexport function isEquivalentUint8Arrays(a: Uint8Array, b: Uint8Array) {\n  if (a.length !== b.length) return false\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Remove Uint8Array `b` from Uint8Array `a`.\n *\n * If `b` is not in `a`, `a` is returned unchanged.\n *\n * Otherwise, the function returns a new Uint8Array instance with size `a.length - b.length`\n */\nexport function removeFromUint8Array(a: Uint8Array, b: Uint8Array) {\n  const tagIndex = indexOfUint8Array(a, b)\n  if (tagIndex === 0) return a.subarray(b.length)\n  if (tagIndex > -1) {\n    const removed = new Uint8Array(a.length - b.length)\n    removed.set(a.slice(0, tagIndex))\n    removed.set(a.slice(tagIndex + b.length), tagIndex)\n    return removed\n  } else {\n    return a\n  }\n}\n"], "names": ["indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "a", "b", "length", "i", "completeMatch", "j", "tagIndex", "subarray", "removed", "Uint8Array", "set", "slice"], "mappings": "AAAA;;CAEC,GAAA;;;;;;;;;;;;;;;;IACeA,iBAAiB,EAAA;eAAjBA;;IA2BAC,uBAAuB,EAAA;eAAvBA;;IAiBAC,oBAAoB,EAAA;eAApBA;;;AA5CT,SAASF,kBAAkBG,CAAa,EAAEC,CAAa;IAC5D,IAAIA,EAAEC,MAAM,KAAK,GAAG,OAAO;IAC3B,IAAIF,EAAEE,MAAM,KAAK,KAAKD,EAAEC,MAAM,GAAGF,EAAEE,MAAM,EAAE,OAAO,CAAC;IAEnD,8BAA8B;IAC9B,IAAK,IAAIC,IAAI,GAAGA,KAAKH,EAAEE,MAAM,GAAGD,EAAEC,MAAM,EAAEC,IAAK;QAC7C,IAAIC,gBAAgB;QACpB,6DAA6D;QAC7D,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,EAAEC,MAAM,EAAEG,IAAK;YACjC,2HAA2H;YAC3H,IAAIL,CAAC,CAACG,IAAIE,EAAE,KAAKJ,CAAC,CAACI,EAAE,EAAE;gBACrBD,gBAAgB;gBAChB;YACF;QACF;QAEA,IAAIA,eAAe;YACjB,OAAOD;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAKO,SAASL,wBAAwBE,CAAa,EAAEC,CAAa;IAClE,IAAID,EAAEE,MAAM,KAAKD,EAAEC,MAAM,EAAE,OAAO;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIH,EAAEE,MAAM,EAAEC,IAAK;QACjC,IAAIH,CAAC,CAACG,EAAE,KAAKF,CAAC,CAACE,EAAE,EAAE,OAAO;IAC5B;IAEA,OAAO;AACT;AASO,SAASJ,qBAAqBC,CAAa,EAAEC,CAAa;IAC/D,MAAMK,WAAWT,kBAAkBG,GAAGC;IACtC,IAAIK,aAAa,GAAG,OAAON,EAAEO,QAAQ,CAACN,EAAEC,MAAM;IAC9C,IAAII,WAAW,CAAC,GAAG;QACjB,MAAME,UAAU,IAAIC,WAAWT,EAAEE,MAAM,GAAGD,EAAEC,MAAM;QAClDM,QAAQE,GAAG,CAACV,EAAEW,KAAK,CAAC,GAAGL;QACvBE,QAAQE,GAAG,CAACV,EAAEW,KAAK,CAACL,WAAWL,EAAEC,MAAM,GAAGI;QAC1C,OAAOE;IACT,OAAO;QACL,OAAOR;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;;+BAAaA,2BAAAA;;;eAAAA;;;AAAN,MAAMA,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["chainStreams", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createDocumentClosingStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "chunk", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "hasBytes", "insertion", "encodedInsertion", "index", "indexOfUint8Array", "ENCODED_TAGS", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "atLeastOneTask", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "isEquivalentUint8Arrays", "BODY", "HTML", "removeFromUint8Array", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "MISSING_ROOT_TAGS_ERROR", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "prerenderStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IA2BgBA,YAAY,EAAA;eAAZA;;IAknBMC,yBAAyB,EAAA;eAAzBA;;IA1DAC,wBAAwB,EAAA;eAAxBA;;IArDAC,kBAAkB,EAAA;eAAlBA;;IAgFAC,uBAAuB,EAAA;eAAvBA;;IAnfNC,6BAA6B,EAAA;eAA7BA;;IA2iBAC,2BAA2B,EAAA;eAA3BA;;IApNAC,+BAA+B,EAAA;eAA/BA;;IA7RAC,yBAAyB,EAAA;eAAzBA;;IAzGAC,gBAAgB,EAAA;eAAhBA;;IATAC,gBAAgB,EAAA;eAAhBA;;IAkBMC,cAAc,EAAA;eAAdA;;IAkBAC,cAAc,EAAA;eAAdA;;;wBAvGI;2BACI;iCACE;2BACkB;6BACrB;mCAKtB;4BACiC;AAExC,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASf,aACd,GAAGgB,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,OAAA,cAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAAChB;IAEd,OAAOM;AACT;AAEO,SAAST,iBAAiBoB,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,SAAS3B,iBAAiB4B,KAAa;IAC5C,OAAO,IAAIN,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACG;YACnBJ,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAezB,eACpB2B,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAe7B,eACpB0B,MAAkC,EAClCU,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMf,SAASC,OAAQ;QAChC,IAAIU,UAAAA,OAAAA,KAAAA,IAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACjB,OAAO;YAAEC,QAAQ;QAAK;IACjD;IAEAc,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAAS/C;IAId,IAAIkD,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,yDAAyD;QACzD,IAAIwB,SAAS;QAEb,MAAME,WAAW,IAAIC,iBAAAA,eAAe;QACpCH,UAAUE;QAEVE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC;YAChB,IAAI;gBACF,MAAMxB,QAAQ,IAAIyB,WAAWN;gBAC7B,IAAIO,cAAc;gBAElB,IAAK,IAAItC,IAAI,GAAGA,IAAI8B,eAAetC,MAAM,EAAEQ,IAAK;oBAC9C,MAAMuC,gBAAgBT,cAAc,CAAC9B,EAAE;oBACvCY,MAAM4B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5EX,eAAetC,MAAM,GAAG;gBACxBuC,mBAAmB;gBACnBvB,WAAWC,OAAO,CAACG;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRoB,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,kDAAkD;YAClDsB,eAAeV,IAAI,CAACR;YACpBmB,oBAAoBnB,MAAM6B,UAAU;YAEpC,sCAAsC;YACtCR,MAAMzB;QACR;QACAyB;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQnC,OAAO;QACxB;IACF;AACF;AAEO,SAASd,0BAA0B,EACxC8D,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,CAAAA,GAAAA,QAAAA,SAAS,IAAGC,KAAK,CAACC,WAAAA,aAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI3D,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B+C,WAAW;YAEX,MAAMC,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZ,IAAIE,WAAW;oBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;oBACxChD,WAAWC,OAAO,CAACgD;gBACrB;gBACAjD,WAAWC,OAAO,CAACG;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAM8C,QAAQC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIJ,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAI1B,WAC9BzB,MAAMpB,MAAM,GAAGiE,iBAAiBjE,MAAM;wBAExC,0DAA0D;wBAC1DuE,oBAAoBvB,GAAG,CAAC5B,MAAMoD,KAAK,CAAC,GAAGN;wBACvC,qCAAqC;wBACrCK,oBAAoBvB,GAAG,CAACiB,kBAAkBC;wBAC1C,+BAA+B;wBAC/BK,oBAAoBvB,GAAG,CACrB5B,MAAMoD,KAAK,CAACN,QACZA,QAAQD,iBAAiBjE,MAAM;wBAEjCgB,WAAWC,OAAO,CAACsD;oBACrB,OAAO;wBACLvD,WAAWC,OAAO,CAACG;oBACrB;oBACA0C,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIE,WAAW;wBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;oBACpC;oBACAhD,WAAWC,OAAO,CAACG;oBACnB0C,WAAW;gBACb;YACF;QACF;QACA,MAAMrB,OAAMzB,UAAU;YACpB,gEAAgE;YAChE,IAAI+C,UAAU;gBACZ,MAAMC,YAAY,MAAMH;gBACxB,IAAIG,WAAW;oBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAInC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,MAAM0B,WAAW,IAAIC,iBAAAA,eAAe;QACpCH,UAAUE;QAEVE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC;YAChB,IAAI;gBACF5B,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRlC,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,wCAAwC;YACxC,IAAIuD,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVlC,MAAMzB;QACR;QACAyB,OAAMzB,UAAU;YACd,IAAIwB,SAAS,OAAOA,QAAQnC,OAAO;YACnC,IAAIsE,SAAS;YAEb,aAAa;YACb3D,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPvD,MAAkC;IAElC,IAAIwD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa/D,UAA4C;QACtE,IAAI6D,MAAM;YACR;QACF;QAEA,MAAMvD,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMyD,CAAAA,GAAAA,WAAAA,cAAc;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEvD,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRqD,cAAc;oBACd;gBACF;gBAEA9D,WAAWC,OAAO,CAACS;YACrB;QACF,EAAE,OAAOuD,KAAK;YACZjE,WAAWkE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI7E,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,6DAA6D;YAC7D,IAAI,CAACyD,MAAM;gBACTA,OAAOE,aAAa/D;YACtB;QACF;QACAyB,OAAMzB,UAAU;YACd,IAAI8D,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa/D;QAC9B;IACF;AACF;AAEA,MAAMmE,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIjF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,IAAIqE,aAAa;gBACf,OAAOrE,WAAWC,OAAO,CAACG;YAC5B;YAEA,MAAM8C,QAAQC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa;YACxE,IAAIpB,QAAQ,CAAC,GAAG;gBACdmB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIjE,MAAMpB,MAAM,KAAKoE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMuF,SAASnE,MAAMoD,KAAK,CAAC,GAAGN;gBAC9BlD,WAAWC,OAAO,CAACsE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAInE,MAAMpB,MAAM,GAAGoE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,GAAGkE,OAAO;oBACnE,uCAAuC;oBACvC,MAAMsB,QAAQpE,MAAMoD,KAAK,CACvBN,QAAQE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM;oBAElDgB,WAAWC,OAAO,CAACuE;gBACrB;YACF,OAAO;gBACLxE,WAAWC,OAAO,CAACG;YACrB;QACF;QACAqB,OAAMzB,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAACmD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIrF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACE0E,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,KAChEI,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACsB,IAAI,KACvDD,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACuB,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFxE,QAAQyE,CAAAA,GAAAA,mBAAAA,oBAAoB,EAACzE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACsB,IAAI;YAC5DvE,QAAQyE,CAAAA,GAAAA,mBAAAA,oBAAoB,EAACzE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACuB,IAAI;YAE5D5E,WAAWC,OAAO,CAACG;QACrB;IACF;AACF;AAOO,SAAS9B;IAId,IAAIwG,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI3F,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC8E,aACD3B,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAAC4B,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACD5B,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAAC4B,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA/E,WAAWC,OAAO,CAACG;QACrB;QACAqB,OAAMzB,UAAU;YACd,MAAMiF,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYrE,IAAI,CAAC;YACjC,IAAI,CAACmE,WAAWE,YAAYrE,IAAI,CAAC;YAEjC,IAAI,CAACqE,YAAYjG,MAAM,EAAE;YAEzBgB,WAAWC,OAAO,CAChBpB,QAAQqB,MAAM,CACZ,CAAC;;+CAEoC,EAAE+E,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYjG,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,EAAEqG,YAAAA,uBAAuB,CAAC;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAASC,kBACPpG,QAA2B,EAC3BqG,YAAyD;IAEzD,IAAIlF,SAASnB;IACb,KAAK,MAAMsG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBnF,SAASA,OAAOoF,WAAW,CAACD;IAC9B;IACA,OAAOnF;AACT;AAcO,eAAenC,mBACpBwH,YAAiC,EACjC,EACEhC,MAAM,EACNiC,iBAAiB,EACjBC,kBAAkB,EAClBC,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBtC,SAASA,OAAOuC,KAAK,CAAC9B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIyB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrDtH;QAEA,4BAA4B;QAC5BwE,mCAAmCkD;QAEnC,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAehH,MAAM,GAAG,IAC9CyE,2BAA2BuC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB/B,4BAA4B+B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBzH,oCAAoC;QAEzD,kDAAkD;QAClD8F;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ExB,mCAAmCiD;KACpC;AACH;AAOO,eAAe5H,yBACpBkI,eAA2C,EAC3C,EACEN,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEK,gBACE,qDAAqD;KACpDV,WAAW,CAACrH,iCACZqH,WAAW,CAAChB,2CACb,gCAAgC;KAC/BgB,WAAW,CAAC7C,mCAAmCiD,wBAChD,4BAA4B;KAC3BJ,WAAW,CACV7C,mCAAmCkD;AAG3C;AAQO,eAAe3H,wBACpBgI,eAA2C,EAC3C,EACER,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACM;IAEjC,OACEK,gBACE,qDAAqD;KACpDV,WAAW,CAACrH,iCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,wBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,4BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,oBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAQO,eAAepG,0BACpB0H,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,aACE,qDAAqD;KACpDD,WAAW,CAACrH,iCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,wBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,4BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,oBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAEO,SAAS/F;IACd,OAAOI,iBAAiB0F;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/app-render/encryption-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n"], "names": ["arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "__next_loaded_action_key", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "length", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "normalizeAppPath", "serverActionsManifestSingleton", "InvariantError", "workStore", "workAsyncStorage", "getStore", "mergeClientReferenceManifests", "route", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAYgBA,mBAAmB,EAAA;eAAnBA;;IA0CAC,OAAO,EAAA;eAAPA;;IAXAC,OAAO,EAAA;eAAPA;;IA6HMC,sBAAsB,EAAA;eAAtBA;;IAxCNC,gCAAgC,EAAA;eAAhCA;;IApBAC,kBAAkB,EAAA;eAAlBA;;IAnCAC,8BAA8B,EAAA;eAA9BA;;IAzCAC,kBAAkB,EAAA;eAAlBA;;;gCA1Be;0BACE;0CACA;AAEjC,IAAIC;AAEG,SAASR,oBACdS,MAAiD;IAEjD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASV,mBAAmBU,MAAc;IAC/C,MAAML,MAAMK,OAAOE,MAAM;IACzB,MAAMC,MAAM,IAAIT,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BE,GAAG,CAACF,EAAE,GAAGD,OAAOI,UAAU,CAACH;IAC7B;IAEA,OAAOE;AACT;AAEO,SAASlB,QAAQoB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACxB,OAAO,CAC1B;QACEyB,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASvB,QAAQqB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACzB,OAAO,CAC1B;QACE0B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMI,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASxB,+BAA+B,EAC7CyB,IAAI,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAYhB;QAEyCC;IADxC,mBAAmB;IACnB,MAAMC,kCAAAA,CAAkCD,gDAAAA,UAAU,CAChDP,kCACD,KAAA,OAAA,KAAA,IAFuCO,8CAErCC,+BAA+B;IAIlC,mBAAmB;IACnBD,UAAU,CAACP,kCAAkC,GAAG;QAC9CQ,iCAAiC;YAC/B,GAAGA,+BAA+B;YAClC,CAACC,CAAAA,GAAAA,UAAAA,gBAAgB,EAACN,MAAM,EAAEC;QAC5B;QACAC;QACAC;IACF;AACF;AAEO,SAAS7B;IACd,MAAMiC,iCAAkCH,UAAkB,CACxDP,kCACD;IAUD,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,OAAOD,+BAA+BJ,eAAe;AACvD;AAEO,SAAS9B;IACd,MAAMkC,iCAAkCH,UAAkB,CACxDP,kCACD;IAMD,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM,EAAEH,+BAA+B,EAAE,GAAGE;IAC5C,MAAME,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAI,CAACF,WAAW;QACd,0EAA0E;QAC1E,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,aAAa;QACb,OAAOG,8BAA8BP;IACvC;IAEA,MAAMJ,0BACJI,+BAA+B,CAACI,UAAUI,KAAK,CAAC;IAElD,IAAI,CAACZ,yBAAyB;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIO,gBAAAA,cAAc,CACtB,CAAC,sCAAsC,EAAEC,UAAUI,KAAK,CAAC,CAAC,CAAC,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOZ;AACT;AAEO,eAAe7B;IACpB,IAAIK,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAM8B,iCAAkCH,UAAkB,CACxDP,kCACD;IAID,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAMM,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CV,+BAA+BL,qBAAqB,CAACgB,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,OAAA,cAA+D,CAA/D,IAAIX,gBAAAA,cAAc,CAAC,8CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEA/B,2BAA2B,MAAMiB,OAAOC,MAAM,CAACyB,SAAS,CACtD,OACA5C,mBAAmB6C,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAOrC;AACT;AAEA,SAASmC,8BACPP,+BAEC;IAED,MAAMiB,2BAA2BC,OAAOC,MAAM,CAC5CnB;IAGF,MAAMoB,gCAA+D;QACnEC,eAAe,CAAC;QAChBC,sBAAsB,CAAC;QACvBC,kBAAkB,CAAC;IACrB;IAEA,KAAK,MAAM3B,2BAA2BqB,yBAA0B;QAC9DG,8BAA8BC,aAAa,GAAG;YAC5C,GAAGD,8BAA8BC,aAAa;YAC9C,GAAGzB,wBAAwByB,aAAa;QAC1C;QACAD,8BAA8BE,oBAAoB,GAAG;YACnD,GAAGF,8BAA8BE,oBAAoB;YACrD,GAAG1B,wBAAwB0B,oBAAoB;QACjD;QACAF,8BAA8BG,gBAAgB,GAAG;YAC/C,GAAGH,8BAA8BG,gBAAgB;YACjD,GAAG3B,wBAAwB2B,gBAAgB;QAC7C;IACF;IAEA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1574, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVeA,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/app-render/encryption.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client.edge'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    const hangingInputAbortSignal =\n      workUnitStore?.type === 'prerender'\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      throw error\n    }\n\n    if (!workUnitStore) {\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    cacheSignal?.beginRead()\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    cacheSignal?.endRead()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        if (workUnitStore?.type === 'prerender') {\n          // Explicitly don't close the stream here (until prerendering is\n          // complete) so that hanging promises are not rejected.\n          if (workUnitStore.renderSignal.aborted) {\n            controller.close()\n          } else {\n            workUnitStore.renderSignal.addEventListener(\n              'abort',\n              () => controller.close(),\n              { once: true }\n            )\n          }\n        } else {\n          controller.close()\n        }\n      },\n    }),\n    {\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n"], "names": ["decryptActionBoundArgs", "encryptActionBoundArgs", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "workUnitAsyncStorage", "exit", "crypto", "getRandomValues", "arrayBufferToString", "buffer", "encrypted", "encrypt", "encode", "btoa", "React", "cache", "args", "clientModules", "getClientReferenceManifestForRsc", "error", "captureStackTrace", "didCatchError", "workUnitStore", "getStore", "hangingInputAbortSignal", "type", "createHangingInputAbortSignal", "serialized", "streamToString", "renderToReadableStream", "signal", "onError", "err", "aborted", "message", "String", "NODE_ENV", "console", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "get", "cacheSignal", "beginRead", "endRead", "set", "encryptedPromise", "decryptedBoundArgs", "edgeRscModuleMapping", "rscModuleMapping", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "renderSignal", "close", "addEventListener", "once", "serverConsumerManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap"], "mappings": "AAAA,oDAAoD,GAAA;;;;;;;;;;;;;;;IA+K9BA,sBAAsB,EAAA;eAAtBA;;IAvFTC,sBAAsB,EAAA;eAAtBA;;;;4BApF0B;4BAEE;sCAEV;iCASxB;8CAKA;kCACuC;8DAC5B;;;;;;AAElB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAEnD,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB;;CAEC,GACD,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,CAAAA,GAAAA,iBAAAA,sBAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,OAAA,cAEL,CAFK,IAAIE,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYb,YAAYc,MAAM,CAClC,MAAMC,CAAAA,GAAAA,iBAAAA,OAAO,EAACV,KAAKW,CAAAA,GAAAA,iBAAAA,kBAAkB,EAACN,UAAUM,CAAAA,GAAAA,iBAAAA,kBAAkB,EAACJ;IAGrE,IAAI,CAACC,UAAUI,UAAU,CAACd,WAAW;QACnC,MAAM,OAAA,cAA8D,CAA9D,IAAII,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,OAAOM,UAAUF,KAAK,CAACR,SAASe,MAAM;AACxC;AAEA;;;CAGC,GACD,eAAeC,qBAAqBhB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,CAAAA,GAAAA,iBAAAA,sBAAsB;IACxC,IAAID,QAAQe,WAAW;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIb,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,8BAAAA,oBAAoB,CAACC,IAAI,CAAC,IAAMC,OAAOC,eAAe,CAACL;IACvD,MAAMX,UAAUiB,CAAAA,GAAAA,iBAAAA,mBAAmB,EAACN,YAAYO,MAAM;IAEtD,MAAMC,YAAY,MAAMC,CAAAA,GAAAA,iBAAAA,OAAO,EAC7BzB,KACAgB,aACAvB,YAAYiC,MAAM,CAAC5B,WAAWC;IAGhC,OAAO4B,KAAKtB,UAAUiB,CAAAA,GAAAA,iBAAAA,mBAAmB,EAACE;AAC5C;AAMO,MAAMpC,yBAAyBwC,OAAAA,OAAK,CAACC,KAAK,CAC/C,eAAezC,uBAAuBU,QAAgB,EAAE,GAAGgC,IAAW;IACpE,MAAM,EAAEC,aAAa,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,gCAAgC;IAE1D,yEAAyE;IACzE,+DAA+D;IAC/D,MAAMC,QAAQ,IAAI/B;IAClBA,MAAMgC,iBAAiB,CAACD,OAAO7C;IAE/B,IAAI+C,gBAAgB;IAEpB,MAAMC,gBAAgBlB,8BAAAA,oBAAoB,CAACmB,QAAQ;IAEnD,MAAMC,0BACJF,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeG,IAAI,MAAK,cACpBC,CAAAA,GAAAA,kBAAAA,6BAA6B,EAACJ,iBAC9BrB;IAEN,oDAAoD;IACpD,MAAM0B,aAAa,MAAMC,CAAAA,GAAAA,sBAAAA,cAAc,EACrCC,CAAAA,GAAAA,YAAAA,sBAAsB,EAACb,MAAMC,eAAe;QAC1Ca,QAAQN;QACRO,SAAQC,GAAG;YACT,IAAIR,2BAAAA,OAAAA,KAAAA,IAAAA,wBAAyBS,OAAO,EAAE;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAIZ,eAAe;gBACjB;YACF;YAEAA,gBAAgB;YAEhB,sEAAsE;YACtE,kEAAkE;YAClEF,MAAMe,OAAO,GAAGF,eAAe5C,QAAQ4C,IAAIE,OAAO,GAAGC,OAAOH;QAC9D;IACF,IACA,AACA,qEADqE,EACE;IACvE,+CAA+C;IAC/CR;IAGF,IAAIH,eAAe;QACjB,IAAI7C,QAAQC,GAAG,CAAC2D,QAAQ,KAAK,WAAe;YAC1C,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxEC,QAAQlB,KAAK,CAACA;QAChB;QAEA,MAAMA;IACR;IAEA,IAAI,CAACG,eAAe;QAClB,OAAOtB,qBAAqBhB,UAAU2C;IACxC;IAEA,MAAMW,2BAA2BC,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACjB;IAC7D,MAAMkB,wBAAwBC,CAAAA,GAAAA,8BAAAA,wBAAwB,EAACnB;IACvD,MAAMoB,WAAW1D,WAAW2C;IAE5B,MAAMgB,kBACJL,CAAAA,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0BM,kBAAkB,CAACC,GAAG,CAACH,SAAAA,KAAAA,CACjDF,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAuBI,kBAAkB,CAACC,GAAG,CAACH,SAAAA;IAEhD,IAAIC,iBAAiB;QACnB,OAAOA;IACT;IAEA,MAAMG,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;IAEnE6C,eAAAA,OAAAA,KAAAA,IAAAA,YAAaC,SAAS;IAEtB,MAAMrC,YAAY,MAAMV,qBAAqBhB,UAAU2C;IAEvDmB,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO;IACpBV,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0BM,kBAAkB,CAACK,GAAG,CAACP,UAAUhC;IAE3D,OAAOA;AACT;AAIK,eAAerC,uBACpBW,QAAgB,EAChBkE,gBAAiC;IAEjC,MAAMxC,YAAY,MAAMwC;IACxB,MAAM5B,gBAAgBlB,8BAAAA,oBAAoB,CAACmB,QAAQ;IAEnD,IAAI7B;IAEJ,IAAI4B,eAAe;QACjB,MAAMwB,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;QAEnE,MAAMqC,2BAA2BC,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACjB;QAC7D,MAAMkB,wBAAwBC,CAAAA,GAAAA,8BAAAA,wBAAwB,EAACnB;QAEvD5B,YACE4C,CAAAA,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0Ba,kBAAkB,CAACN,GAAG,CAACnC,UAAAA,KAAAA,CACjD8B,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAuBW,kBAAkB,CAACN,GAAG,CAACnC,UAAAA;QAEhD,IAAI,CAAChB,WAAW;YACdoD,eAAAA,OAAAA,KAAAA,IAAAA,YAAaC,SAAS;YACtBrD,YAAY,MAAMX,qBAAqBC,UAAU0B;YACjDoC,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO;YACpBV,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0Ba,kBAAkB,CAACF,GAAG,CAACvC,WAAWhB;QAC9D;IACF,OAAO;QACLA,YAAY,MAAMX,qBAAqBC,UAAU0B;IACnD;IAEA,MAAM,EAAE0C,oBAAoB,EAAEC,gBAAgB,EAAE,GAC9CnC,CAAAA,GAAAA,iBAAAA,gCAAgC;IAElC,wDAAwD;IACxD,MAAMoC,eAAe,MAAMC,CAAAA,GAAAA,YAAAA,wBAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAChF,YAAYiC,MAAM,CAAClB;YAEtC,IAAI4B,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeG,IAAI,MAAK,aAAa;gBACvC,gEAAgE;gBAChE,uDAAuD;gBACvD,IAAIH,cAAcsC,YAAY,CAAC3B,OAAO,EAAE;oBACtCyB,WAAWG,KAAK;gBAClB,OAAO;oBACLvC,cAAcsC,YAAY,CAACE,gBAAgB,CACzC,SACA,IAAMJ,WAAWG,KAAK,IACtB;wBAAEE,MAAM;oBAAK;gBAEjB;YACF,OAAO;gBACLL,WAAWG,KAAK;YAClB;QACF;IACF,IACA;QACEG,wBAAwB;YACtB,2FAA2F;YAC3F,oFAAoF;YACpF,6DAA6D;YAC7DC,eAAe;YACfC,WAAW3F,gBAAgB6E,6DAAuBC;YAClDc,iBAAiBC,CAAAA,GAAAA,iBAAAA,kBAAkB;QACrC;IACF;IAGF,OAAOd;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-validate.ts"], "sourcesContent": ["// This function ensures that all the exported values are valid server actions,\n// during the runtime. By definition all actions are required to be async\n// functions, but here we can only check that they are functions.\nexport function ensureServerEntryExports(actions: any[]) {\n  for (let i = 0; i < actions.length; i++) {\n    const action = actions[i]\n    if (typeof action !== 'function') {\n      throw new Error(\n        `A \"use server\" file can only export async functions, found ${typeof action}.\\nRead more: https://nextjs.org/docs/messages/invalid-use-server-value`\n      )\n    }\n  }\n}\n"], "names": ["ensureServerEntryExports", "actions", "i", "length", "action", "Error"], "mappings": "AAAA,+EAA+E;AAC/E,yEAAyE;AACzE,iEAAiE;;;;;+BACjDA,4BAAAA;;;eAAAA;;;AAAT,SAASA,yBAAyBC,OAAc;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAID,QAAQE,MAAM,EAAED,IAAK;QACvC,MAAME,SAASH,OAAO,CAACC,EAAE;QACzB,IAAI,OAAOE,WAAW,YAAY;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,2DAA2D,EAAE,OAAOD,OAAO,uEAAuE,CAAC,GADhJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/next%4015.3.3_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,iXAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}