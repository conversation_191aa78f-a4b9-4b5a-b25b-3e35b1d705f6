{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\n// let userIsLoggedIn = false;\n// let userProjects: [];\n\nconst API_BASE_URL = \"http://localhost:8000\"; // 后端API地址\n\nexport async function login(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/token`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n      body: new URLSearchParams({\n        username: data.email,\n        password: data.password,\n      }).toString(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Login failed\" };\n    }\n\n    const result = await response.json();\n    // You might want to store the token in a cookie or local storage\n    // For now, we'll just return success.\n    return { success: true, user: { name: data.email, email: data.email } }; // Assuming backend returns user info\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function logout() {\n  // For logout, you might clear the token from storage\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/register`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ email: data.email, password: data.password, name: data.name }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Registration failed\" };\n    }\n\n    const result = await response.json();\n    return { success: true, user: result };\n  } catch (error) {\n    console.error(\"Registration error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function getAuthState() {\n    // This function needs to be implemented on the backend if you want to check auth state from there.\n    // For now, we'll assume if a token exists (e.g., in local storage), the user is logged in.\n    // This is a simplified example and might need more robust implementation.\n    return { isLoggedIn: false }; // Placeholder\n}\n\nexport async function saveProject(project: Project) {\n  try {\n    const token = \"YOUR_AUTH_TOKEN\"; // Replace with actual token retrieval\n    const response = await fetch(`${API_BASE_URL}/saveProject`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Authorization\": `Bearer ${token}`,\n      },\n      body: JSON.stringify(project),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to save project\" };\n    }\n\n    const result = await response.json();\n    return { success: true, message: \"Project saved successfully!\" };\n  } catch (error) {\n    console.error(\"Save project error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const response = await fetch(`${API_BASE_URL}/getWritingAnalysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(input),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to get analysis\" };\n    }\n\n    const result = await response.json();\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(\"Writing analysis error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateCharacter`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate character\" };\n        }\n\n        const result = await response.json();\n        return { success: true, character: result };\n    } catch (error) {\n        console.error(\"Character generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateNovelOutline`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(input),\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate novel outline\" };\n        }\n\n        const result = await response.json();\n        return { success: true, outline: result };\n    } catch (error) {\n        console.error(\"Novel outline generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAOA,8BAA8B;AAC9B,8BAA8B;AAC9B,wBAAwB;AAExB,MAAM,eAAe,yBAAyB,UAAU;AAEjD,eAAe,MAAM,IAAS;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,IAAI,gBAAgB;gBACxB,UAAU,KAAK,KAAK;gBACpB,UAAU,KAAK,QAAQ;YACzB,GAAG,QAAQ;QACb;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAAe;QACrE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,iEAAiE;QACjE,sCAAsC;QACtC,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE,MAAM,KAAK,KAAK;gBAAE,OAAO,KAAK,KAAK;YAAC;QAAE,GAAG,qCAAqC;IAChH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IACxE;AACF;AAEO,eAAe;IACpB,qDAAqD;IACrD,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,eAAe,SAAS,IAAS;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,OAAO,KAAK,KAAK;gBAAE,UAAU,KAAK,QAAQ;gBAAE,MAAM,KAAK,IAAI;YAAC;QACrF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAAsB;QAC5E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IACxE;AACF;AAEO,eAAe;IAClB,mGAAmG;IACnG,2FAA2F;IAC3F,0EAA0E;IAC1E,OAAO;QAAE,YAAY;IAAM,GAAG,cAAc;AAChD;AAEO,eAAe,YAAY,OAAgB;IAChD,IAAI;QACF,MAAM,QAAQ,mBAAmB,sCAAsC;QACvE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAAyB;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YAAE,SAAS;YAAM,SAAS;QAA8B;IACjE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IACxE;AACF;AAGO,eAAe,mBAAmB,KAAyC;IAKhF,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAAyB;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YAAE,SAAS;YAAM,aAAa,OAAO,WAAW;QAAC;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IACxE;AACF;AAGO,eAAe;IAKlB,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,CAAC,EAAE;YAC9D,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAA+B;QACvF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YAAE,SAAS;YAAM,WAAW;QAAO;IAC9C,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IAC1E;AACJ;AAEO,eAAe,qBAAqB,KAAgC;IAKvE,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;QACzB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,MAAM,IAAI;YAAmC;QAC3F;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YAAE,SAAS;YAAM,SAAS;QAAO;IAC5C,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsC;IAC1E;AACJ;;;IAxKsB;IA4BA;IAKA;IAuBA;IAOA;IA0BA;IA4BA;IA0BA;;AA/IA,sZAAA;AA4BA,sZAAA;AAKA,sZAAA;AAuBA,sZAAA;AAOA,sZAAA;AA0BA,sZAAA;AA4BA,sZAAA;AA0BA,sZAAA", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/.next-internal/server/app/register/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getAuthState as '006c95642497d0ef7b443912f13eb64d1d84fe07ff'} from 'ACTIONS_MODULE0'\nexport {login as '40145d5f48e9b2cd3ad23766b6412323fb6073c40e'} from 'ACTIONS_MODULE0'\nexport {logout as '004ae6244db5a42653133b3c5adbe531b9296f9472'} from 'ACTIONS_MODULE0'\nexport {register as '40609dfbd242bb3d3cc09f0325f2192c5f2939a0c8'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/register/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/register/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/register/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,4ZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/register/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/register/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/register/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,4ZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}