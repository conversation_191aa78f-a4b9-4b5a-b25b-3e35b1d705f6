{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/__codegen/version.ts"], "sourcesContent": ["// Generated by genversion.\nexport const version = '1.14.0';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,SAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AACO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/async.ts"], "sourcesContent": ["/**\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// NOTE: This file is pulled into client code and cannot have any Node-only\n// dependencies.\n\n/**\n * A handle to a promise and its resolvers.\n */\nexport interface Task<T> {\n  resolve: (result: T) => void;\n  reject: (err: unknown) => void;\n  promise: Promise<T>;\n}\n\n/** Utility for creating Tasks. */\nfunction createTask<T>(): Task<T> {\n  let resolve: unknown, reject: unknown;\n  const promise = new Promise<T>(\n    (res, rej) => ([resolve, reject] = [res, rej])\n  );\n  return {\n    resolve: resolve as Task<T>['resolve'],\n    reject: reject as Task<T>['reject'],\n    promise,\n  };\n}\n\n/**\n * A class designed to help turn repeated callbacks into async iterators.\n * Based loosely on a combination of Go channels and Promises.\n */\nexport class Channel<T> implements AsyncIterable<T> {\n  private ready: Task<void> = createTask<void>();\n  private buffer: (T | null)[] = [];\n  private err: unknown = null;\n\n  send(value: T): void {\n    this.buffer.push(value);\n    this.ready.resolve();\n  }\n\n  close(): void {\n    this.buffer.push(null);\n    this.ready.resolve();\n  }\n\n  error(err: unknown): void {\n    this.err = err;\n    // Note: we must call this.ready.reject here in case we get an error even before the stream is initiated,\n    // however we cannot rely on this.ready.reject because it will be ignored if ready.resolved has already\n    // been called, so this.err will be checked in the iterator as well.\n    this.ready.reject(err);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<T> {\n    return {\n      next: async (): Promise<IteratorResult<T>> => {\n        if (this.err) {\n          throw this.err;\n        }\n\n        if (!this.buffer.length) {\n          await this.ready.promise;\n        }\n        const value = this.buffer.shift()!;\n        if (!this.buffer.length) {\n          this.ready = createTask<void>();\n        }\n\n        return {\n          value,\n          done: !value,\n        };\n      },\n    };\n  }\n}\n\n/**\n * A lazy promise that does not run its executor function until then is called.\n */\nexport class LazyPromise<T> implements PromiseLike<T> {\n  private executor;\n  private promise;\n\n  constructor(executor: (resolve?, reject?) => void | Promise<void>) {\n    this.executor = executor;\n  }\n\n  then<TResult1 = T, TResult2 = never>(\n    onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | undefined\n      | null,\n    onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | undefined\n      | null\n  ): PromiseLike<TResult1 | TResult2> {\n    this.promise ??= new Promise<T>(this.executor);\n    return this.promise.then(onfulfilled, onrejected);\n  }\n}\n\n/** Lazily call the provided function to resolve the LazyPromise. */\nexport function lazy<T>(fn: () => T | PromiseLike<T>): PromiseLike<T> {\n  return new LazyPromise<T>((resolve, reject) => {\n    try {\n      resolve(fn());\n    } catch (e) {\n      reject(e);\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,SAAA,IAAA;IAAA,aAAA,IAAA;IAAA,MAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AA6BA,SAAS,aAAyB;IAChC,IAAI,SAAkB;IACtB,MAAM,UAAU,IAAI,QAClB,CAAC,KAAK,OAAS,CAAC,SAAS,MAAM,CAAA,GAAI;YAAC;YAAK;SAAG;IAE9C,OAAO;QACL;QACA;QACA;IACF;AACF;AAMO,MAAM,QAAuC;IAC1C,QAAoB,WAAiB,EAAA;IACrC,SAAuB,CAAC,CAAA,CAAA;IACxB,MAAe,KAAA;IAEvB,KAAK,KAAA,EAAgB;QACnB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAK;QACtB,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ;IACrB;IAEA,QAAc;QACZ,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI;QACrB,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ;IACrB;IAEA,MAAM,GAAA,EAAoB;QACxB,IAAA,CAAK,GAAA,GAAM;QAIX,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAG;IACvB;IAEA,CAAC,OAAO,aAAa,CAAA,GAAsB;QACzC,OAAO;YACL,MAAM,YAAwC;gBAC5C,IAAI,IAAA,CAAK,GAAA,EAAK;oBACZ,MAAM,IAAA,CAAK,GAAA;gBACb;gBAEA,IAAI,CAAC,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ;oBACvB,MAAM,IAAA,CAAK,KAAA,CAAM,OAAA;gBACnB;gBACA,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;gBAChC,IAAI,CAAC,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ;oBACvB,IAAA,CAAK,KAAA,GAAQ,WAAiB;gBAChC;gBAEA,OAAO;oBACL;oBACA,MAAM,CAAC;gBACT;YACF;QACF;IACF;AACF;AAKO,MAAM,YAAyC;IAC5C,SAAA;IACA,QAAA;IAER,YAAY,QAAA,CAAuD;QACjE,IAAA,CAAK,QAAA,GAAW;IAClB;IAEA,KACE,WAAA,EAIA,UAAA,EAIkC;QAClC,IAAA,CAAK,OAAA,KAAY,IAAI,QAAW,IAAA,CAAK,QAAQ;QAC7C,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,aAAa,UAAU;IAClD;AACF;AAGO,SAAS,KAAQ,EAAA,EAA8C;IACpE,OAAO,IAAI,YAAe,CAAC,SAAS,WAAW;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC;QACd,EAAA,OAAS,GAAG;YACV,OAAO,CAAC;QACV;IACF,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/statusTypes.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as z from 'zod';\n\n/**\n * Enumeration of response status codes.\n */\nexport enum StatusCodes {\n  // Not an error; returned on success.\n  //\n  // HTTP Mapping: 200 OK\n  OK = 0,\n\n  // The operation was cancelled, typically by the caller.\n  //\n  // HTTP Mapping: 499 Client Closed Request\n  CANCELLED = 1,\n\n  // Unknown error.  For example, this error may be returned when\n  // a `Status` value received from another address space belongs to\n  // an error space that is not known in this address space.  Also\n  // errors raised by APIs that do not return enough error information\n  // may be converted to this error.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  UNKNOWN = 2,\n\n  // The client specified an invalid argument.  Note that this differs\n  // from `FAILED_PRECONDITION`.  `INVALID_ARGUMENT` indicates arguments\n  // that are problematic regardless of the state of the system\n  // (e.g., a malformed file name).\n  //\n  // HTTP Mapping: 400 Bad Request\n  INVALID_ARGUMENT = 3,\n\n  // The deadline expired before the operation could complete. For operations\n  // that change the state of the system, this error may be returned\n  // even if the operation has completed successfully.  For example, a\n  // successful response from a server could have been delayed long\n  // enough for the deadline to expire.\n  //\n  // HTTP Mapping: 504 Gateway Timeout\n  DEADLINE_EXCEEDED = 4,\n\n  // Some requested entity (e.g., file or directory) was not found.\n  //\n  // Note to server developers: if a request is denied for an entire class\n  // of users, such as gradual feature rollout or undocumented allowlist,\n  // `NOT_FOUND` may be used. If a request is denied for some users within\n  // a class of users, such as user-based access control, `PERMISSION_DENIED`\n  // must be used.\n  //\n  // HTTP Mapping: 404 Not Found\n  NOT_FOUND = 5,\n\n  // The entity that a client attempted to create (e.g., file or directory)\n  // already exists.\n  //\n  // HTTP Mapping: 409 Conflict\n  ALREADY_EXISTS = 6,\n\n  // The caller does not have permission to execute the specified\n  // operation. `PERMISSION_DENIED` must not be used for rejections\n  // caused by exhausting some resource (use `RESOURCE_EXHAUSTED`\n  // instead for those errors). `PERMISSION_DENIED` must not be\n  // used if the caller can not be identified (use `UNAUTHENTICATED`\n  // instead for those errors). This error code does not imply the\n  // request is valid or the requested entity exists or satisfies\n  // other pre-conditions.\n  //\n  // HTTP Mapping: 403 Forbidden\n  PERMISSION_DENIED = 7,\n\n  // The request does not have valid authentication credentials for the\n  // operation.\n  //\n  // HTTP Mapping: 401 Unauthorized\n  UNAUTHENTICATED = 16,\n\n  // Some resource has been exhausted, perhaps a per-user quota, or\n  // perhaps the entire file system is out of space.\n  //\n  // HTTP Mapping: 429 Too Many Requests\n  RESOURCE_EXHAUSTED = 8,\n\n  // The operation was rejected because the system is not in a state\n  // required for the operation's execution.  For example, the directory\n  // to be deleted is non-empty, an rmdir operation is applied to\n  // a non-directory, etc.\n  //\n  // Service implementors can use the following guidelines to decide\n  // between `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`:\n  //  (a) Use `UNAVAILABLE` if the client can retry just the failing call.\n  //  (b) Use `ABORTED` if the client should retry at a higher level. For\n  //      example, when a client-specified test-and-set fails, indicating the\n  //      client should restart a read-modify-write sequence.\n  //  (c) Use `FAILED_PRECONDITION` if the client should not retry until\n  //      the system state has been explicitly fixed. For example, if an \"rmdir\"\n  //      fails because the directory is non-empty, `FAILED_PRECONDITION`\n  //      should be returned since the client should not retry unless\n  //      the files are deleted from the directory.\n  //\n  // HTTP Mapping: 400 Bad Request\n  FAILED_PRECONDITION = 9,\n\n  // The operation was aborted, typically due to a concurrency issue such as\n  // a sequencer check failure or transaction abort.\n  //\n  // See the guidelines above for deciding between `FAILED_PRECONDITION`,\n  // `ABORTED`, and `UNAVAILABLE`.\n  //\n  // HTTP Mapping: 409 Conflict\n  ABORTED = 10,\n\n  // The operation was attempted past the valid range.  E.g., seeking or\n  // reading past end-of-file.\n  //\n  // Unlike `INVALID_ARGUMENT`, this error indicates a problem that may\n  // be fixed if the system state changes. For example, a 32-bit file\n  // system will generate `INVALID_ARGUMENT` if asked to read at an\n  // offset that is not in the range [0,2^32-1], but it will generate\n  // `OUT_OF_RANGE` if asked to read from an offset past the current\n  // file size.\n  //\n  // There is a fair bit of overlap between `FAILED_PRECONDITION` and\n  // `OUT_OF_RANGE`.  We recommend using `OUT_OF_RANGE` (the more specific\n  // error) when it applies so that callers who are iterating through\n  // a space can easily look for an `OUT_OF_RANGE` error to detect when\n  // they are done.\n  //\n  // HTTP Mapping: 400 Bad Request\n  OUT_OF_RANGE = 11,\n\n  // The operation is not implemented or is not supported/enabled in this\n  // service.\n  //\n  // HTTP Mapping: 501 Not Implemented\n  UNIMPLEMENTED = 12,\n\n  // Internal errors.  This means that some invariants expected by the\n  // underlying system have been broken.  This error code is reserved\n  // for serious errors.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  INTERNAL = 13,\n\n  // The service is currently unavailable.  This is most likely a\n  // transient condition, which can be corrected by retrying with\n  // a backoff. Note that it is not always safe to retry\n  // non-idempotent operations.\n  //\n  // See the guidelines above for deciding between `FAILED_PRECONDITION`,\n  // `ABORTED`, and `UNAVAILABLE`.\n  //\n  // HTTP Mapping: 503 Service Unavailable\n  UNAVAILABLE = 14,\n\n  // Unrecoverable data loss or corruption.\n  //\n  // HTTP Mapping: 500 Internal Server Error\n  DATA_LOSS = 15,\n}\n\nexport const StatusNameSchema = z.enum([\n  'OK',\n  'CANCELLED',\n  'UNKNOWN',\n  'INVALID_ARGUMENT',\n  'DEADLINE_EXCEEDED',\n  'NOT_FOUND',\n  'ALREADY_EXISTS',\n  'PERMISSION_DENIED',\n  'UNAUTHENTICATED',\n  'RESOURCE_EXHAUSTED',\n  'FAILED_PRECONDITION',\n  'ABORTED',\n  'OUT_OF_RANGE',\n  'UNIMPLEMENTED',\n  'INTERNAL',\n  'UNAVAILABLE',\n  'DATA_LOSS',\n]);\nexport type StatusName = z.infer<typeof StatusNameSchema>;\n\nconst statusCodeMap: Record<StatusName, number> = {\n  OK: 200,\n  CANCELLED: 499,\n  UNKNOWN: 500,\n  INVALID_ARGUMENT: 400,\n  DEADLINE_EXCEEDED: 504,\n  NOT_FOUND: 404,\n  ALREADY_EXISTS: 409,\n  PERMISSION_DENIED: 403,\n  UNAUTHENTICATED: 401,\n  RESOURCE_EXHAUSTED: 429,\n  FAILED_PRECONDITION: 400,\n  ABORTED: 409,\n  OUT_OF_RANGE: 400,\n  UNIMPLEMENTED: 501,\n  INTERNAL: 500,\n  UNAVAILABLE: 503,\n  DATA_LOSS: 500,\n};\n\nexport function httpStatusCode(status: StatusName): number {\n  if (!(status in statusCodeMap)) {\n    throw new Error(`Invalid status code ${status}`);\n  }\n  return statusCodeMap[status];\n}\n\nconst StatusCodesSchema = z.nativeEnum(StatusCodes);\n\n// If changing below\nexport const StatusSchema = z.object({\n  code: StatusCodesSchema,\n  message: z.string(),\n  details: z.any().optional(),\n});\n// then also change: genkit-tools/src/types/status.ts\n\nexport type Status = z.infer<typeof StatusSchema>;\n"], "names": ["StatusCodes"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,sBAAA,CAAA;AAAA,SAAA,qBAAA;IAAA,aAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,cAAA,IAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,IAAmB;AAKZ,IAAK,cAAL,aAAA,GAAA,CAAA,CAAKA,iBAAL;IAILA,YAAAA,CAAAA,YAAAA,CAAA,KAAA,GAAK,EAAA,GAAL;IAKAA,YAAAA,CAAAA,YAAAA,CAAA,YAAA,GAAY,EAAA,GAAZ;IASAA,YAAAA,CAAAA,YAAAA,CAAA,UAAA,GAAU,EAAA,GAAV;IAQAA,YAAAA,CAAAA,YAAAA,CAAA,mBAAA,GAAmB,EAAA,GAAnB;IASAA,YAAAA,CAAAA,YAAAA,CAAA,oBAAA,GAAoB,EAAA,GAApB;IAWAA,YAAAA,CAAAA,YAAAA,CAAA,YAAA,GAAY,EAAA,GAAZ;IAMAA,YAAAA,CAAAA,YAAAA,CAAA,iBAAA,GAAiB,EAAA,GAAjB;IAYAA,YAAAA,CAAAA,YAAAA,CAAA,oBAAA,GAAoB,EAAA,GAApB;IAMAA,YAAAA,CAAAA,YAAAA,CAAA,kBAAA,GAAkB,GAAA,GAAlB;IAMAA,YAAAA,CAAAA,YAAAA,CAAA,qBAAA,GAAqB,EAAA,GAArB;IAoBAA,YAAAA,CAAAA,YAAAA,CAAA,sBAAA,GAAsB,EAAA,GAAtB;IASAA,YAAAA,CAAAA,YAAAA,CAAA,UAAA,GAAU,GAAA,GAAV;IAmBAA,YAAAA,CAAAA,YAAAA,CAAA,eAAA,GAAe,GAAA,GAAf;IAMAA,YAAAA,CAAAA,YAAAA,CAAA,gBAAA,GAAgB,GAAA,GAAhB;IAOAA,YAAAA,CAAAA,YAAAA,CAAA,WAAA,GAAW,GAAA,GAAX;IAWAA,YAAAA,CAAAA,YAAAA,CAAA,cAAA,GAAc,GAAA,GAAd;IAKAA,YAAAA,CAAAA,YAAAA,CAAA,YAAA,GAAY,GAAA,GAAZ;IAzJU,OAAAA;AAAA,CAAA,EAAA,eAAA,CAAA;AA4JL,MAAM,mBAAmB,EAAE,IAAA,CAAK;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAM,gBAA4C;IAChD,IAAI;IACJ,WAAW;IACX,SAAS;IACT,kBAAkB;IAClB,mBAAmB;IACnB,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,SAAS;IACT,cAAc;IACd,eAAe;IACf,UAAU;IACV,aAAa;IACb,WAAW;AACb;AAEO,SAAS,eAAe,MAAA,EAA4B;IACzD,IAAI,CAAA,CAAE,UAAU,aAAA,GAAgB;QAC9B,MAAM,IAAI,MAAM,CAAA,oBAAA,EAAuB,MAAM,EAAE;IACjD;IACA,OAAO,aAAA,CAAc,MAAM,CAAA;AAC7B;AAEA,MAAM,oBAAoB,EAAE,UAAA,CAAW,WAAW;AAG3C,MAAM,eAAe,EAAE,MAAA,CAAO;IACnC,MAAM;IACN,SAAS,EAAE,MAAA,CAAO;IAClB,SAAS,EAAE,GAAA,CAAI,EAAE,QAAA,CAAS;AAC5B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/error.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Registry } from './registry.js';\nimport { httpStatusCode, type StatusName } from './statusTypes.js';\n\nexport type { StatusName };\n\nexport interface HttpErrorWireFormat {\n  details?: unknown;\n  message: string;\n  status: StatusName;\n}\n\n/**\n * Base error class for Genkit errors.\n */\nexport class GenkitError extends Error {\n  source?: string;\n  status: StatusName;\n  detail?: any;\n  code: number;\n\n  // For easy printing, we wrap the error with information like the source\n  // and status, but that's redundant with JSON.\n  originalMessage: string;\n\n  constructor({\n    status,\n    message,\n    detail,\n    source,\n  }: {\n    status: StatusName;\n    message: string;\n    detail?: any;\n    source?: string;\n  }) {\n    super(`${source ? `${source}: ` : ''}${status}: ${message}`);\n    this.originalMessage = message;\n    this.code = httpStatusCode(status);\n    this.status = status;\n    this.detail = detail;\n    this.name = 'GenkitError';\n  }\n\n  /**\n   * Returns a JSON-serializable representation of this object.\n   */\n  public toJSON(): HttpErrorWireFormat {\n    return {\n      // This error type is used by 3P authors with the field \"detail\",\n      // but the actual Callable protocol value is \"details\"\n      ...(this.detail === undefined ? {} : { details: this.detail }),\n      status: this.status,\n      message: this.originalMessage,\n    };\n  }\n}\n\nexport class UnstableApiError extends GenkitError {\n  constructor(level: 'beta', message?: string) {\n    super({\n      status: 'FAILED_PRECONDITION',\n      message: `${message ? message + ' ' : ''}This API requires '${level}' stability level.\\n\\nTo use this feature, initialize Genkit using \\`import {genkit} from \"genkit/${level}\"\\`.`,\n    });\n    this.name = 'UnstableApiError';\n  }\n}\n\n/**\n * assertUnstable allows features to raise exceptions when using Genkit from *more* stable initialized instances.\n *\n * @param level The maximum stability channel allowed.\n * @param message An optional message describing which feature is not allowed.\n */\nexport function assertUnstable(\n  registry: Registry,\n  level: 'beta',\n  message?: string\n) {\n  if (level === 'beta' && registry.apiStability === 'stable') {\n    throw new UnstableApiError(level, message);\n  }\n}\n\n/**\n * Creates a new class of Error for issues to be returned to users.\n * Using this error allows a web framework handler (e.g. express, next) to know it\n * is safe to return the message in a request. Other kinds of errors will\n * result in a generic 500 message to avoid the possibility of internal\n * exceptions being leaked to attackers.\n * In JSON requests, code will be an HTTP code and error will be a response body.\n * In streaming requests, { code, message } will be passed as the error message.\n */\nexport class UserFacingError extends GenkitError {\n  constructor(status: StatusName, message: string, details?: any) {\n    super({ status, detail: details, message });\n    super.name = 'UserFacingError';\n  }\n}\n\nexport function getHttpStatus(e: any): number {\n  if (e instanceof GenkitError) {\n    return e.code;\n  }\n  return 500;\n}\n\nexport function getCallableJSON(e: any): HttpErrorWireFormat {\n  if (e instanceof GenkitError) {\n    return e.toJSON();\n  }\n  return {\n    message: 'Internal Error',\n    status: 'INTERNAL',\n  };\n}\n\n/**\n * Extracts error message from the given error object, or if input is not an error then just turn the error into a string.\n */\nexport function getErrorMessage(e: any): string {\n  if (e instanceof Error) {\n    return e.message;\n  }\n  return `${e}`;\n}\n\n/**\n * Extracts stack trace from the given error object, or if input is not an error then returns undefined.\n */\nexport function getErrorStack(e: any): string | undefined {\n  if (e instanceof Error) {\n    return e.stack;\n  }\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,aAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,eAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAiBA,IAAA,qBAAgD;AAazC,MAAM,oBAAoB,MAAM;IACrC,OAAA;IACA,OAAA;IACA,OAAA;IACA,KAAA;IAAA,wEAAA;IAAA,8CAAA;IAIA,gBAAA;IAEA,YAAY,EACV,MAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,EACF,CAKG;QACD,KAAA,CAAM,GAAG,SAAS,GAAG,MAAM,CAAA,EAAA,CAAA,GAAO,EAAE,GAAG,MAAM,CAAA,EAAA,EAAK,OAAO,EAAE;QAC3D,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,IAAA,GAAA,CAAA,GAAO,mBAAA,cAAA,EAAe,MAAM;QACjC,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO;IACd;IAAA;;GAAA,GAKO,SAA8B;QACnC,OAAO;YAAA,iEAAA;YAAA,sDAAA;YAGL,GAAI,IAAA,CAAK,MAAA,KAAW,KAAA,IAAY,CAAC,IAAI;gBAAE,SAAS,IAAA,CAAK,MAAA;YAAO,CAAA;YAC5D,QAAQ,IAAA,CAAK,MAAA;YACb,SAAS,IAAA,CAAK,eAAA;QAChB;IACF;AACF;AAEO,MAAM,yBAAyB,YAAY;IAChD,YAAY,KAAA,EAAe,OAAA,CAAkB;QAC3C,KAAA,CAAM;YACJ,QAAQ;YACR,SAAS,GAAG,UAAU,UAAU,MAAM,EAAE,CAAA,mBAAA,EAAsB,KAAK,CAAA;;4EAAA,EAAqG,KAAK,CAAA,IAAA,CAAA;QAC/K,CAAC;QACD,IAAA,CAAK,IAAA,GAAO;IACd;AACF;AAQO,SAAS,eACd,QAAA,EACA,KAAA,EACA,OAAA,EACA;IACA,IAAI,UAAU,UAAU,SAAS,YAAA,KAAiB,UAAU;QAC1D,MAAM,IAAI,iBAAiB,OAAO,OAAO;IAC3C;AACF;AAWO,MAAM,wBAAwB,YAAY;IAC/C,YAAY,MAAA,EAAoB,OAAA,EAAiB,OAAA,CAAe;QAC9D,KAAA,CAAM;YAAE;YAAQ,QAAQ;YAAS;QAAQ,CAAC;QAC1C,KAAA,CAAM,OAAO;IACf;AACF;AAEO,SAAS,cAAc,CAAA,EAAgB;IAC5C,IAAI,aAAa,aAAa;QAC5B,OAAO,EAAE,IAAA;IACX;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,CAAA,EAA6B;IAC3D,IAAI,aAAa,aAAa;QAC5B,OAAO,EAAE,MAAA,CAAO;IAClB;IACA,OAAO;QACL,SAAS;QACT,QAAQ;IACV;AACF;AAKO,SAAS,gBAAgB,CAAA,EAAgB;IAC9C,IAAI,aAAa,OAAO;QACtB,OAAO,EAAE,OAAA;IACX;IACA,OAAO,GAAG,CAAC,EAAA;AACb;AAKO,SAAS,cAAc,CAAA,EAA4B;IACxD,IAAI,aAAa,OAAO;QACtB,OAAO,EAAE,KAAA;IACX;IACA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/context.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { runInActionRuntimeContext } from './action.js';\nimport { UserFacingError } from './error.js';\nimport type { HasRegistry, Registry } from './registry.js';\n\nconst contextAlsKey = 'core.auth.context';\n\n/**\n * Action side channel data, like auth and other invocation context infromation provided by the invoker.\n */\nexport interface ActionContext {\n  /** Information about the currently authenticated user if provided. */\n  auth?: Record<string, any>;\n  [additionalContext: string]: any;\n}\n\n/**\n * Execute the provided function in the runtime context. Call {@link getFlowContext()} anywhere\n * within the async call stack to retrieve the context. If context object is undefined, this function\n * is a no op passthrough, the function will be invoked as is.\n */\nexport function runWithContext<R>(\n  registry: Registry,\n  context: ActionContext | undefined,\n  fn: () => R\n): R {\n  if (context === undefined) {\n    return fn();\n  }\n  return registry.asyncStore.run(contextAlsKey, context, () =>\n    runInActionRuntimeContext(registry, fn)\n  );\n}\n\n/**\n * Gets the runtime context of the current flow.\n */\nexport function getContext(\n  registry: Registry | HasRegistry\n): ActionContext | undefined {\n  if ((registry as HasRegistry).registry) {\n    registry = (registry as HasRegistry).registry;\n  }\n  registry = registry as Registry;\n  return registry.asyncStore.getStore<ActionContext>(contextAlsKey);\n}\n\n/**\n * A universal type that request handling extensions (e.g. express, next) can map their request to.\n * This allows ContextProviders to build consistent interfacese on any web framework.\n * Headers must be lowercase to ensure portability.\n */\nexport interface RequestData<T = any> {\n  method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'OPTIONS' | 'QUERY';\n  headers: Record<string, string>;\n  input: T;\n}\n\n/**\n * Middleware can read request data and add information to the context that will\n * be passed to the Action. If middleware throws an error, that error will fail\n * the request and the Action will not be invoked. Expected cases should return a\n * UserFacingError, which allows the request handler to know what data is safe to\n * return to end users.\n *\n * Middleware can provide validation in addition to parsing. For example, an auth\n * middleware can have policies for validating auth in addition to passing auth context\n * to the Action.\n */\nexport type ContextProvider<\n  C extends ActionContext = ActionContext,\n  T = any,\n> = (request: RequestData<T>) => C | Promise<C>;\n\nexport interface ApiKeyContext extends ActionContext {\n  auth: {\n    apiKey: string | undefined;\n  };\n}\n\nexport function apiKey(\n  policy: (context: ApiKeyContext) => void | Promise<void>\n): ContextProvider<ApiKeyContext>;\nexport function apiKey(value?: string): ContextProvider<ApiKeyContext>;\nexport function apiKey(\n  valueOrPolicy?: ((context: ApiKeyContext) => void | Promise<void>) | string\n): ContextProvider<ApiKeyContext> {\n  return async (request: RequestData): Promise<ApiKeyContext> => {\n    const context: ApiKeyContext = {\n      auth: { apiKey: request.headers['authorization'] },\n    };\n    if (typeof valueOrPolicy === 'string') {\n      if (!context.auth?.apiKey) {\n        console.error('THROWING UNAUTHENTICATED');\n        throw new UserFacingError('UNAUTHENTICATED', 'Unauthenticated');\n      }\n      if (context.auth?.apiKey != valueOrPolicy) {\n        console.error('Throwing PERMISSION_DENIED');\n        throw new UserFacingError('PERMISSION_DENIED', 'Permission Denied');\n      }\n    } else if (typeof valueOrPolicy === 'function') {\n      await valueOrPolicy(context);\n    } else if (typeof valueOrPolicy !== 'undefined') {\n      throw new Error(\n        `Invalid type ${typeof valueOrPolicy} passed to apiKey()`\n      );\n    }\n    return context;\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,QAAA,IAAA;IAAA,YAAA,IAAA;IAAA,gBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,gBAA0C;AAC1C,IAAA,eAAgC;AAGhC,MAAM,gBAAgB;AAgBf,SAAS,eACd,QAAA,EACA,OAAA,EACA,EAAA,EACG;IACH,IAAI,YAAY,KAAA,GAAW;QACzB,OAAO,GAAG;IACZ;IACA,OAAO,SAAS,UAAA,CAAW,GAAA,CAAI,eAAe,SAAS,IAAA,CAAA,GACrD,cAAA,yBAAA,EAA0B,UAAU,EAAE;AAE1C;AAKO,SAAS,WACd,QAAA,EAC2B;IAC3B,IAAK,SAAyB,QAAA,EAAU;QACtC,WAAY,SAAyB,QAAA;IACvC;IACA,WAAW;IACX,OAAO,SAAS,UAAA,CAAW,QAAA,CAAwB,aAAa;AAClE;AAuCO,SAAS,OACd,aAAA,EACgC;IAChC,OAAO,OAAO,YAAiD;QAC7D,MAAM,UAAyB;YAC7B,MAAM;gBAAE,QAAQ,QAAQ,OAAA,CAAQ,eAAe,CAAA;YAAE;QACnD;QACA,IAAI,OAAO,kBAAkB,UAAU;YACrC,IAAI,CAAC,QAAQ,IAAA,EAAM,QAAQ;gBACzB,QAAQ,KAAA,CAAM,0BAA0B;gBACxC,MAAM,IAAI,aAAA,eAAA,CAAgB,mBAAmB,iBAAiB;YAChE;YACA,IAAI,QAAQ,IAAA,EAAM,UAAU,eAAe;gBACzC,QAAQ,KAAA,CAAM,4BAA4B;gBAC1C,MAAM,IAAI,aAAA,eAAA,CAAgB,qBAAqB,mBAAmB;YACpE;QACF,OAAA,IAAW,OAAO,kBAAkB,YAAY;YAC9C,MAAM,cAAc,OAAO;QAC7B,OAAA,IAAW,OAAO,kBAAkB,aAAa;YAC/C,MAAM,IAAI,MACR,CAAA,aAAA,EAAgB,OAAO,aAAa,CAAA,mBAAA,CAAA;QAExC;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/schema.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport Ajv, { type ErrorObject, type JSONSchemaType } from 'ajv';\nimport addFormats from 'ajv-formats';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { GenkitError } from './error.js';\nimport type { Registry } from './registry.js';\nconst ajv = new Ajv();\naddFormats(ajv);\n\nexport { z }; // provide a consistent zod to use throughout genkit\n\n/**\n * JSON schema.\n */\nexport type JSONSchema = JSONSchemaType<any> | any;\n\nconst jsonSchemas = new WeakMap<z.ZodTypeAny, JSONSchema>();\nconst validators = new WeakMap<JSONSchema, ReturnType<typeof ajv.compile>>();\n\n/**\n * Wrapper object for various ways schema can be provided.\n */\nexport interface ProvidedSchema {\n  jsonSchema?: JSONSchema;\n  schema?: z.ZodTypeAny;\n}\n\n/**\n * Schema validation error.\n */\nexport class ValidationError extends GenkitError {\n  constructor({\n    data,\n    errors,\n    schema,\n  }: {\n    data: any;\n    errors: ValidationErrorDetail[];\n    schema: JSONSchema;\n  }) {\n    super({\n      status: 'INVALID_ARGUMENT',\n      message: `Schema validation failed. Parse Errors:\\n\\n${errors.map((e) => `- ${e.path}: ${e.message}`).join('\\n')}\\n\\nProvided data:\\n\\n${JSON.stringify(data, null, 2)}\\n\\nRequired JSON schema:\\n\\n${JSON.stringify(schema, null, 2)}`,\n      detail: { errors, schema },\n    });\n  }\n}\n\n/**\n * Convertes a Zod schema into a JSON schema, utilizing an in-memory cache for known objects.\n * @param options Provide a json schema and/or zod schema. JSON schema has priority.\n * @returns A JSON schema.\n */\nexport function toJsonSchema({\n  jsonSchema,\n  schema,\n}: ProvidedSchema): JSONSchema | undefined {\n  // if neither jsonSchema or schema is present return undefined\n  if (!jsonSchema && !schema) return null;\n  if (jsonSchema) return jsonSchema;\n  if (jsonSchemas.has(schema!)) return jsonSchemas.get(schema!)!;\n  const outSchema = zodToJsonSchema(schema!, {\n    $refStrategy: 'none',\n    removeAdditionalStrategy: 'strict',\n  });\n  jsonSchemas.set(schema!, outSchema as JSONSchema);\n  return outSchema as JSONSchema;\n}\n\n/**\n * Schema validation error details.\n */\nexport interface ValidationErrorDetail {\n  path: string;\n  message: string;\n}\n\nfunction toErrorDetail(error: ErrorObject): ValidationErrorDetail {\n  return {\n    path: error.instancePath.substring(1).replace(/\\//g, '.') || '(root)',\n    message: error.message!,\n  };\n}\n\n/**\n * Validation response.\n */\nexport type ValidationResponse =\n  | { valid: true; errors: never }\n  | { valid: false; errors: ErrorObject[] };\n\n/**\n * Validates the provided data against the provided schema.\n */\nexport function validateSchema(\n  data: unknown,\n  options: ProvidedSchema\n): { valid: boolean; errors?: any[]; schema: JSONSchema } {\n  const toValidate = toJsonSchema(options);\n  if (!toValidate) {\n    return { valid: true, schema: toValidate };\n  }\n  const validator = validators.get(toValidate) || ajv.compile(toValidate);\n  const valid = validator(data) as boolean;\n  const errors = validator.errors?.map((e) => e);\n  return { valid, errors: errors?.map(toErrorDetail), schema: toValidate };\n}\n\n/**\n * Parses raw data object agaisnt the provided schema.\n */\nexport function parseSchema<T = unknown>(\n  data: unknown,\n  options: ProvidedSchema\n): T {\n  const { valid, errors, schema } = validateSchema(data, options);\n  if (!valid) throw new ValidationError({ data, errors: errors!, schema });\n  return data as T;\n}\n\n/**\n * Registers provided schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineSchema<T extends z.ZodTypeAny>(\n  registry: Registry,\n  name: string,\n  schema: T\n): T {\n  registry.registerSchema(name, { schema });\n  return schema;\n}\n\n/**\n * Registers provided JSON schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineJsonSchema(\n  registry: Registry,\n  name: string,\n  jsonSchema: JSONSchema\n) {\n  registry.registerSchema(name, { jsonSchema });\n  return jsonSchema;\n}\n"], "names": ["Ajv", "addFormats", "zodToJsonSchema"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,iBAAA,CAAA;AAAA,SAAA,gBAAA;IAAA,iBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,cAAA,IAAA;IAAA,aAAA,IAAA;IAAA,cAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,GAAA,IAAA,WAAA,CAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,aAA2D;AAC3D,IAAA,qBAAuB;AACvB,IAAA,aAAkB;AAClB,IAAA,4BAA4B;AAC5B,IAAA,eAA4B;AAE5B,MAAM,MAAM,IAAI,WAAAA,OAAAA,CAAI;AAAA,CAAA,GACpB,mBAAAC,OAAAA,EAAW,GAAG;AASd,MAAM,cAAc,aAAA,GAAA,IAAI,QAAkC;AAC1D,MAAM,aAAa,aAAA,GAAA,IAAI,QAAoD;AAapE,MAAM,wBAAwB,aAAA,WAAA,CAAY;IAC/C,YAAY,EACV,IAAA,EACA,MAAA,EACA,MAAA,EACF,CAIG;QACD,KAAA,CAAM;YACJ,QAAQ;YACR,SAAS,CAAA;;AAAA,EAA8C,OAAO,GAAA,CAAI,CAAC,IAAM,CAAA,EAAA,EAAK,EAAE,IAAI,CAAA,EAAA,EAAK,EAAE,OAAO,EAAE,EAAE,IAAA,CAAK,IAAI,CAAC,CAAA;;;;AAAA,EAAyB,KAAK,SAAA,CAAU,MAAM,MAAM,CAAC,CAAC,CAAA;;;;AAAA,EAAgC,KAAK,SAAA,CAAU,QAAQ,MAAM,CAAC,CAAC,EAAA;YACrO,QAAQ;gBAAE;gBAAQ;YAAO;QAC3B,CAAC;IACH;AACF;AAOO,SAAS,aAAa,EAC3B,UAAA,EACA,MAAA,EACF,EAA2C;IAEzC,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAA,OAAO;IACnC,IAAI,WAAY,CAAA,OAAO;IACvB,IAAI,YAAY,GAAA,CAAI,MAAO,EAAG,CAAA,OAAO,YAAY,GAAA,CAAI,MAAO;IAC5D,MAAM,YAAA,CAAA,GAAY,0BAAAC,OAAAA,EAAgB,QAAS;QACzC,cAAc;QACd,0BAA0B;IAC5B,CAAC;IACD,YAAY,GAAA,CAAI,QAAS,SAAuB;IAChD,OAAO;AACT;AAUA,SAAS,cAAc,KAAA,EAA2C;IAChE,OAAO;QACL,MAAM,MAAM,YAAA,CAAa,SAAA,CAAU,CAAC,EAAE,OAAA,CAAQ,OAAO,GAAG,KAAK;QAC7D,SAAS,MAAM,OAAA;IACjB;AACF;AAYO,SAAS,eACd,IAAA,EACA,OAAA,EACwD;IACxD,MAAM,aAAa,aAAa,OAAO;IACvC,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAW;IAC3C;IACA,MAAM,YAAY,WAAW,GAAA,CAAI,UAAU,KAAK,IAAI,OAAA,CAAQ,UAAU;IACtE,MAAM,QAAQ,UAAU,IAAI;IAC5B,MAAM,SAAS,UAAU,MAAA,EAAQ,IAAI,CAAC,IAAM,CAAC;IAC7C,OAAO;QAAE;QAAO,QAAQ,QAAQ,IAAI,aAAa;QAAG,QAAQ;IAAW;AACzE;AAKO,SAAS,YACd,IAAA,EACA,OAAA,EACG;IACH,MAAM,EAAE,KAAA,EAAO,MAAA,EAAQ,MAAA,CAAO,CAAA,GAAI,eAAe,MAAM,OAAO;IAC9D,IAAI,CAAC,MAAO,CAAA,MAAM,IAAI,gBAAgB;QAAE;QAAM;QAAiB;IAAO,CAAC;IACvE,OAAO;AACT;AAOO,SAAS,aACd,QAAA,EACA,IAAA,EACA,MAAA,EACG;IACH,SAAS,cAAA,CAAe,MAAM;QAAE;IAAO,CAAC;IACxC,OAAO;AACT;AAOO,SAAS,iBACd,QAAA,EACA,IAAA,EACA,UAAA,EACA;IACA,SAAS,cAAA,CAAe,MAAM;QAAE;IAAW,CAAC;IAC5C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/logging.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\n\nconst loggerKey = '__genkit_logger';\n\nconst _defaultLogger = {\n  shouldLog(targetLevel: string) {\n    return LOG_LEVELS.indexOf(this.level) <= LOG_LEVELS.indexOf(targetLevel);\n  },\n  debug(...args: any) {\n    this.shouldLog('debug') && console.debug(...args);\n  },\n  info(...args: any) {\n    this.shouldLog('info') && console.info(...args);\n  },\n  warn(...args: any) {\n    this.shouldLog('warn') && console.warn(...args);\n  },\n  error(...args: any) {\n    this.shouldLog('error') && console.error(...args);\n  },\n  level: 'info',\n};\n\nfunction getLogger() {\n  if (!global[loggerKey]) {\n    global[loggerKey] = _defaultLogger;\n  }\n  return global[loggerKey];\n}\n\nclass Logger {\n  readonly defaultLogger = _defaultLogger;\n\n  init(fn: any) {\n    global[loggerKey] = fn;\n  }\n\n  info(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().info.apply(getLogger(), args);\n  }\n  debug(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().debug.apply(getLogger(), args);\n  }\n  error(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().error.apply(getLogger(), args);\n  }\n  warn(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().warn.apply(getLogger(), args);\n  }\n\n  setLogLevel(level: 'error' | 'warn' | 'info' | 'debug') {\n    getLogger().level = level;\n  }\n\n  logStructured(msg: string, metadata: any) {\n    getLogger().info(msg, metadata);\n  }\n\n  logStructuredError(msg: string, metadata: any) {\n    getLogger().error(msg, metadata);\n  }\n}\n\n/**\n * Genkit logger.\n *\n * ```ts\n * import { logger } from 'genkit/logging';\n *\n * logger.setLogLevel('debug');\n * ```\n */\nexport const logger = new Logger();\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,QAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,MAAM,aAAa;IAAC;IAAS;IAAQ;IAAQ,OAAO;CAAA;AAEpD,MAAM,YAAY;AAElB,MAAM,iBAAiB;IACrB,WAAU,WAAA,EAAqB;QAC7B,OAAO,WAAW,OAAA,CAAQ,IAAA,CAAK,KAAK,KAAK,WAAW,OAAA,CAAQ,WAAW;IACzE;IACA,OAAA,GAAS,IAAA,EAAW;QAClB,IAAA,CAAK,SAAA,CAAU,OAAO,KAAK,QAAQ,KAAA,CAAM,GAAG,IAAI;IAClD;IACA,MAAA,GAAQ,IAAA,EAAW;QACjB,IAAA,CAAK,SAAA,CAAU,MAAM,KAAK,QAAQ,IAAA,CAAK,GAAG,IAAI;IAChD;IACA,MAAA,GAAQ,IAAA,EAAW;QACjB,IAAA,CAAK,SAAA,CAAU,MAAM,KAAK,QAAQ,IAAA,CAAK,GAAG,IAAI;IAChD;IACA,OAAA,GAAS,IAAA,EAAW;QAClB,IAAA,CAAK,SAAA,CAAU,OAAO,KAAK,QAAQ,KAAA,CAAM,GAAG,IAAI;IAClD;IACA,OAAO;AACT;AAEA,SAAS,YAAY;IACnB,IAAI,CAAC,MAAA,CAAO,SAAS,CAAA,EAAG;QACtB,MAAA,CAAO,SAAS,CAAA,GAAI;IACtB;IACA,OAAO,MAAA,CAAO,SAAS,CAAA;AACzB;AAEA,MAAM,OAAO;IACF,gBAAgB,eAAA;IAEzB,KAAK,EAAA,EAAS;QACZ,MAAA,CAAO,SAAS,CAAA,GAAI;IACtB;IAEA,KAAA,GAAQ,IAAA,EAAW;QAEjB,UAAU,EAAE,IAAA,CAAK,KAAA,CAAM,UAAU,GAAG,IAAI;IAC1C;IACA,MAAA,GAAS,IAAA,EAAW;QAElB,UAAU,EAAE,KAAA,CAAM,KAAA,CAAM,UAAU,GAAG,IAAI;IAC3C;IACA,MAAA,GAAS,IAAA,EAAW;QAElB,UAAU,EAAE,KAAA,CAAM,KAAA,CAAM,UAAU,GAAG,IAAI;IAC3C;IACA,KAAA,GAAQ,IAAA,EAAW;QAEjB,UAAU,EAAE,IAAA,CAAK,KAAA,CAAM,UAAU,GAAG,IAAI;IAC1C;IAEA,YAAY,KAAA,EAA4C;QACtD,UAAU,EAAE,KAAA,GAAQ;IACtB;IAEA,cAAc,GAAA,EAAa,QAAA,EAAe;QACxC,UAAU,EAAE,IAAA,CAAK,KAAK,QAAQ;IAChC;IAEA,mBAAmB,GAAA,EAAa,QAAA,EAAe;QAC7C,UAAU,EAAE,KAAA,CAAM,KAAK,QAAQ;IACjC;AACF;AAWO,MAAM,SAAS,IAAI,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/utils.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Deletes any properties with `undefined` values in the provided object.\n * Modifies the provided object.\n */\nexport function deleteUndefinedProps(obj: any) {\n  for (const prop in obj) {\n    if (obj[prop] === undefined) {\n      delete obj[prop];\n    } else {\n      if (typeof obj[prop] === 'object') {\n        deleteUndefinedProps(obj[prop]);\n      }\n    }\n  }\n}\n\n/**\n * Strips (non distructively) any properties with `undefined` values in the provided object and returns\n */\nexport function stripUndefinedProps<T>(input: T): T {\n  if (\n    input === undefined ||\n    input === null ||\n    Array.isArray(input) ||\n    typeof input !== 'object'\n  ) {\n    return input;\n  }\n  const out = {} as T;\n  for (const key in input) {\n    if (input[key] !== undefined) {\n      out[key] = stripUndefinedProps(input[key]);\n    }\n  }\n  return out;\n}\n\n/**\n * Returns the current environment that the app code is running in.\n *\n * @hidden\n */\nexport function getCurrentEnv(): string {\n  return process.env.GENKIT_ENV || 'prod';\n}\n\n/**\n * Whether the current environment is `dev`.\n */\nexport function isDevEnv(): boolean {\n  return getCurrentEnv() === 'dev';\n}\n\n/**\n * Adds flow-specific prefix for OpenTelemetry span attributes.\n */\nexport function featureMetadataPrefix(name: string) {\n  return `feature:${name}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,sBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,UAAA,IAAA;IAAA,qBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAoBO,SAAS,qBAAqB,GAAA,EAAU;IAC7C,IAAA,MAAW,QAAQ,IAAK;QACtB,IAAI,GAAA,CAAI,IAAI,CAAA,KAAM,KAAA,GAAW;YAC3B,OAAO,GAAA,CAAI,IAAI,CAAA;QACjB,OAAO;YACL,IAAI,OAAO,GAAA,CAAI,IAAI,CAAA,KAAM,UAAU;gBACjC,qBAAqB,GAAA,CAAI,IAAI,CAAC;YAChC;QACF;IACF;AACF;AAKO,SAAS,oBAAuB,KAAA,EAAa;IAClD,IACE,UAAU,KAAA,KACV,UAAU,QACV,MAAM,OAAA,CAAQ,KAAK,KACnB,OAAO,UAAU,UACjB;QACA,OAAO;IACT;IACA,MAAM,MAAM,CAAC;IACb,IAAA,MAAW,OAAO,MAAO;QACvB,IAAI,KAAA,CAAM,GAAG,CAAA,KAAM,KAAA,GAAW;YAC5B,GAAA,CAAI,GAAG,CAAA,GAAI,oBAAoB,KAAA,CAAM,GAAG,CAAC;QAC3C;IACF;IACA,OAAO;AACT;AAOO,SAAS,gBAAwB;IACtC,OAAO,QAAQ,GAAA,CAAI,UAAA,IAAc;AACnC;AAKO,SAAS,WAAoB;IAClC,OAAO,cAAc,MAAM;AAC7B;AAKO,SAAS,sBAAsB,IAAA,EAAc;IAClD,OAAO,CAAA,QAAA,EAAW,IAAI,EAAA;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/tracing/exporter.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanKind, type HrTime } from '@opentelemetry/api';\nimport {\n  ExportResultCode,\n  hrTimeToMilliseconds,\n  type ExportResult,\n} from '@opentelemetry/core';\nimport type { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { logger } from '../logging.js';\nimport { deleteUndefinedProps } from '../utils.js';\nimport type { SpanData, TraceData } from './types.js';\n\nexport let telemetryServerUrl: string | undefined;\n\n/**\n * @hidden\n */\nexport function setTelemetryServerUrl(url: string) {\n  telemetryServerUrl = url;\n}\n\n/**\n * Exports collected OpenTelemetetry spans to the telemetry server.\n */\nexport class TraceServerExporter implements SpanExporter {\n  /**\n   * Export spans.\n   * @param spans\n   * @param resultCallback\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    this._sendSpans(spans, resultCallback);\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  shutdown(): Promise<void> {\n    this._sendSpans([]);\n    return this.forceFlush();\n  }\n\n  /**\n   * Converts span info into trace store format.\n   * @param span\n   */\n  private _exportInfo(span: ReadableSpan): SpanData {\n    const spanData: Partial<SpanData> = {\n      spanId: span.spanContext().spanId,\n      traceId: span.spanContext().traceId,\n      startTime: transformTime(span.startTime),\n      endTime: transformTime(span.endTime),\n      attributes: { ...span.attributes },\n      displayName: span.name,\n      links: span.links,\n      spanKind: SpanKind[span.kind],\n      parentSpanId: span.parentSpanId,\n      sameProcessAsParentSpan: { value: !span.spanContext().isRemote },\n      status: span.status,\n      timeEvents: {\n        timeEvent: span.events.map((e) => ({\n          time: transformTime(e.time),\n          annotation: {\n            attributes: e.attributes ?? {},\n            description: e.name,\n          },\n        })),\n      },\n    };\n    if (span.instrumentationLibrary !== undefined) {\n      spanData.instrumentationLibrary = {\n        name: span.instrumentationLibrary.name,\n      };\n      if (span.instrumentationLibrary.schemaUrl !== undefined) {\n        spanData.instrumentationLibrary.schemaUrl =\n          span.instrumentationLibrary.schemaUrl;\n      }\n      if (span.instrumentationLibrary.version !== undefined) {\n        spanData.instrumentationLibrary.version =\n          span.instrumentationLibrary.version;\n      }\n    }\n    deleteUndefinedProps(spanData);\n    return spanData as SpanData;\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  private async _sendSpans(\n    spans: ReadableSpan[],\n    done?: (result: ExportResult) => void\n  ): Promise<void> {\n    const traces = {} as Record<string, ReadableSpan[]>;\n    for (const span of spans) {\n      if (!traces[span.spanContext().traceId]) {\n        traces[span.spanContext().traceId] = [];\n      }\n      traces[span.spanContext().traceId].push(span);\n    }\n    let error = false;\n    for (const traceId of Object.keys(traces)) {\n      try {\n        await this.save(traceId, traces[traceId]);\n      } catch (e) {\n        error = true;\n        logger.error(`Failed to save trace ${traceId}`, e);\n      }\n      if (done) {\n        return done({\n          code: error ? ExportResultCode.FAILED : ExportResultCode.SUCCESS,\n        });\n      }\n    }\n  }\n\n  private async save(traceId, spans: ReadableSpan[]): Promise<void> {\n    if (!telemetryServerUrl) {\n      logger.debug(\n        `Telemetry server is not configured, trace ${traceId} not saved!`\n      );\n      return;\n    }\n    // TODO: add interface for Firestore doc\n    const data = {\n      traceId,\n      spans: {},\n    } as TraceData;\n    for (const span of spans) {\n      const convertedSpan = this._exportInfo(span);\n      data.spans[convertedSpan.spanId] = convertedSpan;\n      if (!convertedSpan.parentSpanId) {\n        data.displayName = convertedSpan.displayName;\n        data.startTime = convertedSpan.startTime;\n        data.endTime = convertedSpan.endTime;\n      }\n    }\n    await fetch(`${telemetryServerUrl}/api/traces`, {\n      method: 'POST',\n      headers: {\n        Accept: 'application/json',\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n  }\n}\n\n// Converts an HrTime to milliseconds.\nfunction transformTime(time: HrTime) {\n  return hrTimeToMilliseconds(time);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,qBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,oBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,aAAsC;AACtC,IAAA,cAIO;AAEP,IAAA,iBAAuB;AACvB,IAAA,eAAqC;AAG9B,IAAI;AAKJ,SAAS,sBAAsB,GAAA,EAAa;IACjD,qBAAqB;AACvB;AAKO,MAAM,oBAA4C;IAAA;;;;GAAA,GAMvD,OACE,KAAA,EACA,cAAA,EACM;QACN,IAAA,CAAK,UAAA,CAAW,OAAO,cAAc;IACvC;IAAA;;GAAA,GAKA,WAA0B;QACxB,IAAA,CAAK,UAAA,CAAW,CAAC,CAAC;QAClB,OAAO,IAAA,CAAK,UAAA,CAAW;IACzB;IAAA;;;GAAA,GAMQ,YAAY,IAAA,EAA8B;QAChD,MAAM,WAA8B;YAClC,QAAQ,KAAK,WAAA,CAAY,EAAE,MAAA;YAC3B,SAAS,KAAK,WAAA,CAAY,EAAE,OAAA;YAC5B,WAAW,cAAc,KAAK,SAAS;YACvC,SAAS,cAAc,KAAK,OAAO;YACnC,YAAY;gBAAE,GAAG,KAAK,UAAA;YAAW;YACjC,aAAa,KAAK,IAAA;YAClB,OAAO,KAAK,KAAA;YACZ,UAAU,WAAA,QAAA,CAAS,KAAK,IAAI,CAAA;YAC5B,cAAc,KAAK,YAAA;YACnB,yBAAyB;gBAAE,OAAO,CAAC,KAAK,WAAA,CAAY,EAAE,QAAA;YAAS;YAC/D,QAAQ,KAAK,MAAA;YACb,YAAY;gBACV,WAAW,KAAK,MAAA,CAAO,GAAA,CAAI,CAAC,IAAA,CAAO;wBACjC,MAAM,cAAc,EAAE,IAAI;wBAC1B,YAAY;4BACV,YAAY,EAAE,UAAA,IAAc,CAAC;4BAC7B,aAAa,EAAE,IAAA;wBACjB;oBACF,CAAA,CAAE;YACJ;QACF;QACA,IAAI,KAAK,sBAAA,KAA2B,KAAA,GAAW;YAC7C,SAAS,sBAAA,GAAyB;gBAChC,MAAM,KAAK,sBAAA,CAAuB,IAAA;YACpC;YACA,IAAI,KAAK,sBAAA,CAAuB,SAAA,KAAc,KAAA,GAAW;gBACvD,SAAS,sBAAA,CAAuB,SAAA,GAC9B,KAAK,sBAAA,CAAuB,SAAA;YAChC;YACA,IAAI,KAAK,sBAAA,CAAuB,OAAA,KAAY,KAAA,GAAW;gBACrD,SAAS,sBAAA,CAAuB,OAAA,GAC9B,KAAK,sBAAA,CAAuB,OAAA;YAChC;QACF;QACA,CAAA,GAAA,aAAA,oBAAA,EAAqB,QAAQ;QAC7B,OAAO;IACT;IAAA;;GAAA,GAKA,aAA4B;QAC1B,OAAO,QAAQ,OAAA,CAAQ;IACzB;IAEA,MAAc,WACZ,KAAA,EACA,IAAA,EACe;QACf,MAAM,SAAS,CAAC;QAChB,KAAA,MAAW,QAAQ,MAAO;YACxB,IAAI,CAAC,MAAA,CAAO,KAAK,WAAA,CAAY,EAAE,OAAO,CAAA,EAAG;gBACvC,MAAA,CAAO,KAAK,WAAA,CAAY,EAAE,OAAO,CAAA,GAAI,CAAC,CAAA;YACxC;YACA,MAAA,CAAO,KAAK,WAAA,CAAY,EAAE,OAAO,CAAA,CAAE,IAAA,CAAK,IAAI;QAC9C;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,WAAW,OAAO,IAAA,CAAK,MAAM,EAAG;YACzC,IAAI;gBACF,MAAM,IAAA,CAAK,IAAA,CAAK,SAAS,MAAA,CAAO,OAAO,CAAC;YAC1C,EAAA,OAAS,GAAG;gBACV,QAAQ;gBACR,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,qBAAA,EAAwB,OAAO,EAAA,EAAI,CAAC;YACnD;YACA,IAAI,MAAM;gBACR,OAAO,KAAK;oBACV,MAAM,QAAQ,YAAA,gBAAA,CAAiB,MAAA,GAAS,YAAA,gBAAA,CAAiB,OAAA;gBAC3D,CAAC;YACH;QACF;IACF;IAEA,MAAc,KAAK,OAAA,EAAS,KAAA,EAAsC;QAChE,IAAI,CAAC,oBAAoB;YACvB,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,0CAAA,EAA6C,OAAO,CAAA,WAAA,CAAA;YAEtD;QACF;QAEA,MAAM,OAAO;YACX;YACA,OAAO,CAAC;QACV;QACA,KAAA,MAAW,QAAQ,MAAO;YACxB,MAAM,gBAAgB,IAAA,CAAK,WAAA,CAAY,IAAI;YAC3C,KAAK,KAAA,CAAM,cAAc,MAAM,CAAA,GAAI;YACnC,IAAI,CAAC,cAAc,YAAA,EAAc;gBAC/B,KAAK,WAAA,GAAc,cAAc,WAAA;gBACjC,KAAK,SAAA,GAAY,cAAc,SAAA;gBAC/B,KAAK,OAAA,GAAU,cAAc,OAAA;YAC/B;QACF;QACA,MAAM,MAAM,GAAG,kBAAkB,CAAA,WAAA,CAAA,EAAe;YAC9C,QAAQ;YACR,SAAS;gBACP,QAAQ;gBACR,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAA,CAAU,IAAI;QAC3B,CAAC;IACH;AACF;AAGA,SAAS,cAAc,IAAA,EAAc;IACnC,OAAA,CAAA,GAAO,YAAA,oBAAA,EAAqB,IAAI;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/tracing/instrumentation.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ROOT_CONTEXT,\n  SpanStatusCode,\n  trace,\n  type Span as ApiSpan,\n  type Link,\n} from '@opentelemetry/api';\nimport { performance } from 'node:perf_hooks';\nimport type { HasRegistry, Registry } from '../registry.js';\nimport { ensureBasicTelemetryInstrumentation } from '../tracing.js';\nimport type { PathMetadata, SpanMetadata, TraceMetadata } from './types.js';\n\nexport const spanMetadataAlsKey = 'core.tracing.instrumentation.span';\nexport const traceMetadataAlsKey = 'core.tracing.instrumentation.trace';\n\nexport const ATTR_PREFIX = 'genkit';\n/** @hidden */\nexport const SPAN_TYPE_ATTR = ATTR_PREFIX + ':type';\nconst TRACER_NAME = 'genkit-tracer';\nconst TRACER_VERSION = 'v1';\n\n/**\n * @hidden\n */\nexport async function newTrace<T>(\n  registry: Registry | HasRegistry,\n  opts: {\n    name: string;\n    labels?: Record<string, string>;\n    links?: Link[];\n  },\n  fn: (metadata: SpanMetadata, rootSpan: ApiSpan) => Promise<T>\n) {\n  registry = (registry as HasRegistry).registry\n    ? (registry as HasRegistry).registry\n    : (registry as Registry);\n\n  await ensureBasicTelemetryInstrumentation();\n  const traceMetadata: TraceMetadata = registry.asyncStore.getStore(\n    traceMetadataAlsKey\n  ) || {\n    paths: new Set<PathMetadata>(),\n    timestamp: performance.now(),\n    featureName: opts.name,\n  };\n  return await registry.asyncStore.run(traceMetadataAlsKey, traceMetadata, () =>\n    runInNewSpan(\n      registry,\n      {\n        metadata: {\n          name: opts.name,\n        },\n        labels: opts.labels,\n        links: opts.links,\n      },\n      async (metadata, otSpan) => {\n        return await fn(metadata, otSpan);\n      }\n    )\n  );\n}\n\n/**\n * Runs the provided function in a new span.\n *\n * @hidden\n */\nexport async function runInNewSpan<T>(\n  registry: Registry | HasRegistry,\n  opts: {\n    metadata: SpanMetadata;\n    labels?: Record<string, string>;\n    links?: Link[];\n  },\n  fn: (metadata: SpanMetadata, otSpan: ApiSpan, isRoot: boolean) => Promise<T>\n): Promise<T> {\n  await ensureBasicTelemetryInstrumentation();\n  const resolvedRegistry = (registry as HasRegistry).registry\n    ? (registry as HasRegistry).registry\n    : (registry as Registry);\n\n  const tracer = trace.getTracer(TRACER_NAME, TRACER_VERSION);\n  const parentStep =\n    resolvedRegistry.asyncStore.getStore<SpanMetadata>(spanMetadataAlsKey);\n  const isInRoot = parentStep?.isRoot === true;\n  if (!parentStep) opts.metadata.isRoot ||= true;\n  return await tracer.startActiveSpan(\n    opts.metadata.name,\n    { links: opts.links, root: opts.metadata.isRoot },\n    async (otSpan) => {\n      if (opts.labels) otSpan.setAttributes(opts.labels);\n      try {\n        opts.metadata.path = buildPath(\n          opts.metadata.name,\n          parentStep?.path || '',\n          opts.labels\n        );\n\n        const output = await resolvedRegistry.asyncStore.run(\n          spanMetadataAlsKey,\n          opts.metadata,\n          () => fn(opts.metadata, otSpan, isInRoot)\n        );\n        if (opts.metadata.state !== 'error') {\n          opts.metadata.state = 'success';\n        }\n\n        recordPath(resolvedRegistry, opts.metadata);\n        return output;\n      } catch (e) {\n        recordPath(resolvedRegistry, opts.metadata, e);\n        opts.metadata.state = 'error';\n        otSpan.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: getErrorMessage(e),\n        });\n        if (e instanceof Error) {\n          otSpan.recordException(e);\n        }\n\n        // Mark the first failing span as the source of failure. Prevent parent\n        // spans that catch re-thrown exceptions from also claiming to be the\n        // source.\n        if (typeof e === 'object') {\n          if (!(e as any).ignoreFailedSpan) {\n            opts.metadata.isFailureSource = true;\n          }\n          (e as any).ignoreFailedSpan = true;\n        }\n\n        throw e;\n      } finally {\n        otSpan.setAttributes(metadataToAttributes(opts.metadata));\n        otSpan.end();\n      }\n    }\n  );\n}\n\n/**\n * Creates a new child span and attaches it to a previously created trace. This\n * is useful, for example, for adding deferred user engagement metadata.\n *\n * @hidden\n */\nexport async function appendSpan(\n  traceId: string,\n  parentSpanId: string,\n  metadata: SpanMetadata,\n  labels?: Record<string, string>\n) {\n  await ensureBasicTelemetryInstrumentation();\n\n  const tracer = trace.getTracer(TRACER_NAME, TRACER_VERSION);\n\n  const spanContext = trace.setSpanContext(ROOT_CONTEXT, {\n    traceId: traceId,\n    traceFlags: 1, // sampled\n    spanId: parentSpanId,\n  });\n\n  // TODO(abrook): add explicit start time to align with parent\n  const span = tracer.startSpan(metadata.name, {}, spanContext);\n  span.setAttributes(metadataToAttributes(metadata));\n  if (labels) {\n    span.setAttributes(labels);\n  }\n  span.end();\n}\n\nfunction getErrorMessage(e: any): string {\n  if (e instanceof Error) {\n    return e.message;\n  }\n  return `${e}`;\n}\n\nfunction metadataToAttributes(metadata: SpanMetadata): Record<string, string> {\n  const out = {} as Record<string, string>;\n  Object.keys(metadata).forEach((key) => {\n    if (\n      key === 'metadata' &&\n      typeof metadata[key] === 'object' &&\n      metadata.metadata\n    ) {\n      Object.entries(metadata.metadata).forEach(([metaKey, value]) => {\n        out[ATTR_PREFIX + ':metadata:' + metaKey] = value;\n      });\n    } else if (key === 'input' || typeof metadata[key] === 'object') {\n      out[ATTR_PREFIX + ':' + key] = JSON.stringify(metadata[key]);\n    } else {\n      out[ATTR_PREFIX + ':' + key] = metadata[key];\n    }\n  });\n  return out;\n}\n\n/**\n * Sets provided attribute value in the current span.\n *\n * @hidden\n */\nexport function setCustomMetadataAttribute(\n  registry: Registry,\n  key: string,\n  value: string\n) {\n  const currentStep = getCurrentSpan(registry);\n  if (!currentStep) {\n    return;\n  }\n  if (!currentStep.metadata) {\n    currentStep.metadata = {};\n  }\n  currentStep.metadata[key] = value;\n}\n\n/**\n * Sets provided attribute values in the current span.\n *\n * @hidden\n */\nexport function setCustomMetadataAttributes(\n  registry: Registry,\n  values: Record<string, string>\n) {\n  const currentStep = getCurrentSpan(registry);\n  if (!currentStep) {\n    return;\n  }\n  if (!currentStep.metadata) {\n    currentStep.metadata = {};\n  }\n  for (const [key, value] of Object.entries(values)) {\n    currentStep.metadata[key] = value;\n  }\n}\n\n/**\n * Converts a fully annotated path to a friendly display version for logs\n *\n * @hidden\n */\nexport function toDisplayPath(path: string): string {\n  const pathPartRegex = /\\{([^\\,}]+),[^\\}]+\\}/g;\n  return Array.from(path.matchAll(pathPartRegex), (m) => m[1]).join(' > ');\n}\n\nfunction getCurrentSpan(registry: Registry): SpanMetadata {\n  const step = registry.asyncStore.getStore<SpanMetadata>(spanMetadataAlsKey);\n  if (!step) {\n    throw new Error('running outside step context');\n  }\n  return step;\n}\n\nfunction buildPath(\n  name: string,\n  parentPath: string,\n  labels?: Record<string, string>\n) {\n  const stepType =\n    labels && labels['genkit:type']\n      ? `,t:${labels['genkit:metadata:subtype'] === 'flow' ? 'flow' : labels['genkit:type']}`\n      : '';\n  return parentPath + `/{${name}${stepType}}`;\n}\n\nfunction recordPath(registry: Registry, spanMeta: SpanMetadata, err?: any) {\n  const path = spanMeta.path || '';\n  const decoratedPath = decoratePathWithSubtype(spanMeta);\n  // Only add the path if a child has not already been added. In the event that\n  // an error is rethrown, we don't want to add each step in the unwind.\n  const paths = Array.from(\n    registry.asyncStore.getStore<TraceMetadata>(traceMetadataAlsKey)?.paths ||\n      new Set<PathMetadata>()\n  );\n  const status = err ? 'failure' : 'success';\n  if (!paths.some((p) => p.path.startsWith(path) && p.status === status)) {\n    const now = performance.now();\n    const start =\n      registry.asyncStore.getStore<TraceMetadata>(traceMetadataAlsKey)\n        ?.timestamp || now;\n    registry.asyncStore\n      .getStore<TraceMetadata>(traceMetadataAlsKey)\n      ?.paths?.add({\n        path: decoratedPath,\n        error: err?.name,\n        latency: now - start,\n        status,\n      });\n  }\n  spanMeta.path = decoratedPath;\n}\n\nfunction decoratePathWithSubtype(metadata: SpanMetadata): string {\n  if (!metadata.path) {\n    return '';\n  }\n\n  const pathComponents = metadata.path.split('}/{');\n\n  if (pathComponents.length == 1) {\n    return metadata.path;\n  }\n\n  const stepSubtype =\n    metadata.metadata && metadata.metadata['subtype']\n      ? `,s:${metadata.metadata['subtype']}`\n      : '';\n  const root = `${pathComponents.slice(0, -1).join('}/{')}}/`;\n  const decoratedStep = `{${pathComponents.at(-1)?.slice(0, -1)}${stepSubtype}}`;\n  return root + decoratedStep;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,0BAAA,CAAA;AAAA,SAAA,yBAAA;IAAA,aAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,YAAA,IAAA;IAAA,UAAA,IAAA;IAAA,cAAA,IAAA;IAAA,4BAAA,IAAA;IAAA,6BAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,eAAA,IAAA;IAAA,qBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,aAMO;AACP,IAAA,yBAA4B;AAE5B,IAAA,iBAAoD;AAG7C,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAE5B,MAAM,cAAc;AAEpB,MAAM,iBAAiB,cAAc;AAC5C,MAAM,cAAc;AACpB,MAAM,iBAAiB;AAKvB,eAAsB,SACpB,QAAA,EACA,IAAA,EAKA,EAAA,EACA;IACA,WAAY,SAAyB,QAAA,GAChC,SAAyB,QAAA,GACzB;IAEL,MAAA,CAAA,GAAM,eAAA,mCAAA,EAAoC;IAC1C,MAAM,gBAA+B,SAAS,UAAA,CAAW,QAAA,CACvD,wBACG;QACH,OAAO,aAAA,GAAA,IAAI,IAAkB;QAC7B,WAAW,uBAAA,WAAA,CAAY,GAAA,CAAI;QAC3B,aAAa,KAAK,IAAA;IACpB;IACA,OAAO,MAAM,SAAS,UAAA,CAAW,GAAA,CAAI,qBAAqB,eAAe,IACvE,aACE,UACA;YACE,UAAU;gBACR,MAAM,KAAK,IAAA;YACb;YACA,QAAQ,KAAK,MAAA;YACb,OAAO,KAAK,KAAA;QACd,GACA,OAAO,UAAU,WAAW;YAC1B,OAAO,MAAM,GAAG,UAAU,MAAM;QAClC;AAGN;AAOA,eAAsB,aACpB,QAAA,EACA,IAAA,EAKA,EAAA,EACY;IACZ,MAAA,CAAA,GAAM,eAAA,mCAAA,EAAoC;IAC1C,MAAM,mBAAoB,SAAyB,QAAA,GAC9C,SAAyB,QAAA,GACzB;IAEL,MAAM,SAAS,WAAA,KAAA,CAAM,SAAA,CAAU,aAAa,cAAc;IAC1D,MAAM,aACJ,iBAAiB,UAAA,CAAW,QAAA,CAAuB,kBAAkB;IACvE,MAAM,WAAW,YAAY,WAAW;IACxC,IAAI,CAAC,WAAY,CAAA,KAAK,QAAA,CAAS,MAAA,KAAW;IAC1C,OAAO,MAAM,OAAO,eAAA,CAClB,KAAK,QAAA,CAAS,IAAA,EACd;QAAE,OAAO,KAAK,KAAA;QAAO,MAAM,KAAK,QAAA,CAAS,MAAA;IAAO,GAChD,OAAO,WAAW;QAChB,IAAI,KAAK,MAAA,CAAQ,CAAA,OAAO,aAAA,CAAc,KAAK,MAAM;QACjD,IAAI;YACF,KAAK,QAAA,CAAS,IAAA,GAAO,UACnB,KAAK,QAAA,CAAS,IAAA,EACd,YAAY,QAAQ,IACpB,KAAK,MAAA;YAGP,MAAM,SAAS,MAAM,iBAAiB,UAAA,CAAW,GAAA,CAC/C,oBACA,KAAK,QAAA,EACL,IAAM,GAAG,KAAK,QAAA,EAAU,QAAQ,QAAQ;YAE1C,IAAI,KAAK,QAAA,CAAS,KAAA,KAAU,SAAS;gBACnC,KAAK,QAAA,CAAS,KAAA,GAAQ;YACxB;YAEA,WAAW,kBAAkB,KAAK,QAAQ;YAC1C,OAAO;QACT,EAAA,OAAS,GAAG;YACV,WAAW,kBAAkB,KAAK,QAAA,EAAU,CAAC;YAC7C,KAAK,QAAA,CAAS,KAAA,GAAQ;YACtB,OAAO,SAAA,CAAU;gBACf,MAAM,WAAA,cAAA,CAAe,KAAA;gBACrB,SAAS,gBAAgB,CAAC;YAC5B,CAAC;YACD,IAAI,aAAa,OAAO;gBACtB,OAAO,eAAA,CAAgB,CAAC;YAC1B;YAKA,IAAI,OAAO,MAAM,UAAU;gBACzB,IAAI,CAAE,EAAU,gBAAA,EAAkB;oBAChC,KAAK,QAAA,CAAS,eAAA,GAAkB;gBAClC;gBACC,EAAU,gBAAA,GAAmB;YAChC;YAEA,MAAM;QACR,SAAE;YACA,OAAO,aAAA,CAAc,qBAAqB,KAAK,QAAQ,CAAC;YACxD,OAAO,GAAA,CAAI;QACb;IACF;AAEJ;AAQA,eAAsB,WACpB,OAAA,EACA,YAAA,EACA,QAAA,EACA,MAAA,EACA;IACA,MAAA,CAAA,GAAM,eAAA,mCAAA,EAAoC;IAE1C,MAAM,SAAS,WAAA,KAAA,CAAM,SAAA,CAAU,aAAa,cAAc;IAE1D,MAAM,cAAc,WAAA,KAAA,CAAM,cAAA,CAAe,WAAA,YAAA,EAAc;QACrD;QACA,YAAY;QAAA,UAAA;QACZ,QAAQ;IACV,CAAC;IAGD,MAAM,OAAO,OAAO,SAAA,CAAU,SAAS,IAAA,EAAM,CAAC,GAAG,WAAW;IAC5D,KAAK,aAAA,CAAc,qBAAqB,QAAQ,CAAC;IACjD,IAAI,QAAQ;QACV,KAAK,aAAA,CAAc,MAAM;IAC3B;IACA,KAAK,GAAA,CAAI;AACX;AAEA,SAAS,gBAAgB,CAAA,EAAgB;IACvC,IAAI,aAAa,OAAO;QACtB,OAAO,EAAE,OAAA;IACX;IACA,OAAO,GAAG,CAAC,EAAA;AACb;AAEA,SAAS,qBAAqB,QAAA,EAAgD;IAC5E,MAAM,MAAM,CAAC;IACb,OAAO,IAAA,CAAK,QAAQ,EAAE,OAAA,CAAQ,CAAC,QAAQ;QACrC,IACE,QAAQ,cACR,OAAO,QAAA,CAAS,GAAG,CAAA,KAAM,YACzB,SAAS,QAAA,EACT;YACA,OAAO,OAAA,CAAQ,SAAS,QAAQ,EAAE,OAAA,CAAQ,CAAC,CAAC,SAAS,KAAK,CAAA,KAAM;gBAC9D,GAAA,CAAI,cAAc,eAAe,OAAO,CAAA,GAAI;YAC9C,CAAC;QACH,OAAA,IAAW,QAAQ,WAAW,OAAO,QAAA,CAAS,GAAG,CAAA,KAAM,UAAU;YAC/D,GAAA,CAAI,cAAc,MAAM,GAAG,CAAA,GAAI,KAAK,SAAA,CAAU,QAAA,CAAS,GAAG,CAAC;QAC7D,OAAO;YACL,GAAA,CAAI,cAAc,MAAM,GAAG,CAAA,GAAI,QAAA,CAAS,GAAG,CAAA;QAC7C;IACF,CAAC;IACD,OAAO;AACT;AAOO,SAAS,2BACd,QAAA,EACA,GAAA,EACA,KAAA,EACA;IACA,MAAM,cAAc,eAAe,QAAQ;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IACA,IAAI,CAAC,YAAY,QAAA,EAAU;QACzB,YAAY,QAAA,GAAW,CAAC;IAC1B;IACA,YAAY,QAAA,CAAS,GAAG,CAAA,GAAI;AAC9B;AAOO,SAAS,4BACd,QAAA,EACA,MAAA,EACA;IACA,MAAM,cAAc,eAAe,QAAQ;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IACA,IAAI,CAAC,YAAY,QAAA,EAAU;QACzB,YAAY,QAAA,GAAW,CAAC;IAC1B;IACA,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,EAAG;QACjD,YAAY,QAAA,CAAS,GAAG,CAAA,GAAI;IAC9B;AACF;AAOO,SAAS,cAAc,IAAA,EAAsB;IAClD,MAAM,gBAAgB;IACtB,OAAO,MAAM,IAAA,CAAK,KAAK,QAAA,CAAS,aAAa,GAAG,CAAC,IAAM,CAAA,CAAE,CAAC,CAAC,EAAE,IAAA,CAAK,KAAK;AACzE;AAEA,SAAS,eAAe,QAAA,EAAkC;IACxD,MAAM,OAAO,SAAS,UAAA,CAAW,QAAA,CAAuB,kBAAkB;IAC1E,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,8BAA8B;IAChD;IACA,OAAO;AACT;AAEA,SAAS,UACP,IAAA,EACA,UAAA,EACA,MAAA,EACA;IACA,MAAM,WACJ,UAAU,MAAA,CAAO,aAAa,CAAA,GAC1B,CAAA,GAAA,EAAM,MAAA,CAAO,yBAAyB,CAAA,KAAM,SAAS,SAAS,MAAA,CAAO,aAAa,CAAC,EAAA,GACnF;IACN,OAAO,aAAa,CAAA,EAAA,EAAK,IAAI,GAAG,QAAQ,CAAA,CAAA,CAAA;AAC1C;AAEA,SAAS,WAAW,QAAA,EAAoB,QAAA,EAAwB,GAAA,EAAW;IACzE,MAAM,OAAO,SAAS,IAAA,IAAQ;IAC9B,MAAM,gBAAgB,wBAAwB,QAAQ;IAGtD,MAAM,QAAQ,MAAM,IAAA,CAClB,SAAS,UAAA,CAAW,QAAA,CAAwB,mBAAmB,GAAG,SAChE,aAAA,GAAA,IAAI,IAAkB;IAE1B,MAAM,SAAS,MAAM,YAAY;IACjC,IAAI,CAAC,MAAM,IAAA,CAAK,CAAC,IAAM,EAAE,IAAA,CAAK,UAAA,CAAW,IAAI,KAAK,EAAE,MAAA,KAAW,MAAM,GAAG;QACtE,MAAM,MAAM,uBAAA,WAAA,CAAY,GAAA,CAAI;QAC5B,MAAM,QACJ,SAAS,UAAA,CAAW,QAAA,CAAwB,mBAAmB,GAC3D,aAAa;QACnB,SAAS,UAAA,CACN,QAAA,CAAwB,mBAAmB,GAC1C,OAAO,IAAI;YACX,MAAM;YACN,OAAO,KAAK;YACZ,SAAS,MAAM;YACf;QACF,CAAC;IACL;IACA,SAAS,IAAA,GAAO;AAClB;AAEA,SAAS,wBAAwB,QAAA,EAAgC;IAC/D,IAAI,CAAC,SAAS,IAAA,EAAM;QAClB,OAAO;IACT;IAEA,MAAM,iBAAiB,SAAS,IAAA,CAAK,KAAA,CAAM,KAAK;IAEhD,IAAI,eAAe,MAAA,IAAU,GAAG;QAC9B,OAAO,SAAS,IAAA;IAClB;IAEA,MAAM,cACJ,SAAS,QAAA,IAAY,SAAS,QAAA,CAAS,SAAS,CAAA,GAC5C,CAAA,GAAA,EAAM,SAAS,QAAA,CAAS,SAAS,CAAC,EAAA,GAClC;IACN,MAAM,OAAO,GAAG,eAAe,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,KAAK,CAAC,CAAA,EAAA,CAAA;IACvD,MAAM,gBAAgB,CAAA,CAAA,EAAI,eAAe,EAAA,CAAG,CAAA,CAAE,GAAG,MAAM,GAAG,CAAA,CAAE,CAAC,GAAG,WAAW,CAAA,CAAA,CAAA;IAC3E,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/tracing/processor.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Context } from '@opentelemetry/api';\nimport type {\n  ReadableSpan,\n  Span,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport { ATTR_PREFIX } from './instrumentation.js';\n\n// Experimental, WIP\n\nexport class GenkitSpanProcessorWrapper implements SpanProcessor {\n  constructor(private processor: SpanProcessor) {}\n\n  forceFlush(): Promise<void> {\n    return this.processor.forceFlush();\n  }\n\n  onStart(span: Span, parentContext: Context): void {\n    return this.processor.onStart(span, parentContext);\n  }\n\n  onEnd(span: ReadableSpan): void {\n    if (\n      Object.keys(span.attributes).find((k) => k.startsWith(ATTR_PREFIX + ':'))\n    ) {\n      return this.processor.onEnd(new FilteringReadableSpanProxy(span));\n    } else {\n      return this.processor.onEnd(span);\n    }\n  }\n\n  async shutdown(): Promise<void> {\n    return this.processor.shutdown();\n  }\n}\n\nclass FilteringReadableSpanProxy implements ReadableSpan {\n  constructor(private span: ReadableSpan) {}\n\n  get name() {\n    return this.span.name;\n  }\n  get kind() {\n    return this.span.kind;\n  }\n  get parentSpanId() {\n    return this.span.parentSpanId;\n  }\n  get startTime() {\n    return this.span.startTime;\n  }\n  get endTime() {\n    return this.span.endTime;\n  }\n  get status() {\n    return this.span.status;\n  }\n  get attributes() {\n    const out = {} as Record<string, any>;\n    for (const [key, value] of Object.entries(this.span.attributes)) {\n      if (!key.startsWith(ATTR_PREFIX + ':')) {\n        out[key] = value;\n      }\n    }\n    return out;\n  }\n  get links() {\n    return this.span.links;\n  }\n  get events() {\n    return this.span.events;\n  }\n  get duration() {\n    return this.span.duration;\n  }\n  get ended() {\n    return this.span.ended;\n  }\n  get resource() {\n    return this.span.resource;\n  }\n  get instrumentationLibrary() {\n    return this.span.instrumentationLibrary;\n  }\n  get droppedAttributesCount() {\n    return this.span.droppedAttributesCount;\n  }\n  get droppedEventsCount() {\n    return this.span.droppedEventsCount;\n  }\n  get droppedLinksCount() {\n    return this.span.droppedLinksCount;\n  }\n\n  spanContext() {\n    return this.span.spanContext();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,oBAAA,CAAA;AAAA,SAAA,mBAAA;IAAA,4BAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAsBA,IAAA,yBAA4B;AAIrB,MAAM,2BAAoD;IAC/D,YAAoB,SAAA,CAA0B;QAA1B,IAAA,CAAA,SAAA,GAAA;IAA2B;IAE/C,aAA4B;QAC1B,OAAO,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW;IACnC;IAEA,QAAQ,IAAA,EAAY,aAAA,EAA8B;QAChD,OAAO,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,MAAM,aAAa;IACnD;IAEA,MAAM,IAAA,EAA0B;QAC9B,IACE,OAAO,IAAA,CAAK,KAAK,UAAU,EAAE,IAAA,CAAK,CAAC,IAAM,EAAE,UAAA,CAAW,uBAAA,WAAA,GAAc,GAAG,CAAC,GACxE;YACA,OAAO,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,IAAI,2BAA2B,IAAI,CAAC;QAClE,OAAO;YACL,OAAO,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,IAAI;QAClC;IACF;IAEA,MAAM,WAA0B;QAC9B,OAAO,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS;IACjC;AACF;AAEA,MAAM,2BAAmD;IACvD,YAAoB,IAAA,CAAoB;QAApB,IAAA,CAAA,IAAA,GAAA;IAAqB;IAEzC,IAAI,OAAO;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACnB;IACA,IAAI,OAAO;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACnB;IACA,IAAI,eAAe;QACjB,OAAO,IAAA,CAAK,IAAA,CAAK,YAAA;IACnB;IACA,IAAI,YAAY;QACd,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACnB;IACA,IAAI,UAAU;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACnB;IACA,IAAI,SAAS;QACX,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA;IACnB;IACA,IAAI,aAAa;QACf,MAAM,MAAM,CAAC;QACb,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,UAAU,EAAG;YAC/D,IAAI,CAAC,IAAI,UAAA,CAAW,uBAAA,WAAA,GAAc,GAAG,GAAG;gBACtC,GAAA,CAAI,GAAG,CAAA,GAAI;YACb;QACF;QACA,OAAO;IACT;IACA,IAAI,QAAQ;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA;IACnB;IACA,IAAI,SAAS;QACX,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA;IACnB;IACA,IAAI,WAAW;QACb,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IACnB;IACA,IAAI,QAAQ;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA;IACnB;IACA,IAAI,WAAW;QACb,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IACnB;IACA,IAAI,yBAAyB;QAC3B,OAAO,IAAA,CAAK,IAAA,CAAK,sBAAA;IACnB;IACA,IAAI,yBAAyB;QAC3B,OAAO,IAAA,CAAK,IAAA,CAAK,sBAAA;IACnB;IACA,IAAI,qBAAqB;QACvB,OAAO,IAAA,CAAK,IAAA,CAAK,kBAAA;IACnB;IACA,IAAI,oBAAoB;QACtB,OAAO,IAAA,CAAK,IAAA,CAAK,iBAAA;IACnB;IAEA,cAAc;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,WAAA,CAAY;IAC/B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/tracing/types.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { z } from 'zod';\n\n// NOTE: Keep this file in sync with genkit-tools/common/src/types/trace.ts!\n// Eventually tools will be source of truth for these types (by generating a\n// JSON schema) but until then this file must be manually kept in sync\n\nexport const PathMetadataSchema = z.object({\n  path: z.string(),\n  status: z.string(),\n  error: z.string().optional(),\n  latency: z.number(),\n});\nexport type PathMetadata = z.infer<typeof PathMetadataSchema>;\n\nexport const TraceMetadataSchema = z.object({\n  featureName: z.string().optional(),\n  paths: z.set(PathMetadataSchema).optional(),\n  timestamp: z.number(),\n});\nexport type TraceMetadata = z.infer<typeof TraceMetadataSchema>;\n\nexport const SpanMetadataSchema = z.object({\n  name: z.string(),\n  state: z.enum(['success', 'error']).optional(),\n  input: z.any().optional(),\n  output: z.any().optional(),\n  isRoot: z.boolean().optional(),\n  metadata: z.record(z.string(), z.string()).optional(),\n  path: z.string().optional(),\n  // Indicates a \"leaf\" span that is the source of a failure.\n  isFailureSource: z.boolean().optional(),\n});\nexport type SpanMetadata = z.infer<typeof SpanMetadataSchema>;\n\nexport const SpanStatusSchema = z.object({\n  code: z.number(),\n  message: z.string().optional(),\n});\n\nexport const TimeEventSchema = z.object({\n  time: z.number(),\n  annotation: z.object({\n    attributes: z.record(z.string(), z.any()),\n    description: z.string(),\n  }),\n});\n\nexport const SpanContextSchema = z.object({\n  traceId: z.string(),\n  spanId: z.string(),\n  isRemote: z.boolean().optional(),\n  traceFlags: z.number(),\n});\n\nexport const LinkSchema = z.object({\n  context: SpanContextSchema.optional(),\n  attributes: z.record(z.string(), z.any()).optional(),\n  droppedAttributesCount: z.number().optional(),\n});\n\nexport const InstrumentationLibrarySchema = z.object({\n  name: z.string().readonly(),\n  version: z.string().optional().readonly(),\n  schemaUrl: z.string().optional().readonly(),\n});\n\nexport const SpanDataSchema = z.object({\n  spanId: z.string(),\n  traceId: z.string(),\n  parentSpanId: z.string().optional(),\n  startTime: z.number(),\n  endTime: z.number(),\n  attributes: z.record(z.string(), z.any()),\n  displayName: z.string(),\n  links: z.array(LinkSchema).optional(),\n  instrumentationLibrary: InstrumentationLibrarySchema,\n  spanKind: z.string(),\n  sameProcessAsParentSpan: z.object({ value: z.boolean() }).optional(),\n  status: SpanStatusSchema.optional(),\n  timeEvents: z\n    .object({\n      timeEvent: z.array(TimeEventSchema),\n    })\n    .optional(),\n  truncated: z.boolean().optional(),\n});\nexport type SpanData = z.infer<typeof SpanDataSchema>;\n\nexport const TraceDataSchema = z.object({\n  traceId: z.string(),\n  displayName: z.string().optional(),\n  startTime: z\n    .number()\n    .optional()\n    .describe('trace start time in milliseconds since the epoch'),\n  endTime: z\n    .number()\n    .optional()\n    .describe('end time in milliseconds since the epoch'),\n  spans: z.record(z.string(), SpanDataSchema),\n});\n\nexport type TraceData = z.infer<typeof TraceDataSchema>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,8BAAA,IAAA;IAAA,YAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,qBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,aAAkB;AAMX,MAAM,qBAAqB,WAAA,CAAA,CAAE,MAAA,CAAO;IACzC,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO;IACf,QAAQ,WAAA,CAAA,CAAE,MAAA,CAAO;IACjB,OAAO,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC3B,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO;AACpB,CAAC;AAGM,MAAM,sBAAsB,WAAA,CAAA,CAAE,MAAA,CAAO;IAC1C,aAAa,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,OAAO,WAAA,CAAA,CAAE,GAAA,CAAI,kBAAkB,EAAE,QAAA,CAAS;IAC1C,WAAW,WAAA,CAAA,CAAE,MAAA,CAAO;AACtB,CAAC;AAGM,MAAM,qBAAqB,WAAA,CAAA,CAAE,MAAA,CAAO;IACzC,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO;IACf,OAAO,WAAA,CAAA,CAAE,IAAA,CAAK;QAAC;QAAW,OAAO;KAAC,EAAE,QAAA,CAAS;IAC7C,OAAO,WAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACxB,QAAQ,WAAA,CAAA,CAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACzB,QAAQ,WAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC7B,UAAU,WAAA,CAAA,CAAE,MAAA,CAAO,WAAA,CAAA,CAAE,MAAA,CAAO,GAAG,WAAA,CAAA,CAAE,MAAA,CAAO,CAAC,EAAE,QAAA,CAAS;IACpD,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAAA,2DAAA;IAE1B,iBAAiB,WAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AACxC,CAAC;AAGM,MAAM,mBAAmB,WAAA,CAAA,CAAE,MAAA,CAAO;IACvC,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO;IACf,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC/B,CAAC;AAEM,MAAM,kBAAkB,WAAA,CAAA,CAAE,MAAA,CAAO;IACtC,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO;IACf,YAAY,WAAA,CAAA,CAAE,MAAA,CAAO;QACnB,YAAY,WAAA,CAAA,CAAE,MAAA,CAAO,WAAA,CAAA,CAAE,MAAA,CAAO,GAAG,WAAA,CAAA,CAAE,GAAA,CAAI,CAAC;QACxC,aAAa,WAAA,CAAA,CAAE,MAAA,CAAO;IACxB,CAAC;AACH,CAAC;AAEM,MAAM,oBAAoB,WAAA,CAAA,CAAE,MAAA,CAAO;IACxC,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO;IAClB,QAAQ,WAAA,CAAA,CAAE,MAAA,CAAO;IACjB,UAAU,WAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC/B,YAAY,WAAA,CAAA,CAAE,MAAA,CAAO;AACvB,CAAC;AAEM,MAAM,aAAa,WAAA,CAAA,CAAE,MAAA,CAAO;IACjC,SAAS,kBAAkB,QAAA,CAAS;IACpC,YAAY,WAAA,CAAA,CAAE,MAAA,CAAO,WAAA,CAAA,CAAE,MAAA,CAAO,GAAG,WAAA,CAAA,CAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;IACnD,wBAAwB,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;AAC9C,CAAC;AAEM,MAAM,+BAA+B,WAAA,CAAA,CAAE,MAAA,CAAO;IACnD,MAAM,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC1B,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;IACxC,WAAW,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS,EAAE,QAAA,CAAS;AAC5C,CAAC;AAEM,MAAM,iBAAiB,WAAA,CAAA,CAAE,MAAA,CAAO;IACrC,QAAQ,WAAA,CAAA,CAAE,MAAA,CAAO;IACjB,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO;IAClB,cAAc,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAClC,WAAW,WAAA,CAAA,CAAE,MAAA,CAAO;IACpB,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO;IAClB,YAAY,WAAA,CAAA,CAAE,MAAA,CAAO,WAAA,CAAA,CAAE,MAAA,CAAO,GAAG,WAAA,CAAA,CAAE,GAAA,CAAI,CAAC;IACxC,aAAa,WAAA,CAAA,CAAE,MAAA,CAAO;IACtB,OAAO,WAAA,CAAA,CAAE,KAAA,CAAM,UAAU,EAAE,QAAA,CAAS;IACpC,wBAAwB;IACxB,UAAU,WAAA,CAAA,CAAE,MAAA,CAAO;IACnB,yBAAyB,WAAA,CAAA,CAAE,MAAA,CAAO;QAAE,OAAO,WAAA,CAAA,CAAE,OAAA,CAAQ;IAAE,CAAC,EAAE,QAAA,CAAS;IACnE,QAAQ,iBAAiB,QAAA,CAAS;IAClC,YAAY,WAAA,CAAA,CACT,MAAA,CAAO;QACN,WAAW,WAAA,CAAA,CAAE,KAAA,CAAM,eAAe;IACpC,CAAC,EACA,QAAA,CAAS;IACZ,WAAW,WAAA,CAAA,CAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;AAClC,CAAC;AAGM,MAAM,kBAAkB,WAAA,CAAA,CAAE,MAAA,CAAO;IACtC,SAAS,WAAA,CAAA,CAAE,MAAA,CAAO;IAClB,aAAa,WAAA,CAAA,CAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IACjC,WAAW,WAAA,CAAA,CACR,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,kDAAkD;IAC9D,SAAS,WAAA,CAAA,CACN,MAAA,CAAO,EACP,QAAA,CAAS,EACT,QAAA,CAAS,0CAA0C;IACtD,OAAO,WAAA,CAAA,CAAE,MAAA,CAAO,WAAA,CAAA,CAAE,MAAA,CAAO,GAAG,cAAc;AAC5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/tracing.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NodeSDK } from '@opentelemetry/sdk-node';\nimport {\n  BatchSpanProcessor,\n  SimpleSpanProcessor,\n  type SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport { logger } from './logging.js';\nimport type { TelemetryConfig } from './telemetryTypes.js';\nimport {\n  TraceServerExporter,\n  setTelemetryServerUrl,\n} from './tracing/exporter.js';\nimport { isDevEnv } from './utils.js';\n\nexport * from './tracing/exporter.js';\nexport * from './tracing/instrumentation.js';\nexport * from './tracing/processor.js';\nexport * from './tracing/types.js';\n\nlet telemetrySDK: NodeSDK | null = null;\nlet nodeOtelConfig: TelemetryConfig | null = null;\n\nconst instrumentationKey = '__GENKIT_TELEMETRY_INSTRUMENTED';\n\n/**\n * @hidden\n */\nexport async function ensureBasicTelemetryInstrumentation() {\n  await checkFirebaseMonitoringAutoInit();\n\n  if (global[instrumentationKey]) {\n    return await global[instrumentationKey];\n  }\n\n  await enableTelemetry({});\n}\n\n/**\n * Checks to see if the customer is using Firebase Genkit Monitoring\n * auto initialization via environment variable by attempting to resolve\n * the firebase plugin.\n *\n * Enables Firebase Genkit Monitoring if the plugin is installed and warns\n * if it hasn't been installed.\n */\nasync function checkFirebaseMonitoringAutoInit() {\n  if (\n    !global[instrumentationKey] &&\n    process.env.ENABLE_FIREBASE_MONITORING === 'true'\n  ) {\n    try {\n      const firebaseModule = await require('@genkit-ai/firebase');\n      firebaseModule.enableFirebaseTelemetry();\n    } catch (e) {\n      logger.warn(\n        \"It looks like you're trying to enable firebase monitoring, but \" +\n          \"haven't installed the firebase plugin. Please run \" +\n          '`npm i --save @genkit-ai/firebase` and redeploy.'\n      );\n    }\n  }\n}\n\n/**\n * Enables tracing and metrics open telemetry configuration.\n */\nexport async function enableTelemetry(\n  telemetryConfig: TelemetryConfig | Promise<TelemetryConfig>\n) {\n  if (process.env.GENKIT_TELEMETRY_SERVER) {\n    setTelemetryServerUrl(process.env.GENKIT_TELEMETRY_SERVER);\n  }\n  global[instrumentationKey] =\n    telemetryConfig instanceof Promise ? telemetryConfig : Promise.resolve();\n\n  telemetryConfig =\n    telemetryConfig instanceof Promise\n      ? await telemetryConfig\n      : telemetryConfig;\n\n  nodeOtelConfig = telemetryConfig || {};\n\n  const processors: SpanProcessor[] = [createTelemetryServerProcessor()];\n  if (nodeOtelConfig.traceExporter) {\n    throw new Error('Please specify spanProcessors instead.');\n  }\n  if (nodeOtelConfig.spanProcessors) {\n    processors.push(...nodeOtelConfig.spanProcessors);\n  }\n  if (nodeOtelConfig.spanProcessor) {\n    processors.push(nodeOtelConfig.spanProcessor);\n    delete nodeOtelConfig.spanProcessor;\n  }\n  nodeOtelConfig.spanProcessors = processors;\n  telemetrySDK = new NodeSDK(nodeOtelConfig);\n  telemetrySDK.start();\n  process.on('SIGTERM', async () => await cleanUpTracing());\n}\n\nexport async function cleanUpTracing(): Promise<void> {\n  if (!telemetrySDK) {\n    return;\n  }\n\n  // Metrics are not flushed as part of the shutdown operation. If metrics\n  // are enabled, we need to manually flush them *before* the reader\n  // receives shutdown order.\n  await maybeFlushMetrics();\n  await telemetrySDK.shutdown();\n  logger.debug('OpenTelemetry SDK shut down.');\n  telemetrySDK = null;\n}\n\n/**\n * Creates a new SpanProcessor for exporting data to the telemetry server.\n */\nfunction createTelemetryServerProcessor(): SpanProcessor {\n  const exporter = new TraceServerExporter();\n  return isDevEnv()\n    ? new SimpleSpanProcessor(exporter)\n    : new BatchSpanProcessor(exporter);\n}\n\n/** Flush metrics if present. */\nfunction maybeFlushMetrics(): Promise<void> {\n  if (nodeOtelConfig?.metricReader) {\n    return nodeOtelConfig.metricReader.forceFlush();\n  }\n  return Promise.resolve();\n}\n\n/**\n * Flushes all configured span processors.\n *\n * @hidden\n */\nexport async function flushTracing() {\n  if (nodeOtelConfig?.spanProcessors) {\n    await Promise.all(nodeOtelConfig.spanProcessors.map((p) => p.forceFlush()));\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,kBAAA,CAAA;AAAA,SAAA,iBAAA;IAAA,gBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,qCAAA,IAAA;IAAA,cAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,kBAAwB;AACxB,IAAA,wBAIO;AACP,IAAA,iBAAuB;AAEvB,IAAA,kBAGO;AACP,IAAA,eAAyB;AAEzB,WAAA,iBAAc,4JA9Bd,OAAA,OAAA;AA+BA,WAAA,iBAAc,mKA/Bd,OAAA,OAAA;AAgCA,WAAA,iBAAc,6JAhCd,OAAA,OAAA;AAiCA,WAAA,iBAAc,yJAjCd,OAAA,OAAA;AAmCA,IAAI,eAA+B;AACnC,IAAI,iBAAyC;AAE7C,MAAM,qBAAqB;AAK3B,eAAsB,sCAAsC;IAC1D,MAAM,gCAAgC;IAEtC,IAAI,MAAA,CAAO,kBAAkB,CAAA,EAAG;QAC9B,OAAO,MAAM,MAAA,CAAO,kBAAkB,CAAA;IACxC;IAEA,MAAM,gBAAgB,CAAC,CAAC;AAC1B;AAUA,eAAe,kCAAkC;IAC/C,IACE,CAAC,MAAA,CAAO,kBAAkB,CAAA,IAC1B,QAAQ,GAAA,CAAI,0BAAA,KAA+B,QAC3C;QACA,IAAI;YACF,MAAM,iBAAiB,MAAM,QAAQ,qBAAqB;;;;;YAC1D,eAAe,uBAAA,CAAwB;QACzC,EAAA,OAAS,GAAG;YACV,eAAA,MAAA,CAAO,IAAA,CACL;QAIJ;IACF;AACF;AAKA,eAAsB,gBACpB,eAAA,EACA;IACA,IAAI,QAAQ,GAAA,CAAI,uBAAA,EAAyB;QACvC,CAAA,GAAA,gBAAA,qBAAA,EAAsB,QAAQ,GAAA,CAAI,uBAAuB;IAC3D;IACA,MAAA,CAAO,kBAAkB,CAAA,GACvB,2BAA2B,UAAU,kBAAkB,QAAQ,OAAA,CAAQ;IAEzE,kBACE,2BAA2B,UACvB,MAAM,kBACN;IAEN,iBAAiB,mBAAmB,CAAC;IAErC,MAAM,aAA8B;QAAC,+BAA+B,CAAC;KAAA;IACrE,IAAI,eAAe,aAAA,EAAe;QAChC,MAAM,IAAI,MAAM,wCAAwC;IAC1D;IACA,IAAI,eAAe,cAAA,EAAgB;QACjC,WAAW,IAAA,CAAK,GAAG,eAAe,cAAc;IAClD;IACA,IAAI,eAAe,aAAA,EAAe;QAChC,WAAW,IAAA,CAAK,eAAe,aAAa;QAC5C,OAAO,eAAe,aAAA;IACxB;IACA,eAAe,cAAA,GAAiB;IAChC,eAAe,IAAI,gBAAA,OAAA,CAAQ,cAAc;IACzC,aAAa,KAAA,CAAM;IACnB,QAAQ,EAAA,CAAG,WAAW,UAAY,MAAM,eAAe,CAAC;AAC1D;AAEA,eAAsB,iBAAgC;IACpD,IAAI,CAAC,cAAc;QACjB;IACF;IAKA,MAAM,kBAAkB;IACxB,MAAM,aAAa,QAAA,CAAS;IAC5B,eAAA,MAAA,CAAO,KAAA,CAAM,8BAA8B;IAC3C,eAAe;AACjB;AAKA,SAAS,iCAAgD;IACvD,MAAM,WAAW,IAAI,gBAAA,mBAAA,CAAoB;IACzC,OAAA,CAAA,GAAO,aAAA,QAAA,EAAS,IACZ,IAAI,sBAAA,mBAAA,CAAoB,QAAQ,IAChC,IAAI,sBAAA,kBAAA,CAAmB,QAAQ;AACrC;AAGA,SAAS,oBAAmC;IAC1C,IAAI,gBAAgB,cAAc;QAChC,OAAO,eAAe,YAAA,CAAa,UAAA,CAAW;IAChD;IACA,OAAO,QAAQ,OAAA,CAAQ;AACzB;AAOA,eAAsB,eAAe;IACnC,IAAI,gBAAgB,gBAAgB;QAClC,MAAM,QAAQ,GAAA,CAAI,eAAe,cAAA,CAAe,GAAA,CAAI,CAAC,IAAM,EAAE,UAAA,CAAW,CAAC,CAAC;IAC5E;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/action.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { JSONSchema7 } from 'json-schema';\nimport type * as z from 'zod';\nimport { lazy } from './async.js';\nimport { getContext, runWithContext, type ActionContext } from './context.js';\nimport type { ActionType, Registry } from './registry.js';\nimport { parseSchema } from './schema.js';\nimport {\n  SPAN_TYPE_ATTR,\n  newTrace,\n  setCustomMetadataAttributes,\n} from './tracing.js';\n\nexport { StatusCodes, StatusSchema, type Status } from './statusTypes.js';\nexport type { JSONSchema7 };\n\nconst makeNoopAbortSignal = () =>\n  ({\n    aborted: false,\n    onabort(ev) {},\n    throwIfAborted() {},\n  }) as AbortSignal;\n\n/**\n * Action metadata.\n */\nexport interface ActionMetadata<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  actionType?: ActionType;\n  name: string;\n  description?: string;\n  inputSchema?: I;\n  inputJsonSchema?: JSONSchema7;\n  outputSchema?: O;\n  outputJsonSchema?: JSONSchema7;\n  streamSchema?: S;\n  metadata?: Record<string, any>;\n  detached?: boolean;\n}\n\n/**\n * Results of an action run. Includes telemetry.\n */\nexport interface ActionResult<O> {\n  result: O;\n  telemetry: {\n    traceId: string;\n    spanId: string;\n  };\n}\n\n/**\n * Options (side channel) data to pass to the model.\n */\nexport interface ActionRunOptions<S> {\n  /**\n   * Streaming callback (optional).\n   */\n  onChunk?: StreamingCallback<S>;\n\n  /**\n   * Additional runtime context data (ex. auth context data).\n   */\n  context?: ActionContext;\n\n  /**\n   * Additional span attributes to apply to OT spans.\n   */\n  telemetryLabels?: Record<string, string>;\n\n  /**\n   * Abort signal for the action request.\n   */\n  abortSignal?: AbortSignal;\n}\n\n/**\n * Options (side channel) data to pass to the model.\n */\nexport interface ActionFnArg<S> {\n  /**\n   * Whether the caller of the action requested streaming.\n   */\n  streamingRequested: boolean;\n\n  /**\n   * Streaming callback (optional).\n   */\n  sendChunk: StreamingCallback<S>;\n\n  /**\n   * Additional runtime context data (ex. auth context data).\n   */\n  context?: ActionContext;\n\n  /**\n   * Trace context containing trace and span IDs.\n   */\n  trace: {\n    traceId: string;\n    spanId: string;\n  };\n\n  /**\n   * Abort signal for the action request.\n   */\n  abortSignal: AbortSignal;\n}\n\n/**\n * Streaming response from an action.\n */\nexport interface StreamingResponse<\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /** Iterator over the streaming chunks. */\n  stream: AsyncGenerator<z.infer<S>>;\n  /** Final output of the action. */\n  output: Promise<z.infer<O>>;\n}\n\n/**\n * Self-describing, validating, observable, locally and remotely callable function.\n */\nexport type Action<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n  RunOptions extends ActionRunOptions<S> = ActionRunOptions<S>,\n> = ((input?: z.infer<I>, options?: RunOptions) => Promise<z.infer<O>>) & {\n  __action: ActionMetadata<I, O, S>;\n  __registry: Registry;\n  run(\n    input?: z.infer<I>,\n    options?: ActionRunOptions<z.infer<S>>\n  ): Promise<ActionResult<z.infer<O>>>;\n\n  stream(\n    input?: z.infer<I>,\n    opts?: ActionRunOptions<z.infer<S>>\n  ): StreamingResponse<O, S>;\n};\n\n/**\n * Self-describing, validating, observable, locally and remotely callable function.\n */\nexport type DetachedAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n  RunOptions extends ActionRunOptions<S> = ActionRunOptions<S>,\n> = {\n  __action: ActionMetadata<I, O, S>;\n  attach(registry: Registry): Action<I, O, S, RunOptions>;\n};\n\n/**\n * Action factory params.\n */\nexport type ActionParams<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name:\n    | string\n    | {\n        pluginId: string;\n        actionId: string;\n      };\n  description?: string;\n  inputSchema?: I;\n  inputJsonSchema?: JSONSchema7;\n  outputSchema?: O;\n  outputJsonSchema?: JSONSchema7;\n  metadata?: Record<string, any>;\n  use?: Middleware<z.infer<I>, z.infer<O>, z.infer<S>>[];\n  streamSchema?: S;\n  actionType: ActionType;\n};\n\nexport type ActionAsyncParams<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> = ActionParams<I, O, S> & {\n  fn: (\n    input: z.infer<I>,\n    options: ActionFnArg<z.infer<S>>\n  ) => Promise<z.infer<O>>;\n};\n\nexport type SimpleMiddleware<I = any, O = any> = (\n  req: I,\n  next: (req?: I) => Promise<O>\n) => Promise<O>;\n\nexport type MiddlewareWithOptions<I = any, O = any, S = any> = (\n  req: I,\n  options: ActionRunOptions<S> | undefined,\n  next: (req?: I, options?: ActionRunOptions<S>) => Promise<O>\n) => Promise<O>;\n\n/**\n * Middleware function for actions.\n */\nexport type Middleware<I = any, O = any, S = any> =\n  | SimpleMiddleware<I, O>\n  | MiddlewareWithOptions<I, O, S>;\n\n/**\n * Creates an action with provided middleware.\n */\nexport function actionWithMiddleware<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  action: Action<I, O, S>,\n  middleware: Middleware<z.infer<I>, z.infer<O>, z.infer<S>>[]\n): Action<I, O, S> {\n  const wrapped = (async (req: z.infer<I>) => {\n    return (await wrapped.run(req)).result;\n  }) as Action<I, O, S>;\n  wrapped.__action = action.__action;\n  wrapped.__registry = action.__registry;\n  wrapped.run = async (\n    req: z.infer<I>,\n    options?: ActionRunOptions<z.infer<S>>\n  ): Promise<ActionResult<z.infer<O>>> => {\n    let telemetry;\n    const dispatch = async (\n      index: number,\n      req: z.infer<I>,\n      opts?: ActionRunOptions<z.infer<S>>\n    ) => {\n      if (index === middleware.length) {\n        // end of the chain, call the original model action\n        const result = await action.run(req, opts);\n        telemetry = result.telemetry;\n        return result.result;\n      }\n\n      const currentMiddleware = middleware[index];\n      if (currentMiddleware.length === 3) {\n        return (currentMiddleware as MiddlewareWithOptions<I, O, z.infer<S>>)(\n          req,\n          opts,\n          async (modifiedReq, modifiedOptions) =>\n            dispatch(index + 1, modifiedReq || req, modifiedOptions || opts)\n        );\n      } else if (currentMiddleware.length === 2) {\n        return (currentMiddleware as SimpleMiddleware<I, O>)(\n          req,\n          async (modifiedReq) => dispatch(index + 1, modifiedReq || req, opts)\n        );\n      } else {\n        throw new Error('unspported middleware function shape');\n      }\n    };\n    wrapped.stream = action.stream;\n\n    return { result: await dispatch(0, req, options), telemetry };\n  };\n  return wrapped;\n}\n\n/**\n * Creates an action with the provided config.\n */\nexport function action<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  config: ActionParams<I, O, S>,\n  fn: (\n    input: z.infer<I>,\n    options: ActionFnArg<z.infer<S>>\n  ) => Promise<z.infer<O>>\n): Action<I, O, z.infer<S>> {\n  return detachedAction(config, fn).attach(registry);\n}\n\n/**\n * Creates an action with the provided config.\n */\nexport function detachedAction<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  config: ActionParams<I, O, S>,\n  fn: (\n    input: z.infer<I>,\n    options: ActionFnArg<z.infer<S>> & { registry: Registry }\n  ) => Promise<z.infer<O>>\n): DetachedAction<I, O, z.infer<S>> {\n  const actionName =\n    typeof config.name === 'string'\n      ? config.name\n      : `${config.name.pluginId}/${config.name.actionId}`;\n  const actionMetadata = {\n    name: actionName,\n    description: config.description,\n    inputSchema: config.inputSchema,\n    inputJsonSchema: config.inputJsonSchema,\n    outputSchema: config.outputSchema,\n    outputJsonSchema: config.outputJsonSchema,\n    streamSchema: config.streamSchema,\n    metadata: config.metadata,\n    actionType: config.actionType,\n    detached: true,\n  } as ActionMetadata<I, O, S>;\n\n  return {\n    __action: actionMetadata,\n    attach(registry: Registry): Action<I, O, z.infer<S>> {\n      const actionFn = async (\n        input?: I,\n        options?: ActionRunOptions<z.infer<S>>\n      ) => {\n        return (await actionFn.run(input, options)).result;\n      };\n      actionFn.__registry = registry;\n      actionFn.__action = { ...actionMetadata };\n      delete actionFn.__action['detached'];\n\n      actionFn.run = async (\n        input: z.infer<I>,\n        options?: ActionRunOptions<z.infer<S>>\n      ): Promise<ActionResult<z.infer<O>>> => {\n        input = parseSchema(input, {\n          schema: config.inputSchema,\n          jsonSchema: config.inputJsonSchema,\n        });\n        let traceId;\n        let spanId;\n        let output = await newTrace(\n          registry,\n          {\n            name: actionName,\n            labels: {\n              [SPAN_TYPE_ATTR]: 'action',\n              'genkit:metadata:subtype': config.actionType,\n              ...options?.telemetryLabels,\n            },\n          },\n          async (metadata, span) => {\n            setCustomMetadataAttributes(registry, {\n              subtype: config.actionType,\n            });\n            if (options?.context) {\n              setCustomMetadataAttributes(registry, {\n                context: JSON.stringify(options.context),\n              });\n            }\n\n            traceId = span.spanContext().traceId;\n            spanId = span.spanContext().spanId;\n            metadata.name = actionName;\n            metadata.input = input;\n\n            try {\n              const actionFn = () =>\n                fn(input, {\n                  ...options,\n                  // Context can either be explicitly set, or inherited from the parent action.\n                  context: {\n                    ...registry.context,\n                    ...(options?.context ?? getContext(registry)),\n                  },\n                  streamingRequested:\n                    !!options?.onChunk &&\n                    options.onChunk !== sentinelNoopStreamingCallback,\n                  sendChunk: options?.onChunk ?? sentinelNoopStreamingCallback,\n                  trace: {\n                    traceId,\n                    spanId,\n                  },\n                  registry,\n                  abortSignal: options?.abortSignal ?? makeNoopAbortSignal(),\n                });\n              // if context is explicitly passed in, we run action with the provided context,\n              // otherwise we let upstream context carry through.\n              const output = await runWithContext(\n                registry,\n                options?.context,\n                actionFn\n              );\n\n              metadata.output = JSON.stringify(output);\n              return output;\n            } catch (err) {\n              if (typeof err === 'object') {\n                (err as any).traceId = traceId;\n              }\n              throw err;\n            }\n          }\n        );\n        output = parseSchema(output, {\n          schema: config.outputSchema,\n          jsonSchema: config.outputJsonSchema,\n        });\n        return {\n          result: output,\n          telemetry: {\n            traceId,\n            spanId,\n          },\n        };\n      };\n\n      actionFn.stream = (\n        input?: z.infer<I>,\n        opts?: ActionRunOptions<z.infer<S>>\n      ): StreamingResponse<O, S> => {\n        let chunkStreamController: ReadableStreamController<z.infer<S>>;\n        const chunkStream = new ReadableStream<z.infer<S>>({\n          start(controller) {\n            chunkStreamController = controller;\n          },\n          pull() {},\n          cancel() {},\n        });\n\n        const invocationPromise = actionFn\n          .run(config.inputSchema ? config.inputSchema.parse(input) : input, {\n            onChunk: ((chunk: z.infer<S>) => {\n              chunkStreamController.enqueue(chunk);\n            }) as S extends z.ZodVoid\n              ? undefined\n              : StreamingCallback<z.infer<S>>,\n            context: {\n              ...registry.context,\n              ...(opts?.context ?? getContext(registry)),\n            },\n          })\n          .then((s) => s.result)\n          .finally(() => {\n            chunkStreamController.close();\n          });\n\n        return {\n          output: invocationPromise,\n          stream: (async function* () {\n            const reader = chunkStream.getReader();\n            while (true) {\n              const chunk = await reader.read();\n              if (chunk.value) {\n                yield chunk.value;\n              }\n              if (chunk.done) {\n                break;\n              }\n            }\n            return await invocationPromise;\n          })(),\n        };\n      };\n\n      if (config.use) {\n        return actionWithMiddleware(actionFn, config.use);\n      }\n      return actionFn;\n    },\n  };\n}\n\nexport function isAction(a: unknown): a is Action {\n  return (\n    typeof a === 'function' && '__action' in a && !(a as any).__action.detached\n  );\n}\n\nexport function isDetachedAction(a: unknown): a is DetachedAction {\n  return (\n    !!(a as DetachedAction).__action &&\n    !!(a as DetachedAction).__action.detached &&\n    typeof (a as DetachedAction).attach === 'function'\n  );\n}\n\n/**\n * Defines an action with the given config and registers it in the registry.\n */\nexport function defineAction<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  config: ActionParams<I, O, S>,\n  fn: (\n    input: z.infer<I>,\n    options: ActionFnArg<z.infer<S>>\n  ) => Promise<z.infer<O>>\n): Action<I, O, S> {\n  if (isInRuntimeContext(registry)) {\n    throw new Error(\n      'Cannot define new actions at runtime.\\n' +\n        'See: https://github.com/firebase/genkit/blob/main/docs/errors/no_new_actions_at_runtime.md'\n    );\n  }\n  const act = action(\n    registry,\n    config,\n    async (i: I, options): Promise<z.infer<O>> => {\n      await registry.initializeAllPlugins();\n      return await runInActionRuntimeContext(registry, () => fn(i, options));\n    }\n  );\n  act.__action.actionType = config.actionType;\n  registry.registerAction(config.actionType, act);\n  return act;\n}\n\n/**\n * Defines an action with the given config promise and registers it in the registry.\n */\nexport function defineActionAsync<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  actionType: ActionType,\n  name:\n    | string\n    | {\n        pluginId: string;\n        actionId: string;\n      },\n  config: PromiseLike<ActionAsyncParams<I, O, S>>,\n  onInit?: (action: Action<I, O, S>) => void\n): PromiseLike<Action<I, O, S>> {\n  const actionName =\n    typeof name === 'string' ? name : `${name.pluginId}/${name.actionId}`;\n  const actionPromise = lazy(() =>\n    config.then((resolvedConfig) => {\n      const act = action(\n        registry,\n        resolvedConfig,\n        async (i: I, options): Promise<z.infer<O>> => {\n          await registry.initializeAllPlugins();\n          return await runInActionRuntimeContext(registry, () =>\n            resolvedConfig.fn(i, options)\n          );\n        }\n      );\n      act.__action.actionType = actionType;\n      onInit?.(act);\n      return act;\n    })\n  );\n  registry.registerActionAsync(actionType, actionName, actionPromise);\n  return actionPromise;\n}\n\n// Streaming callback function.\nexport type StreamingCallback<T> = (chunk: T) => void;\n\nconst streamingAlsKey = 'core.action.streamingCallback';\nexport const sentinelNoopStreamingCallback = () => null;\n\n/**\n * Executes provided function with streaming callback in async local storage which can be retrieved\n * using {@link getStreamingCallback}.\n */\nexport function runWithStreamingCallback<S, O>(\n  registry: Registry,\n  streamingCallback: StreamingCallback<S> | undefined,\n  fn: () => O\n): O {\n  return registry.asyncStore.run(\n    streamingAlsKey,\n    streamingCallback || sentinelNoopStreamingCallback,\n    fn\n  );\n}\n\n/**\n * Retrieves the {@link StreamingCallback} previously set by {@link runWithStreamingCallback}\n *\n * @hidden\n */\nexport function getStreamingCallback<S>(\n  registry: Registry\n): StreamingCallback<S> | undefined {\n  const cb =\n    registry.asyncStore.getStore<StreamingCallback<S>>(streamingAlsKey);\n  if (cb === sentinelNoopStreamingCallback) {\n    return undefined;\n  }\n  return cb;\n}\n\nconst runtimeContextAslKey = 'core.action.runtimeContext';\n\n/**\n * Checks whether the caller is currently in the runtime context of an action.\n */\nexport function isInRuntimeContext(registry: Registry) {\n  return registry.asyncStore.getStore(runtimeContextAslKey) === 'runtime';\n}\n\n/**\n * Execute the provided function in the action runtime context.\n */\nexport function runInActionRuntimeContext<R>(registry: Registry, fn: () => R) {\n  return registry.asyncStore.run(runtimeContextAslKey, 'runtime', fn);\n}\n\n/**\n * Execute the provided function outside the action runtime context.\n */\nexport function runOutsideActionRuntimeContext<R>(\n  registry: Registry,\n  fn: () => R\n) {\n  return registry.asyncStore.run(runtimeContextAslKey, 'outside', fn);\n}\n"], "names": ["action", "req", "actionFn", "output"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,iBAAA,CAAA;AAAA,SAAA,gBAAA;IAAA,aAAA,IAAA,mBAAA,WAAA;IAAA,cAAA,IAAA,mBAAA,YAAA;IAAA,QAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,cAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,sBAAA,IAAA;IAAA,UAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,2BAAA,IAAA;IAAA,gCAAA,IAAA;IAAA,0BAAA,IAAA;IAAA,+BAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAkBA,IAAA,eAAqB;AACrB,IAAA,iBAA+D;AAE/D,IAAA,gBAA4B;AAC5B,IAAA,iBAIO;AAEP,IAAA,qBAAuD;AAGvD,MAAM,sBAAsB,IAAA,CACzB;QACC,SAAS;QACT,SAAQ,EAAA,EAAI,CAAC;QACb,iBAAiB,EAAC;IACpB,CAAA;AAoMK,SAAS,qBAKdA,OAAAA,EACA,UAAA,EACiB;IACjB,MAAM,UAAW,OAAO,QAAoB;QAC1C,OAAA,CAAQ,MAAM,QAAQ,GAAA,CAAI,GAAG,CAAA,EAAG,MAAA;IAClC;IACA,QAAQ,QAAA,GAAWA,QAAO,QAAA;IAC1B,QAAQ,UAAA,GAAaA,QAAO,UAAA;IAC5B,QAAQ,GAAA,GAAM,OACZ,KACA,YACsC;QACtC,IAAI;QACJ,MAAM,WAAW,OACf,OACAC,MACA,SACG;YACH,IAAI,UAAU,WAAW,MAAA,EAAQ;gBAE/B,MAAM,SAAS,MAAMD,QAAO,GAAA,CAAIC,MAAK,IAAI;gBACzC,YAAY,OAAO,SAAA;gBACnB,OAAO,OAAO,MAAA;YAChB;YAEA,MAAM,oBAAoB,UAAA,CAAW,KAAK,CAAA;YAC1C,IAAI,kBAAkB,MAAA,KAAW,GAAG;gBAClC,OAAQ,kBACNA,MACA,MACA,OAAO,aAAa,kBAClB,SAAS,QAAQ,GAAG,eAAeA,MAAK,mBAAmB,IAAI;YAErE,OAAA,IAAW,kBAAkB,MAAA,KAAW,GAAG;gBACzC,OAAQ,kBACNA,MACA,OAAO,cAAgB,SAAS,QAAQ,GAAG,eAAeA,MAAK,IAAI;YAEvE,OAAO;gBACL,MAAM,IAAI,MAAM,sCAAsC;YACxD;QACF;QACA,QAAQ,MAAA,GAASD,QAAO,MAAA;QAExB,OAAO;YAAE,QAAQ,MAAM,SAAS,GAAG,KAAK,OAAO;YAAG;QAAU;IAC9D;IACA,OAAO;AACT;AAKO,SAAS,OAKd,QAAA,EACA,MAAA,EACA,EAAA,EAI0B;IAC1B,OAAO,eAAe,QAAQ,EAAE,EAAE,MAAA,CAAO,QAAQ;AACnD;AAKO,SAAS,eAKd,MAAA,EACA,EAAA,EAIkC;IAClC,MAAM,aACJ,OAAO,OAAO,IAAA,KAAS,WACnB,OAAO,IAAA,GACP,GAAG,OAAO,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,OAAO,IAAA,CAAK,QAAQ,EAAA;IACrD,MAAM,iBAAiB;QACrB,MAAM;QACN,aAAa,OAAO,WAAA;QACpB,aAAa,OAAO,WAAA;QACpB,iBAAiB,OAAO,eAAA;QACxB,cAAc,OAAO,YAAA;QACrB,kBAAkB,OAAO,gBAAA;QACzB,cAAc,OAAO,YAAA;QACrB,UAAU,OAAO,QAAA;QACjB,YAAY,OAAO,UAAA;QACnB,UAAU;IACZ;IAEA,OAAO;QACL,UAAU;QACV,QAAO,QAAA,EAA8C;YACnD,MAAM,WAAW,OACf,OACA,YACG;gBACH,OAAA,CAAQ,MAAM,SAAS,GAAA,CAAI,OAAO,OAAO,CAAA,EAAG,MAAA;YAC9C;YACA,SAAS,UAAA,GAAa;YACtB,SAAS,QAAA,GAAW;gBAAE,GAAG,cAAA;YAAe;YACxC,OAAO,SAAS,QAAA,CAAS,UAAU,CAAA;YAEnC,SAAS,GAAA,GAAM,OACb,OACA,YACsC;gBACtC,QAAA,CAAA,GAAQ,cAAA,WAAA,EAAY,OAAO;oBACzB,QAAQ,OAAO,WAAA;oBACf,YAAY,OAAO,eAAA;gBACrB,CAAC;gBACD,IAAI;gBACJ,IAAI;gBACJ,IAAI,SAAS,MAAA,CAAA,GAAM,eAAA,QAAA,EACjB,UACA;oBACE,MAAM;oBACN,QAAQ;wBACN,CAAC,eAAA,cAAc,CAAA,EAAG;wBAClB,2BAA2B,OAAO,UAAA;wBAClC,GAAG,SAAS,eAAA;oBACd;gBACF,GACA,OAAO,UAAU,SAAS;oBACxB,CAAA,GAAA,eAAA,2BAAA,EAA4B,UAAU;wBACpC,SAAS,OAAO,UAAA;oBAClB,CAAC;oBACD,IAAI,SAAS,SAAS;wBACpB,CAAA,GAAA,eAAA,2BAAA,EAA4B,UAAU;4BACpC,SAAS,KAAK,SAAA,CAAU,QAAQ,OAAO;wBACzC,CAAC;oBACH;oBAEA,UAAU,KAAK,WAAA,CAAY,EAAE,OAAA;oBAC7B,SAAS,KAAK,WAAA,CAAY,EAAE,MAAA;oBAC5B,SAAS,IAAA,GAAO;oBAChB,SAAS,KAAA,GAAQ;oBAEjB,IAAI;wBACF,MAAME,YAAW,IACf,GAAG,OAAO;gCACR,GAAG,OAAA;gCAAA,6EAAA;gCAEH,SAAS;oCACP,GAAG,SAAS,OAAA;oCACZ,GAAI,SAAS,WAAA,CAAA,GAAW,eAAA,UAAA,EAAW,QAAQ,CAAA;gCAC7C;gCACA,oBACE,CAAC,CAAC,SAAS,WACX,QAAQ,OAAA,KAAY;gCACtB,WAAW,SAAS,WAAW;gCAC/B,OAAO;oCACL;oCACA;gCACF;gCACA;gCACA,aAAa,SAAS,eAAe,oBAAoB;4BAC3D,CAAC;wBAGH,MAAMC,UAAS,MAAA,CAAA,GAAM,eAAA,cAAA,EACnB,UACA,SAAS,SACTD;wBAGF,SAAS,MAAA,GAAS,KAAK,SAAA,CAAUC,OAAM;wBACvC,OAAOA;oBACT,EAAA,OAAS,KAAK;wBACZ,IAAI,OAAO,QAAQ,UAAU;4BAC1B,IAAY,OAAA,GAAU;wBACzB;wBACA,MAAM;oBACR;gBACF;gBAEF,SAAA,CAAA,GAAS,cAAA,WAAA,EAAY,QAAQ;oBAC3B,QAAQ,OAAO,YAAA;oBACf,YAAY,OAAO,gBAAA;gBACrB,CAAC;gBACD,OAAO;oBACL,QAAQ;oBACR,WAAW;wBACT;wBACA;oBACF;gBACF;YACF;YAEA,SAAS,MAAA,GAAS,CAChB,OACA,SAC4B;gBAC5B,IAAI;gBACJ,MAAM,cAAc,IAAI,eAA2B;oBACjD,OAAM,UAAA,EAAY;wBAChB,wBAAwB;oBAC1B;oBACA,OAAO,EAAC;oBACR,SAAS,EAAC;gBACZ,CAAC;gBAED,MAAM,oBAAoB,SACvB,GAAA,CAAI,OAAO,WAAA,GAAc,OAAO,WAAA,CAAY,KAAA,CAAM,KAAK,IAAI,OAAO;oBACjE,SAAU,CAAC,UAAsB;wBAC/B,sBAAsB,OAAA,CAAQ,KAAK;oBACrC;oBAGA,SAAS;wBACP,GAAG,SAAS,OAAA;wBACZ,GAAI,MAAM,WAAA,CAAA,GAAW,eAAA,UAAA,EAAW,QAAQ,CAAA;oBAC1C;gBACF,CAAC,EACA,IAAA,CAAK,CAAC,IAAM,EAAE,MAAM,EACpB,OAAA,CAAQ,MAAM;oBACb,sBAAsB,KAAA,CAAM;gBAC9B,CAAC;gBAEH,OAAO;oBACL,QAAQ;oBACR,QAAS,mBAAmB;wBAC1B,MAAM,SAAS,YAAY,SAAA,CAAU;wBACrC,MAAO,KAAM;4BACX,MAAM,QAAQ,MAAM,OAAO,IAAA,CAAK;4BAChC,IAAI,MAAM,KAAA,EAAO;gCACf,MAAM,MAAM,KAAA;4BACd;4BACA,IAAI,MAAM,IAAA,EAAM;gCACd;4BACF;wBACF;wBACA,OAAO,MAAM;oBACf,EAAG;gBACL;YACF;YAEA,IAAI,OAAO,GAAA,EAAK;gBACd,OAAO,qBAAqB,UAAU,OAAO,GAAG;YAClD;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,SAAS,CAAA,EAAyB;IAChD,OACE,OAAO,MAAM,cAAc,cAAc,KAAK,CAAE,EAAU,QAAA,CAAS,QAAA;AAEvE;AAEO,SAAS,iBAAiB,CAAA,EAAiC;IAChE,OACE,CAAC,CAAE,EAAqB,QAAA,IACxB,CAAC,CAAE,EAAqB,QAAA,CAAS,QAAA,IACjC,OAAQ,EAAqB,MAAA,KAAW;AAE5C;AAKO,SAAS,aAKd,QAAA,EACA,MAAA,EACA,EAAA,EAIiB;IACjB,IAAI,mBAAmB,QAAQ,GAAG;QAChC,MAAM,IAAI,MACR;IAGJ;IACA,MAAM,MAAM,OACV,UACA,QACA,OAAO,GAAM,YAAiC;QAC5C,MAAM,SAAS,oBAAA,CAAqB;QACpC,OAAO,MAAM,0BAA0B,UAAU,IAAM,GAAG,GAAG,OAAO,CAAC;IACvE;IAEF,IAAI,QAAA,CAAS,UAAA,GAAa,OAAO,UAAA;IACjC,SAAS,cAAA,CAAe,OAAO,UAAA,EAAY,GAAG;IAC9C,OAAO;AACT;AAKO,SAAS,kBAKd,QAAA,EACA,UAAA,EACA,IAAA,EAMA,MAAA,EACA,MAAA,EAC8B;IAC9B,MAAM,aACJ,OAAO,SAAS,WAAW,OAAO,GAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,KAAK,QAAQ,EAAA;IACrE,MAAM,gBAAA,CAAA,GAAgB,aAAA,IAAA,EAAK,IACzB,OAAO,IAAA,CAAK,CAAC,mBAAmB;YAC9B,MAAM,MAAM,OACV,UACA,gBACA,OAAO,GAAM,YAAiC;gBAC5C,MAAM,SAAS,oBAAA,CAAqB;gBACpC,OAAO,MAAM,0BAA0B,UAAU,IAC/C,eAAe,EAAA,CAAG,GAAG,OAAO;YAEhC;YAEF,IAAI,QAAA,CAAS,UAAA,GAAa;YAC1B,SAAS,GAAG;YACZ,OAAO;QACT,CAAC;IAEH,SAAS,mBAAA,CAAoB,YAAY,YAAY,aAAa;IAClE,OAAO;AACT;AAKA,MAAM,kBAAkB;AACjB,MAAM,gCAAgC,IAAM;AAM5C,SAAS,yBACd,QAAA,EACA,iBAAA,EACA,EAAA,EACG;IACH,OAAO,SAAS,UAAA,CAAW,GAAA,CACzB,iBACA,qBAAqB,+BACrB;AAEJ;AAOO,SAAS,qBACd,QAAA,EACkC;IAClC,MAAM,KACJ,SAAS,UAAA,CAAW,QAAA,CAA+B,eAAe;IACpE,IAAI,OAAO,+BAA+B;QACxC,OAAO,KAAA;IACT;IACA,OAAO;AACT;AAEA,MAAM,uBAAuB;AAKtB,SAAS,mBAAmB,QAAA,EAAoB;IACrD,OAAO,SAAS,UAAA,CAAW,QAAA,CAAS,oBAAoB,MAAM;AAChE;AAKO,SAAS,0BAA6B,QAAA,EAAoB,EAAA,EAAa;IAC5E,OAAO,SAAS,UAAA,CAAW,GAAA,CAAI,sBAAsB,WAAW,EAAE;AACpE;AAKO,SAAS,+BACd,QAAA,EACA,EAAA,EACA;IACA,OAAO,SAAS,UAAA,CAAW,GAAA,CAAI,sBAAsB,WAAW,EAAE;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1891, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/background-action.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { JSONSchema7 } from 'json-schema';\nimport * as z from 'zod';\nimport { Action, ActionMetadata, defineAction, Middleware } from './action.js';\nimport { ActionContext } from './context.js';\nimport { GenkitError } from './error.js';\nimport { ActionType, Registry } from './registry.js';\nimport { toJsonSchema } from './schema.js';\n\n/**\n * Zod schema of an opration representing a background task.\n */\nexport const OperationSchema = z.object({\n  action: z.string().optional(),\n  id: z.string(),\n  done: z.boolean().optional(),\n  output: z.any().optional(),\n  error: z.object({ message: z.string() }).passthrough().optional(),\n  metadata: z.record(z.string(), z.any()).optional(),\n});\n\n/**\n * Background operation.\n */\nexport interface Operation<O = any> {\n  action?: string;\n  id: string;\n  done?: boolean;\n  output?: O;\n  error?: { message: string; [key: string]: unknown };\n  metadata?: Record<string, any>;\n}\n\n/**\n * Background action. Unlike regular action, background action can run for a long time in the background.\n * The returned operation can used to check the status of the background operation and retrieve the response.\n */\nexport interface BackgroundAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  RunOptions extends BackgroundActionRunOptions = BackgroundActionRunOptions,\n> {\n  __action: ActionMetadata<I, O>;\n  readonly supportsCancel: boolean;\n\n  start(\n    input?: z.infer<I>,\n    options?: RunOptions\n  ): Promise<Operation<z.infer<O>>>;\n\n  check(operation: Operation<z.infer<O>>): Promise<Operation<z.infer<O>>>;\n\n  cancel(operation: Operation<z.infer<O>>): Promise<Operation<z.infer<O>>>;\n}\n\nexport async function lookupBackgroundAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  key: string\n): Promise<BackgroundAction<I, O> | undefined> {\n  const root: Action<I, typeof OperationSchema> = await registry.lookupAction<\n    I,\n    typeof OperationSchema,\n    Action<I, typeof OperationSchema>\n  >(key);\n  if (!root) return undefined;\n  const actionName = key.substring(key.indexOf('/', 1) + 1);\n  return new BackgroundActionImpl(\n    root,\n    await registry.lookupAction<\n      typeof OperationSchema,\n      typeof OperationSchema,\n      Action<typeof OperationSchema, typeof OperationSchema>\n    >(`/check-operation/${actionName}/check`),\n    await registry.lookupAction<\n      typeof OperationSchema,\n      typeof OperationSchema,\n      Action<typeof OperationSchema, typeof OperationSchema>\n    >(`/cancel-operation/${actionName}/cancel`)\n  );\n}\n\nclass BackgroundActionImpl<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  RunOptions extends BackgroundActionRunOptions = BackgroundActionRunOptions,\n> implements BackgroundAction<I, O, RunOptions>\n{\n  __action: ActionMetadata<I, O>;\n\n  readonly startAction: Action<I, typeof OperationSchema>;\n  readonly checkAction: Action<typeof OperationSchema, typeof OperationSchema>;\n  readonly cancelAction?: Action<\n    typeof OperationSchema,\n    typeof OperationSchema\n  >;\n\n  constructor(\n    startAction: Action<I, typeof OperationSchema>,\n    checkAction: Action<typeof OperationSchema, typeof OperationSchema>,\n    cancelAction:\n      | Action<typeof OperationSchema, typeof OperationSchema>\n      | undefined\n  ) {\n    this.__action = {\n      name: startAction.__action.name,\n      description: startAction.__action.description,\n      inputSchema: startAction.__action.inputSchema,\n      inputJsonSchema: startAction.__action.inputJsonSchema,\n      metadata: startAction.__action.metadata,\n      actionType: startAction.__action.actionType,\n    };\n    this.startAction = startAction;\n    this.checkAction = checkAction;\n    this.cancelAction = cancelAction;\n  }\n\n  async start(\n    input?: z.infer<I>,\n    options?: RunOptions\n  ): Promise<Operation<z.infer<O>>> {\n    return await this.startAction(input, options);\n  }\n\n  async check(\n    operation: Operation<z.infer<O>>\n  ): Promise<Operation<z.infer<O>>> {\n    return await this.checkAction(operation);\n  }\n\n  get supportsCancel(): boolean {\n    return !!this.cancelAction;\n  }\n\n  async cancel(\n    operation: Operation<z.infer<O>>\n  ): Promise<Operation<z.infer<O>>> {\n    if (!this.cancelAction) {\n      return operation;\n    }\n    return await this.cancelAction(operation);\n  }\n}\n\n/**\n * Options (side channel) data to pass to the model.\n */\nexport interface BackgroundActionRunOptions {\n  /**\n   * Additional runtime context data (ex. auth context data).\n   */\n  context?: ActionContext;\n\n  /**\n   * Additional span attributes to apply to OT spans.\n   */\n  telemetryLabels?: Record<string, string>;\n}\n\n/**\n * Options (side channel) data to pass to the model.\n */\nexport interface BackgroundActionFnArg<S> {\n  /**\n   * Additional runtime context data (ex. auth context data).\n   */\n  context?: ActionContext;\n\n  /**\n   * Trace context containing trace and span IDs.\n   */\n  trace: {\n    traceId: string;\n    spanId: string;\n  };\n}\n\n/**\n * Action factory params.\n */\nexport type BackgroundActionParams<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> = {\n  name: string;\n  start: (\n    input: z.infer<I>,\n    options: BackgroundActionFnArg<z.infer<S>>\n  ) => Promise<Operation<z.infer<O>>>;\n  check: (input: Operation<z.infer<O>>) => Promise<Operation<z.infer<O>>>;\n  cancel?: (input: Operation<z.infer<O>>) => Promise<Operation<z.infer<O>>>;\n  actionType: ActionType;\n\n  description?: string;\n  inputSchema?: I;\n  inputJsonSchema?: JSONSchema7;\n  outputSchema?: O;\n  outputJsonSchema?: JSONSchema7;\n  metadata?: Record<string, any>;\n  use?: Middleware<z.infer<I>, z.infer<O>>[];\n  streamSchema?: S;\n};\n\n/**\n * Defines an action with the given config and registers it in the registry.\n */\nexport function defineBackgroundAction<\n  I extends z.ZodTypeAny,\n  O extends z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  config: BackgroundActionParams<I, O, S>\n): BackgroundAction<I, O> {\n  const startAction = defineAction(\n    registry,\n    {\n      actionType: config.actionType,\n      name: config.name,\n      description: config.description,\n      inputSchema: config.inputSchema,\n      inputJsonSchema: config.inputJsonSchema,\n      outputSchema: OperationSchema,\n      metadata: {\n        ...config.metadata,\n        outputSchema: toJsonSchema({\n          schema: config.outputSchema,\n          jsonSchema: config.outputJsonSchema,\n        }),\n      },\n      use: config.use,\n    },\n    async (input, options) => {\n      const operation = await config.start(input, options);\n      operation.action = `/${config.actionType}/${config.name}`;\n      return operation;\n    }\n  );\n  const checkAction = defineAction(\n    registry,\n    {\n      actionType: 'check-operation',\n      name: `${config.name}/check`,\n      description: config.description,\n      inputSchema: OperationSchema,\n      inputJsonSchema: config.inputJsonSchema,\n      outputSchema: OperationSchema,\n      metadata: {\n        ...config.metadata,\n        outputSchema: toJsonSchema({\n          schema: config.outputSchema,\n          jsonSchema: config.outputJsonSchema,\n        }),\n      },\n    },\n    async (input) => {\n      const operation = await config.check(input);\n      operation.action = `/${config.actionType}/${config.name}`;\n      return operation;\n    }\n  );\n  let cancelAction:\n    | Action<typeof OperationSchema, typeof OperationSchema>\n    | undefined = undefined;\n  if (config.cancel) {\n    cancelAction = defineAction(\n      registry,\n      {\n        actionType: 'cancel-operation',\n        name: `${config.name}/cancel`,\n        description: config.description,\n        inputSchema: OperationSchema,\n        inputJsonSchema: config.inputJsonSchema,\n        outputSchema: OperationSchema,\n        metadata: {\n          ...config.metadata,\n          outputSchema: toJsonSchema({\n            schema: config.outputSchema,\n            jsonSchema: config.outputJsonSchema,\n          }),\n        },\n      },\n      async (input) => {\n        if (!config.cancel) {\n          throw new GenkitError({\n            status: 'UNAVAILABLE',\n            message: `${config.name} does not support cancellation.`,\n          });\n        }\n        const operation = await config.cancel(input);\n        operation.action = `/${config.actionType}/${config.name}`;\n        return operation;\n      }\n    );\n  }\n\n  return new BackgroundActionImpl(startAction, checkAction, cancelAction);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,4BAAA,CAAA;AAAA,SAAA,2BAAA;IAAA,iBAAA,IAAA;IAAA,wBAAA,IAAA;IAAA,wBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAiBA,IAAA,IAAmB;AACnB,IAAA,gBAAiE;AAEjE,IAAA,eAA4B;AAE5B,IAAA,gBAA6B;AAKtB,MAAM,kBAAkB,EAAE,MAAA,CAAO;IACtC,QAAQ,EAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC5B,IAAI,EAAE,MAAA,CAAO;IACb,MAAM,EAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC3B,QAAQ,EAAE,GAAA,CAAI,EAAE,QAAA,CAAS;IACzB,OAAO,EAAE,MAAA,CAAO;QAAE,SAAS,EAAE,MAAA,CAAO;IAAE,CAAC,EAAE,WAAA,CAAY,EAAE,QAAA,CAAS;IAChE,UAAU,EAAE,MAAA,CAAO,EAAE,MAAA,CAAO,GAAG,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS;AACnD,CAAC;AAoCD,eAAsB,uBAIpB,QAAA,EACA,GAAA,EAC6C;IAC7C,MAAM,OAA0C,MAAM,SAAS,YAAA,CAI7D,GAAG;IACL,IAAI,CAAC,KAAM,CAAA,OAAO,KAAA;IAClB,MAAM,aAAa,IAAI,SAAA,CAAU,IAAI,OAAA,CAAQ,KAAK,CAAC,IAAI,CAAC;IACxD,OAAO,IAAI,qBACT,MACA,MAAM,SAAS,YAAA,CAIb,CAAA,iBAAA,EAAoB,UAAU,CAAA,MAAA,CAAQ,GACxC,MAAM,SAAS,YAAA,CAIb,CAAA,kBAAA,EAAqB,UAAU,CAAA,OAAA,CAAS;AAE9C;AAEA,MAAM,qBAKN;IACE,SAAA;IAES,YAAA;IACA,YAAA;IACA,aAAA;IAKT,YACE,WAAA,EACA,WAAA,EACA,YAAA,CAGA;QACA,IAAA,CAAK,QAAA,GAAW;YACd,MAAM,YAAY,QAAA,CAAS,IAAA;YAC3B,aAAa,YAAY,QAAA,CAAS,WAAA;YAClC,aAAa,YAAY,QAAA,CAAS,WAAA;YAClC,iBAAiB,YAAY,QAAA,CAAS,eAAA;YACtC,UAAU,YAAY,QAAA,CAAS,QAAA;YAC/B,YAAY,YAAY,QAAA,CAAS,UAAA;QACnC;QACA,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,YAAA,GAAe;IACtB;IAEA,MAAM,MACJ,KAAA,EACA,OAAA,EACgC;QAChC,OAAO,MAAM,IAAA,CAAK,WAAA,CAAY,OAAO,OAAO;IAC9C;IAEA,MAAM,MACJ,SAAA,EACgC;QAChC,OAAO,MAAM,IAAA,CAAK,WAAA,CAAY,SAAS;IACzC;IAEA,IAAI,iBAA0B;QAC5B,OAAO,CAAC,CAAC,IAAA,CAAK,YAAA;IAChB;IAEA,MAAM,OACJ,SAAA,EACgC;QAChC,IAAI,CAAC,IAAA,CAAK,YAAA,EAAc;YACtB,OAAO;QACT;QACA,OAAO,MAAM,IAAA,CAAK,YAAA,CAAa,SAAS;IAC1C;AACF;AAiEO,SAAS,uBAKd,QAAA,EACA,MAAA,EACwB;IACxB,MAAM,cAAA,CAAA,GAAc,cAAA,YAAA,EAClB,UACA;QACE,YAAY,OAAO,UAAA;QACnB,MAAM,OAAO,IAAA;QACb,aAAa,OAAO,WAAA;QACpB,aAAa,OAAO,WAAA;QACpB,iBAAiB,OAAO,eAAA;QACxB,cAAc;QACd,UAAU;YACR,GAAG,OAAO,QAAA;YACV,cAAA,CAAA,GAAc,cAAA,YAAA,EAAa;gBACzB,QAAQ,OAAO,YAAA;gBACf,YAAY,OAAO,gBAAA;YACrB,CAAC;QACH;QACA,KAAK,OAAO,GAAA;IACd,GACA,OAAO,OAAO,YAAY;QACxB,MAAM,YAAY,MAAM,OAAO,KAAA,CAAM,OAAO,OAAO;QACnD,UAAU,MAAA,GAAS,CAAA,CAAA,EAAI,OAAO,UAAU,CAAA,CAAA,EAAI,OAAO,IAAI,EAAA;QACvD,OAAO;IACT;IAEF,MAAM,cAAA,CAAA,GAAc,cAAA,YAAA,EAClB,UACA;QACE,YAAY;QACZ,MAAM,GAAG,OAAO,IAAI,CAAA,MAAA,CAAA;QACpB,aAAa,OAAO,WAAA;QACpB,aAAa;QACb,iBAAiB,OAAO,eAAA;QACxB,cAAc;QACd,UAAU;YACR,GAAG,OAAO,QAAA;YACV,cAAA,CAAA,GAAc,cAAA,YAAA,EAAa;gBACzB,QAAQ,OAAO,YAAA;gBACf,YAAY,OAAO,gBAAA;YACrB,CAAC;QACH;IACF,GACA,OAAO,UAAU;QACf,MAAM,YAAY,MAAM,OAAO,KAAA,CAAM,KAAK;QAC1C,UAAU,MAAA,GAAS,CAAA,CAAA,EAAI,OAAO,UAAU,CAAA,CAAA,EAAI,OAAO,IAAI,EAAA;QACvD,OAAO;IACT;IAEF,IAAI,eAEY,KAAA;IAChB,IAAI,OAAO,MAAA,EAAQ;QACjB,eAAA,CAAA,GAAe,cAAA,YAAA,EACb,UACA;YACE,YAAY;YACZ,MAAM,GAAG,OAAO,IAAI,CAAA,OAAA,CAAA;YACpB,aAAa,OAAO,WAAA;YACpB,aAAa;YACb,iBAAiB,OAAO,eAAA;YACxB,cAAc;YACd,UAAU;gBACR,GAAG,OAAO,QAAA;gBACV,cAAA,CAAA,GAAc,cAAA,YAAA,EAAa;oBACzB,QAAQ,OAAO,YAAA;oBACf,YAAY,OAAO,gBAAA;gBACrB,CAAC;YACH;QACF,GACA,OAAO,UAAU;YACf,IAAI,CAAC,OAAO,MAAA,EAAQ;gBAClB,MAAM,IAAI,aAAA,WAAA,CAAY;oBACpB,QAAQ;oBACR,SAAS,GAAG,OAAO,IAAI,CAAA,+BAAA,CAAA;gBACzB,CAAC;YACH;YACA,MAAM,YAAY,MAAM,OAAO,MAAA,CAAO,KAAK;YAC3C,UAAU,MAAA,GAAS,CAAA,CAAA,EAAI,OAAO,UAAU,CAAA,CAAA,EAAI,OAAO,IAAI,EAAA;YACvD,OAAO;QACT;IAEJ;IAEA,OAAO,IAAI,qBAAqB,aAAa,aAAa,YAAY;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/registry.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Dotprompt } from 'dotprompt';\nimport { AsyncLocalStorage } from 'node:async_hooks';\nimport type * as z from 'zod';\nimport {\n  runOutsideActionRuntimeContext,\n  type Action,\n  type ActionMetadata,\n} from './action.js';\nimport {\n  BackgroundAction,\n  lookupBackgroundAction,\n} from './background-action.js';\nimport { ActionContext } from './context.js';\nimport { GenkitError } from './error.js';\nimport { logger } from './logging.js';\nimport type { PluginProvider } from './plugin.js';\nimport { toJsonSchema, type JSONSchema } from './schema.js';\n\nexport type AsyncProvider<T> = () => Promise<T>;\n\n/**\n * Type of a runnable action.\n */\nexport type ActionType =\n  | 'custom'\n  | 'embedder'\n  | 'evaluator'\n  | 'executable-prompt'\n  | 'flow'\n  | 'indexer'\n  | 'model'\n  | 'background-model'\n  | 'check-operation'\n  | 'cancel-operation'\n  | 'prompt'\n  | 'reranker'\n  | 'retriever'\n  | 'tool'\n  | 'util'\n  | 'resource';\n\n/**\n * A schema is either a Zod schema or a JSON schema.\n */\nexport interface Schema {\n  schema?: z.ZodTypeAny;\n  jsonSchema?: JSONSchema;\n}\n\nfunction parsePluginName(registryKey: string) {\n  const tokens = registryKey.split('/');\n  if (tokens.length >= 4) {\n    return tokens[2];\n  }\n  return undefined;\n}\n\ninterface ParsedRegistryKey {\n  actionType: ActionType;\n  pluginName?: string;\n  actionName: string;\n}\n\n/**\n * Parses the registry key into key parts as per the key format convention. Ex:\n *  - /model/googleai/gemini-2.0-flash\n *  - /prompt/my-plugin/folder/my-prompt\n *  - /util/generate\n */\nexport function parseRegistryKey(\n  registryKey: string\n): ParsedRegistryKey | undefined {\n  const tokens = registryKey.split('/');\n  if (tokens.length < 3) {\n    // Invalid key format\n    return undefined;\n  }\n  // ex: /model/googleai/gemini-2.0-flash or /prompt/my-plugin/folder/my-prompt\n  if (tokens.length >= 4) {\n    return {\n      actionType: tokens[1] as ActionType,\n      pluginName: tokens[2],\n      actionName: tokens.slice(3).join('/'),\n    };\n  }\n  // ex: /util/generate\n  return {\n    actionType: tokens[1] as ActionType,\n    actionName: tokens[2],\n  };\n}\n\nexport type ActionsRecord = Record<string, Action<z.ZodTypeAny, z.ZodTypeAny>>;\nexport type ActionMetadataRecord = Record<string, ActionMetadata>;\n\n/**\n * The registry is used to store and lookup actions, trace stores, flow state stores, plugins, and schemas.\n */\nexport class Registry {\n  private actionsById: Record<\n    string,\n    | Action<z.ZodTypeAny, z.ZodTypeAny>\n    | PromiseLike<Action<z.ZodTypeAny, z.ZodTypeAny>>\n  > = {};\n  private pluginsByName: Record<string, PluginProvider> = {};\n  private schemasByName: Record<string, Schema> = {};\n  private valueByTypeAndName: Record<string, Record<string, any>> = {};\n  private allPluginsInitialized = false;\n  public apiStability: 'stable' | 'beta' = 'stable';\n\n  readonly asyncStore: AsyncStore;\n  readonly dotprompt: Dotprompt;\n  readonly parent?: Registry;\n  /** Additional runtime context data for flows and tools. */\n  context?: ActionContext;\n\n  constructor(parent?: Registry) {\n    if (parent) {\n      this.parent = parent;\n      this.apiStability = parent?.apiStability;\n      this.asyncStore = parent.asyncStore;\n      this.dotprompt = parent.dotprompt;\n    } else {\n      this.asyncStore = new AsyncStore();\n      this.dotprompt = new Dotprompt({\n        schemaResolver: async (name) => {\n          const resolvedSchema = await this.lookupSchema(name);\n          if (!resolvedSchema) {\n            throw new GenkitError({\n              message: `Schema '${name}' not found`,\n              status: 'NOT_FOUND',\n            });\n          }\n          return toJsonSchema(resolvedSchema);\n        },\n      });\n    }\n  }\n\n  /**\n   * Creates a new registry overlaid onto the provided registry.\n   * @param parent The parent registry.\n   * @returns The new overlaid registry.\n   */\n  static withParent(parent: Registry) {\n    return new Registry(parent);\n  }\n\n  /**\n   * Looks up an action in the registry.\n   * @param key The key of the action to lookup.\n   * @returns The action.\n   */\n  async lookupAction<\n    I extends z.ZodTypeAny,\n    O extends z.ZodTypeAny,\n    R extends Action<I, O>,\n  >(key: string): Promise<R> {\n    // We always try to initialize the plugin first.\n    const parsedKey = parseRegistryKey(key);\n    if (parsedKey?.pluginName && this.pluginsByName[parsedKey.pluginName]) {\n      await this.initializePlugin(parsedKey.pluginName);\n\n      // If we don't see the key in the registry, we try to resolve\n      // the action with the dynamic resolver. If it exists, it will\n      // register the action in the registry.\n      if (!this.actionsById[key]) {\n        await this.resolvePluginAction(\n          parsedKey.pluginName,\n          parsedKey.actionType,\n          parsedKey.actionName\n        );\n      }\n    }\n    return (\n      ((await this.actionsById[key]) as R) || this.parent?.lookupAction(key)\n    );\n  }\n\n  /**\n   * Looks up a background action from the registry.\n   * @param key The key of the action to lookup.\n   * @returns The action.\n   */\n  async lookupBackgroundAction(\n    key: string\n  ): Promise<BackgroundAction | undefined> {\n    return lookupBackgroundAction(this, key);\n  }\n\n  /**\n   * Registers an action in the registry.\n   * @param type The type of the action to register.\n   * @param action The action to register.\n   */\n  registerAction<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n    type: ActionType,\n    action: Action<I, O>\n  ) {\n    if (type !== action.__action.actionType) {\n      throw new GenkitError({\n        status: 'INVALID_ARGUMENT',\n        message: `action type (${type}) does not match type on action (${action.__action.actionType})`,\n      });\n    }\n    const key = `/${type}/${action.__action.name}`;\n    logger.debug(`registering ${key}`);\n    if (this.actionsById.hasOwnProperty(key)) {\n      // TODO: Make this an error!\n      logger.warn(\n        `WARNING: ${key} already has an entry in the registry. Overwriting.`\n      );\n    }\n    this.actionsById[key] = action;\n  }\n\n  /**\n   * Registers an action promise in the registry.\n   */\n  registerActionAsync<I extends z.ZodTypeAny, O extends z.ZodTypeAny>(\n    type: ActionType,\n    name: string,\n    action: PromiseLike<Action<I, O>>\n  ) {\n    const key = `/${type}/${name}`;\n    logger.debug(`registering ${key} (async)`);\n    if (this.actionsById.hasOwnProperty(key)) {\n      // TODO: Make this an error!\n      logger.warn(\n        `WARNING: ${key} already has an entry in the registry. Overwriting.`\n      );\n    }\n    this.actionsById[key] = action;\n  }\n\n  /**\n   * Returns all actions that have been registered in the registry.\n   * @returns All actions in the registry as a map of <key, action>.\n   */\n  async listActions(): Promise<ActionsRecord> {\n    await this.initializeAllPlugins();\n    const actions: Record<string, Action<z.ZodTypeAny, z.ZodTypeAny>> = {};\n    await Promise.all(\n      Object.entries(this.actionsById).map(async ([key, action]) => {\n        actions[key] = await action;\n      })\n    );\n    return {\n      ...(await this.parent?.listActions()),\n      ...actions,\n    };\n  }\n\n  /**\n   * Returns all actions that are resolvable by plugins as well as those that are already\n   * in the registry.\n   *\n   * NOTE: this method should not be used in latency sensitive code paths.\n   * It may rely on \"admin\" API calls such as \"list models\", which may cause increased cold start latency.\n   *\n   * @returns All resolvable action metadata as a map of <key, action metadata>.\n   */\n  async listResolvableActions(): Promise<ActionMetadataRecord> {\n    const resolvableActions = {} as ActionMetadataRecord;\n    // We listActions for all plugins in parallel.\n    await Promise.all(\n      Object.entries(this.pluginsByName).map(async ([pluginName, plugin]) => {\n        if (plugin.listActions) {\n          try {\n            (await plugin.listActions()).forEach((meta) => {\n              if (!meta.name) {\n                throw new GenkitError({\n                  status: 'INVALID_ARGUMENT',\n                  message: `Invalid metadata when listing actions from ${pluginName} - name required`,\n                });\n              }\n              if (!meta.actionType) {\n                throw new GenkitError({\n                  status: 'INVALID_ARGUMENT',\n                  message: `Invalid metadata when listing actions from ${pluginName} - actionType required`,\n                });\n              }\n              resolvableActions[`/${meta.actionType}/${meta.name}`] = meta;\n            });\n          } catch (e) {\n            logger.error(`Error listing actions for ${pluginName}\\n`, e);\n          }\n        }\n      })\n    );\n    // Also add actions that are already registered.\n    for (const [key, action] of Object.entries(await this.listActions())) {\n      resolvableActions[key] = action.__action;\n    }\n    return {\n      ...(await this.parent?.listResolvableActions()),\n      ...resolvableActions,\n    };\n  }\n\n  /**\n   * Initializes all plugins in the registry.\n   */\n  async initializeAllPlugins() {\n    if (this.allPluginsInitialized) {\n      return;\n    }\n    for (const pluginName of Object.keys(this.pluginsByName)) {\n      await this.initializePlugin(pluginName);\n    }\n    this.allPluginsInitialized = true;\n  }\n\n  /**\n   * Registers a plugin provider. This plugin must be initialized before it can be used by calling {@link initializePlugin} or {@link initializeAllPlugins}.\n   * @param name The name of the plugin to register.\n   * @param provider The plugin provider.\n   */\n  registerPluginProvider(name: string, provider: PluginProvider) {\n    if (this.pluginsByName[name]) {\n      throw new Error(`Plugin ${name} already registered`);\n    }\n    this.allPluginsInitialized = false;\n    let cached;\n    let isInitialized = false;\n    this.pluginsByName[name] = {\n      name: provider.name,\n      initializer: () => {\n        if (!isInitialized) {\n          cached = provider.initializer();\n          isInitialized = true;\n        }\n        return cached;\n      },\n      resolver: async (actionType: ActionType, actionName: string) => {\n        if (provider.resolver) {\n          await provider.resolver(actionType, actionName);\n        }\n      },\n      listActions: async () => {\n        if (provider.listActions) {\n          return await provider.listActions();\n        }\n        return [];\n      },\n    };\n  }\n\n  /**\n   * Looks up a plugin.\n   * @param name The name of the plugin to lookup.\n   * @returns The plugin provider.\n   */\n  lookupPlugin(name: string): PluginProvider | undefined {\n    return this.pluginsByName[name] || this.parent?.lookupPlugin(name);\n  }\n\n  /**\n   * Resolves a new Action dynamically by registering it.\n   * @param pluginName The name of the plugin\n   * @param actionType The type of the action\n   * @param actionName The name of the action\n   * @returns\n   */\n  async resolvePluginAction(\n    pluginName: string,\n    actionType: ActionType,\n    actionName: string\n  ) {\n    const plugin = this.pluginsByName[pluginName];\n    if (plugin) {\n      return await runOutsideActionRuntimeContext(this, async () => {\n        if (plugin.resolver) {\n          await plugin.resolver(actionType, actionName);\n        }\n      });\n    }\n  }\n\n  /**\n   * Initializes a plugin already registered with {@link registerPluginProvider}.\n   * @param name The name of the plugin to initialize.\n   * @returns The plugin.\n   */\n  async initializePlugin(name: string) {\n    if (this.pluginsByName[name]) {\n      return await runOutsideActionRuntimeContext(this, () =>\n        this.pluginsByName[name].initializer()\n      );\n    }\n  }\n\n  /**\n   * Registers a schema.\n   * @param name The name of the schema to register.\n   * @param data The schema to register (either a Zod schema or a JSON schema).\n   */\n  registerSchema(name: string, data: Schema) {\n    if (this.schemasByName[name]) {\n      throw new Error(`Schema ${name} already registered`);\n    }\n    this.schemasByName[name] = data;\n  }\n\n  registerValue(type: string, name: string, value: any) {\n    if (!this.valueByTypeAndName[type]) {\n      this.valueByTypeAndName[type] = {};\n    }\n    this.valueByTypeAndName[type][name] = value;\n  }\n\n  async lookupValue<T = unknown>(\n    type: string,\n    key: string\n  ): Promise<T | undefined> {\n    const pluginName = parsePluginName(key);\n    if (!this.valueByTypeAndName[type]?.[key] && pluginName) {\n      await this.initializePlugin(pluginName);\n    }\n    return (\n      (this.valueByTypeAndName[type]?.[key] as T) ||\n      this.parent?.lookupValue(type, key)\n    );\n  }\n\n  async listValues<T>(type: string): Promise<Record<string, T>> {\n    await this.initializeAllPlugins();\n    return {\n      ...((await this.parent?.listValues(type)) || {}),\n      ...(this.valueByTypeAndName[type] || {}),\n    } as Record<string, T>;\n  }\n\n  /**\n   * Looks up a schema.\n   * @param name The name of the schema to lookup.\n   * @returns The schema.\n   */\n  lookupSchema(name: string): Schema | undefined {\n    return this.schemasByName[name] || this.parent?.lookupSchema(name);\n  }\n}\n\n/**\n * Manages AsyncLocalStorage instances in a single place.\n */\nexport class AsyncStore {\n  private asls: Record<string, AsyncLocalStorage<any>> = {};\n\n  getStore<T>(key: string): T | undefined {\n    return this.asls[key]?.getStore();\n  }\n\n  run<T, R>(key: string, store: T, callback: () => R): R {\n    if (!this.asls[key]) {\n      this.asls[key] = new AsyncLocalStorage<T>();\n    }\n    return this.asls[key].run(store, callback);\n  }\n}\n\n/**\n * An object that has a reference to Genkit Registry.\n */\nexport interface HasRegistry {\n  get registry(): Registry;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,mBAAA,CAAA;AAAA,SAAA,kBAAA;IAAA,YAAA,IAAA;IAAA,UAAA,IAAA;IAAA,kBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,mBAA0B;AAC1B,IAAA,0BAAkC;AAElC,IAAA,gBAIO;AACP,IAAA,2BAGO;AAEP,IAAA,eAA4B;AAC5B,IAAA,iBAAuB;AAEvB,IAAA,gBAA8C;AAiC9C,SAAS,gBAAgB,WAAA,EAAqB;IAC5C,MAAM,SAAS,YAAY,KAAA,CAAM,GAAG;IACpC,IAAI,OAAO,MAAA,IAAU,GAAG;QACtB,OAAO,MAAA,CAAO,CAAC,CAAA;IACjB;IACA,OAAO,KAAA;AACT;AAcO,SAAS,iBACd,WAAA,EAC+B;IAC/B,MAAM,SAAS,YAAY,KAAA,CAAM,GAAG;IACpC,IAAI,OAAO,MAAA,GAAS,GAAG;QAErB,OAAO,KAAA;IACT;IAEA,IAAI,OAAO,MAAA,IAAU,GAAG;QACtB,OAAO;YACL,YAAY,MAAA,CAAO,CAAC,CAAA;YACpB,YAAY,MAAA,CAAO,CAAC,CAAA;YACpB,YAAY,OAAO,KAAA,CAAM,CAAC,EAAE,IAAA,CAAK,GAAG;QACtC;IACF;IAEA,OAAO;QACL,YAAY,MAAA,CAAO,CAAC,CAAA;QACpB,YAAY,MAAA,CAAO,CAAC,CAAA;IACtB;AACF;AAQO,MAAM,SAAS;IACZ,cAIJ,CAAC,EAAA;IACG,gBAAgD,CAAC,EAAA;IACjD,gBAAwC,CAAC,EAAA;IACzC,qBAA0D,CAAC,EAAA;IAC3D,wBAAwB,MAAA;IACzB,eAAkC,SAAA;IAEhC,WAAA;IACA,UAAA;IACA,OAAA;IAAA,yDAAA,GAET,QAAA;IAEA,YAAY,MAAA,CAAmB;QAC7B,IAAI,QAAQ;YACV,IAAA,CAAK,MAAA,GAAS;YACd,IAAA,CAAK,YAAA,GAAe,QAAQ;YAC5B,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;YACzB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QAC1B,OAAO;YACL,IAAA,CAAK,UAAA,GAAa,IAAI,WAAW;YACjC,IAAA,CAAK,SAAA,GAAY,IAAI,iBAAA,SAAA,CAAU;gBAC7B,gBAAgB,OAAO,SAAS;oBAC9B,MAAM,iBAAiB,MAAM,IAAA,CAAK,YAAA,CAAa,IAAI;oBACnD,IAAI,CAAC,gBAAgB;wBACnB,MAAM,IAAI,aAAA,WAAA,CAAY;4BACpB,SAAS,CAAA,QAAA,EAAW,IAAI,CAAA,WAAA,CAAA;4BACxB,QAAQ;wBACV,CAAC;oBACH;oBACA,OAAA,CAAA,GAAO,cAAA,YAAA,EAAa,cAAc;gBACpC;YACF,CAAC;QACH;IACF;IAAA;;;;GAAA,GAOA,OAAO,WAAW,MAAA,EAAkB;QAClC,OAAO,IAAI,SAAS,MAAM;IAC5B;IAAA;;;;GAAA,GAOA,MAAM,aAIJ,GAAA,EAAyB;QAEzB,MAAM,YAAY,iBAAiB,GAAG;QACtC,IAAI,WAAW,cAAc,IAAA,CAAK,aAAA,CAAc,UAAU,UAAU,CAAA,EAAG;YACrE,MAAM,IAAA,CAAK,gBAAA,CAAiB,UAAU,UAAU;YAKhD,IAAI,CAAC,IAAA,CAAK,WAAA,CAAY,GAAG,CAAA,EAAG;gBAC1B,MAAM,IAAA,CAAK,mBAAA,CACT,UAAU,UAAA,EACV,UAAU,UAAA,EACV,UAAU,UAAA;YAEd;QACF;QACA,OACI,MAAM,IAAA,CAAK,WAAA,CAAY,GAAG,CAAA,IAAY,IAAA,CAAK,MAAA,EAAQ,aAAa,GAAG;IAEzE;IAAA;;;;GAAA,GAOA,MAAM,uBACJ,GAAA,EACuC;QACvC,OAAA,CAAA,GAAO,yBAAA,sBAAA,EAAuB,IAAA,EAAM,GAAG;IACzC;IAAA;;;;GAAA,GAOA,eACE,IAAA,EACA,MAAA,EACA;QACA,IAAI,SAAS,OAAO,QAAA,CAAS,UAAA,EAAY;YACvC,MAAM,IAAI,aAAA,WAAA,CAAY;gBACpB,QAAQ;gBACR,SAAS,CAAA,aAAA,EAAgB,IAAI,CAAA,iCAAA,EAAoC,OAAO,QAAA,CAAS,UAAU,CAAA,CAAA,CAAA;YAC7F,CAAC;QACH;QACA,MAAM,MAAM,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,OAAO,QAAA,CAAS,IAAI,EAAA;QAC5C,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,YAAA,EAAe,GAAG,EAAE;QACjC,IAAI,IAAA,CAAK,WAAA,CAAY,cAAA,CAAe,GAAG,GAAG;YAExC,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,SAAA,EAAY,GAAG,CAAA,mDAAA,CAAA;QAEnB;QACA,IAAA,CAAK,WAAA,CAAY,GAAG,CAAA,GAAI;IAC1B;IAAA;;GAAA,GAKA,oBACE,IAAA,EACA,IAAA,EACA,MAAA,EACA;QACA,MAAM,MAAM,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,EAAI,IAAI,EAAA;QAC5B,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,YAAA,EAAe,GAAG,CAAA,QAAA,CAAU;QACzC,IAAI,IAAA,CAAK,WAAA,CAAY,cAAA,CAAe,GAAG,GAAG;YAExC,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,SAAA,EAAY,GAAG,CAAA,mDAAA,CAAA;QAEnB;QACA,IAAA,CAAK,WAAA,CAAY,GAAG,CAAA,GAAI;IAC1B;IAAA;;;GAAA,GAMA,MAAM,cAAsC;QAC1C,MAAM,IAAA,CAAK,oBAAA,CAAqB;QAChC,MAAM,UAA8D,CAAC;QACrE,MAAM,QAAQ,GAAA,CACZ,OAAO,OAAA,CAAQ,IAAA,CAAK,WAAW,EAAE,GAAA,CAAI,OAAO,CAAC,KAAK,MAAM,CAAA,KAAM;YAC5D,OAAA,CAAQ,GAAG,CAAA,GAAI,MAAM;QACvB,CAAC;QAEH,OAAO;YACL,GAAI,MAAM,IAAA,CAAK,MAAA,EAAQ,YAAY,CAAA;YACnC,GAAG,OAAA;QACL;IACF;IAAA;;;;;;;;GAAA,GAWA,MAAM,wBAAuD;QAC3D,MAAM,oBAAoB,CAAC;QAE3B,MAAM,QAAQ,GAAA,CACZ,OAAO,OAAA,CAAQ,IAAA,CAAK,aAAa,EAAE,GAAA,CAAI,OAAO,CAAC,YAAY,MAAM,CAAA,KAAM;YACrE,IAAI,OAAO,WAAA,EAAa;gBACtB,IAAI;oBACF,CAAC,MAAM,OAAO,WAAA,CAAY,CAAA,EAAG,OAAA,CAAQ,CAAC,SAAS;wBAC7C,IAAI,CAAC,KAAK,IAAA,EAAM;4BACd,MAAM,IAAI,aAAA,WAAA,CAAY;gCACpB,QAAQ;gCACR,SAAS,CAAA,2CAAA,EAA8C,UAAU,CAAA,gBAAA,CAAA;4BACnE,CAAC;wBACH;wBACA,IAAI,CAAC,KAAK,UAAA,EAAY;4BACpB,MAAM,IAAI,aAAA,WAAA,CAAY;gCACpB,QAAQ;gCACR,SAAS,CAAA,2CAAA,EAA8C,UAAU,CAAA,sBAAA,CAAA;4BACnE,CAAC;wBACH;wBACA,iBAAA,CAAkB,CAAA,CAAA,EAAI,KAAK,UAAU,CAAA,CAAA,EAAI,KAAK,IAAI,EAAE,CAAA,GAAI;oBAC1D,CAAC;gBACH,EAAA,OAAS,GAAG;oBACV,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,0BAAA,EAA6B,UAAU,CAAA;AAAA,CAAA,EAAM,CAAC;gBAC7D;YACF;QACF,CAAC;QAGH,KAAA,MAAW,CAAC,KAAK,MAAM,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,IAAA,CAAK,WAAA,CAAY,CAAC,EAAG;YACpE,iBAAA,CAAkB,GAAG,CAAA,GAAI,OAAO,QAAA;QAClC;QACA,OAAO;YACL,GAAI,MAAM,IAAA,CAAK,MAAA,EAAQ,sBAAsB,CAAA;YAC7C,GAAG,iBAAA;QACL;IACF;IAAA;;GAAA,GAKA,MAAM,uBAAuB;QAC3B,IAAI,IAAA,CAAK,qBAAA,EAAuB;YAC9B;QACF;QACA,KAAA,MAAW,cAAc,OAAO,IAAA,CAAK,IAAA,CAAK,aAAa,EAAG;YACxD,MAAM,IAAA,CAAK,gBAAA,CAAiB,UAAU;QACxC;QACA,IAAA,CAAK,qBAAA,GAAwB;IAC/B;IAAA;;;;GAAA,GAOA,uBAAuB,IAAA,EAAc,QAAA,EAA0B;QAC7D,IAAI,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,EAAG;YAC5B,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,IAAI,CAAA,mBAAA,CAAqB;QACrD;QACA,IAAA,CAAK,qBAAA,GAAwB;QAC7B,IAAI;QACJ,IAAI,gBAAgB;QACpB,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,GAAI;YACzB,MAAM,SAAS,IAAA;YACf,aAAa,MAAM;gBACjB,IAAI,CAAC,eAAe;oBAClB,SAAS,SAAS,WAAA,CAAY;oBAC9B,gBAAgB;gBAClB;gBACA,OAAO;YACT;YACA,UAAU,OAAO,YAAwB,eAAuB;gBAC9D,IAAI,SAAS,QAAA,EAAU;oBACrB,MAAM,SAAS,QAAA,CAAS,YAAY,UAAU;gBAChD;YACF;YACA,aAAa,YAAY;gBACvB,IAAI,SAAS,WAAA,EAAa;oBACxB,OAAO,MAAM,SAAS,WAAA,CAAY;gBACpC;gBACA,OAAO,CAAC,CAAA;YACV;QACF;IACF;IAAA;;;;GAAA,GAOA,aAAa,IAAA,EAA0C;QACrD,OAAO,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,IAAK,IAAA,CAAK,MAAA,EAAQ,aAAa,IAAI;IACnE;IAAA;;;;;;GAAA,GASA,MAAM,oBACJ,UAAA,EACA,UAAA,EACA,UAAA,EACA;QACA,MAAM,SAAS,IAAA,CAAK,aAAA,CAAc,UAAU,CAAA;QAC5C,IAAI,QAAQ;YACV,OAAO,MAAA,CAAA,GAAM,cAAA,8BAAA,EAA+B,IAAA,EAAM,YAAY;gBAC5D,IAAI,OAAO,QAAA,EAAU;oBACnB,MAAM,OAAO,QAAA,CAAS,YAAY,UAAU;gBAC9C;YACF,CAAC;QACH;IACF;IAAA;;;;GAAA,GAOA,MAAM,iBAAiB,IAAA,EAAc;QACnC,IAAI,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,EAAG;YAC5B,OAAO,MAAA,CAAA,GAAM,cAAA,8BAAA,EAA+B,IAAA,EAAM,IAChD,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,CAAE,WAAA,CAAY;QAEzC;IACF;IAAA;;;;GAAA,GAOA,eAAe,IAAA,EAAc,IAAA,EAAc;QACzC,IAAI,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,EAAG;YAC5B,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,IAAI,CAAA,mBAAA,CAAqB;QACrD;QACA,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,GAAI;IAC7B;IAEA,cAAc,IAAA,EAAc,IAAA,EAAc,KAAA,EAAY;QACpD,IAAI,CAAC,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,EAAG;YAClC,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,GAAI,CAAC;QACnC;QACA,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,CAAE,IAAI,CAAA,GAAI;IACxC;IAEA,MAAM,YACJ,IAAA,EACA,GAAA,EACwB;QACxB,MAAM,aAAa,gBAAgB,GAAG;QACtC,IAAI,CAAC,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,EAAA,CAAI,GAAG,CAAA,IAAK,YAAY;YACvD,MAAM,IAAA,CAAK,gBAAA,CAAiB,UAAU;QACxC;QACA,OACG,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,EAAA,CAAI,GAAG,CAAA,IACpC,IAAA,CAAK,MAAA,EAAQ,YAAY,MAAM,GAAG;IAEtC;IAEA,MAAM,WAAc,IAAA,EAA0C;QAC5D,MAAM,IAAA,CAAK,oBAAA,CAAqB;QAChC,OAAO;YACL,GAAK,MAAM,IAAA,CAAK,MAAA,EAAQ,WAAW,IAAI,KAAM,CAAC,CAAA;YAC9C,GAAI,IAAA,CAAK,kBAAA,CAAmB,IAAI,CAAA,IAAK,CAAC,CAAA;QACxC;IACF;IAAA;;;;GAAA,GAOA,aAAa,IAAA,EAAkC;QAC7C,OAAO,IAAA,CAAK,aAAA,CAAc,IAAI,CAAA,IAAK,IAAA,CAAK,MAAA,EAAQ,aAAa,IAAI;IACnE;AACF;AAKO,MAAM,WAAW;IACd,OAA+C,CAAC,EAAA;IAExD,SAAY,GAAA,EAA4B;QACtC,OAAO,IAAA,CAAK,IAAA,CAAK,GAAG,CAAA,EAAG,SAAS;IAClC;IAEA,IAAU,GAAA,EAAa,KAAA,EAAU,QAAA,EAAsB;QACrD,IAAI,CAAC,IAAA,CAAK,IAAA,CAAK,GAAG,CAAA,EAAG;YACnB,IAAA,CAAK,IAAA,CAAK,GAAG,CAAA,GAAI,IAAI,wBAAA,iBAAA,CAAqB;QAC5C;QACA,OAAO,IAAA,CAAK,IAAA,CAAK,GAAG,CAAA,CAAE,GAAA,CAAI,OAAO,QAAQ;IAC3C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2409, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/flow.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AsyncLocalStorage } from 'node:async_hooks';\nimport type { z } from 'zod';\nimport { ActionFnArg, defineAction, type Action } from './action.js';\nimport { Registry, type HasRegistry } from './registry.js';\nimport { SPAN_TYPE_ATTR, runInNewSpan } from './tracing.js';\n\n/**\n * Flow is an observable, streamable, (optionally) strongly typed function.\n */\nexport interface Flow<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> extends Action<I, O, S> {}\n\n/**\n * Configuration for a streaming flow.\n */\nexport interface FlowConfig<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> {\n  /** Name of the flow. */\n  name: string;\n  /** Schema of the input to the flow. */\n  inputSchema?: I;\n  /** Schema of the output from the flow. */\n  outputSchema?: O;\n  /** Schema of the streaming chunks from the flow. */\n  streamSchema?: S;\n  /** Metadata of the flow used by tooling. */\n  metadata?: Record<string, any>;\n}\n\n/**\n * Flow execution context for flow to access the streaming callback and\n * side-channel context data. The context itself is a function, a short-cut\n * for streaming callback.\n */\nexport interface FlowSideChannel<S> extends ActionFnArg<S> {\n  (chunk: S): void;\n}\n\n/**\n * Function to be executed in the flow.\n */\nexport type FlowFn<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n> = (\n  /** Input to the flow. */\n  input: z.infer<I>,\n  /** Callback for streaming functions only. */\n  streamingCallback: FlowSideChannel<z.infer<S>>\n) => Promise<z.infer<O>> | z.infer<O>;\n\n/**\n * Defines a non-streaming flow. This operates on the currently active registry.\n */\nexport function defineFlow<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  config: FlowConfig<I, O, S> | string,\n  fn: FlowFn<I, O, S>\n): Flow<I, O, S> {\n  const resolvedConfig: FlowConfig<I, O, S> =\n    typeof config === 'string' ? { name: config } : config;\n\n  return defineFlowAction(registry, resolvedConfig, fn);\n}\n\n/**\n * Registers a flow as an action in the registry.\n */\nfunction defineFlowAction<\n  I extends z.ZodTypeAny = z.ZodTypeAny,\n  O extends z.ZodTypeAny = z.ZodTypeAny,\n  S extends z.ZodTypeAny = z.ZodTypeAny,\n>(\n  registry: Registry,\n  config: FlowConfig<I, O, S>,\n  fn: FlowFn<I, O, S>\n): Flow<I, O, S> {\n  return defineAction(\n    registry,\n    {\n      actionType: 'flow',\n      name: config.name,\n      inputSchema: config.inputSchema,\n      outputSchema: config.outputSchema,\n      streamSchema: config.streamSchema,\n      metadata: config.metadata,\n    },\n    async (\n      input,\n      { sendChunk, context, trace, abortSignal, streamingRequested }\n    ) => {\n      return await legacyRegistryAls.run(registry, () => {\n        const ctx = sendChunk;\n        (ctx as FlowSideChannel<z.infer<S>>).sendChunk = sendChunk;\n        (ctx as FlowSideChannel<z.infer<S>>).context = context;\n        (ctx as FlowSideChannel<z.infer<S>>).trace = trace;\n        (ctx as FlowSideChannel<z.infer<S>>).abortSignal = abortSignal;\n        (ctx as FlowSideChannel<z.infer<S>>).streamingRequested =\n          streamingRequested;\n        return fn(input, ctx as FlowSideChannel<z.infer<S>>);\n      });\n    }\n  );\n}\n\nconst legacyRegistryAls = new AsyncLocalStorage<Registry>();\n\nexport function run<T>(\n  name: string,\n  func: () => Promise<T>,\n  registry?: Registry\n): Promise<T>;\nexport function run<T>(\n  name: string,\n  input: any,\n  func: (input?: any) => Promise<T>,\n  registry?: Registry\n): Promise<T>;\n\n/**\n * A flow step that executes the provided function. Each run step is recorded separately in the trace.\n */\nexport function run<T>(\n  name: string,\n  funcOrInput: () => Promise<T>,\n  fnOrRegistry?: Registry | HasRegistry | ((input?: any) => Promise<T>),\n  maybeRegistry?: Registry | HasRegistry\n): Promise<T> {\n  let func;\n  let input;\n  let registry: Registry | undefined;\n  if (typeof funcOrInput === 'function') {\n    func = funcOrInput;\n  } else {\n    input = funcOrInput;\n  }\n  if (typeof fnOrRegistry === 'function') {\n    func = fnOrRegistry;\n  } else if (\n    fnOrRegistry instanceof Registry ||\n    (fnOrRegistry as HasRegistry)?.registry\n  ) {\n    registry = (fnOrRegistry as HasRegistry)?.registry\n      ? (fnOrRegistry as HasRegistry)?.registry\n      : (fnOrRegistry as Registry);\n  }\n  if (maybeRegistry) {\n    registry = (maybeRegistry as HasRegistry).registry\n      ? (maybeRegistry as HasRegistry).registry\n      : (maybeRegistry as Registry);\n  }\n\n  if (!registry) {\n    registry = legacyRegistryAls.getStore();\n  }\n  if (!registry) {\n    throw new Error(\n      'Unable to resolve registry. Consider explicitly passing Genkit instance.'\n    );\n  }\n\n  if (!func) {\n    throw new Error('unable to resolve run function');\n  }\n  return runInNewSpan(\n    registry,\n    {\n      metadata: { name },\n      labels: {\n        [SPAN_TYPE_ATTR]: 'flowStep',\n      },\n    },\n    async (meta) => {\n      meta.input = input;\n      const output = arguments.length === 3 ? await func(input) : await func();\n      meta.output = JSON.stringify(output);\n      return output;\n    }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,eAAA,CAAA;AAAA,SAAA,cAAA;IAAA,YAAA,IAAA;IAAA,KAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,0BAAkC;AAElC,IAAA,gBAAuD;AACvD,IAAA,kBAA2C;AAC3C,IAAA,iBAA6C;AAyDtC,SAAS,WAKd,QAAA,EACA,MAAA,EACA,EAAA,EACe;IACf,MAAM,iBACJ,OAAO,WAAW,WAAW;QAAE,MAAM;IAAO,IAAI;IAElD,OAAO,iBAAiB,UAAU,gBAAgB,EAAE;AACtD;AAKA,SAAS,iBAKP,QAAA,EACA,MAAA,EACA,EAAA,EACe;IACf,OAAA,CAAA,GAAO,cAAA,YAAA,EACL,UACA;QACE,YAAY;QACZ,MAAM,OAAO,IAAA;QACb,aAAa,OAAO,WAAA;QACpB,cAAc,OAAO,YAAA;QACrB,cAAc,OAAO,YAAA;QACrB,UAAU,OAAO,QAAA;IACnB,GACA,OACE,OACA,EAAE,SAAA,EAAW,OAAA,EAAS,KAAA,EAAO,WAAA,EAAa,kBAAA,CAAmB,CAAA,KAC1D;QACH,OAAO,MAAM,kBAAkB,GAAA,CAAI,UAAU,MAAM;YACjD,MAAM,MAAM;YACX,IAAoC,SAAA,GAAY;YAChD,IAAoC,OAAA,GAAU;YAC9C,IAAoC,KAAA,GAAQ;YAC5C,IAAoC,WAAA,GAAc;YAClD,IAAoC,kBAAA,GACnC;YACF,OAAO,GAAG,OAAO,GAAkC;QACrD,CAAC;IACH;AAEJ;AAEA,MAAM,oBAAoB,IAAI,wBAAA,iBAAA,CAA4B;AAiBnD,SAAS,IACd,IAAA,EACA,WAAA,EACA,YAAA,EACA,aAAA,EACY;IACZ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,gBAAgB,YAAY;QACrC,OAAO;IACT,OAAO;QACL,QAAQ;IACV;IACA,IAAI,OAAO,iBAAiB,YAAY;QACtC,OAAO;IACT,OAAA,IACE,wBAAwB,gBAAA,QAAA,IACvB,cAA8B,UAC/B;QACA,WAAY,cAA8B,WACrC,cAA8B,WAC9B;IACP;IACA,IAAI,eAAe;QACjB,WAAY,cAA8B,QAAA,GACrC,cAA8B,QAAA,GAC9B;IACP;IAEA,IAAI,CAAC,UAAU;QACb,WAAW,kBAAkB,QAAA,CAAS;IACxC;IACA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MACR;IAEJ;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,gCAAgC;IAClD;IACA,OAAA,CAAA,GAAO,eAAA,YAAA,EACL,UACA;QACE,UAAU;YAAE;QAAK;QACjB,QAAQ;YACN,CAAC,eAAA,cAAc,CAAA,EAAG;QACpB;IACF,GACA,OAAO,SAAS;QACd,KAAK,KAAA,GAAQ;QACb,MAAM,SAAS,UAAU,MAAA,KAAW,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK;QACvE,KAAK,MAAA,GAAS,KAAK,SAAA,CAAU,MAAM;QACnC,OAAO;IACT;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/plugin.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { z } from 'zod';\nimport type { Action, ActionMetadata } from './action.js';\nimport type { ActionType } from './registry.js';\n\nexport interface Provider<T> {\n  id: string;\n  value: T;\n}\n\nexport interface PluginProvider {\n  name: string;\n  initializer: () =>\n    | InitializedPlugin\n    | void\n    | Promise<InitializedPlugin | void>;\n  resolver?: (action: ActionType, target: string) => Promise<void>;\n  listActions?: () => Promise<ActionMetadata[]>;\n}\n\nexport interface InitializedPlugin {\n  models?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  retrievers?: Action<z.Zod<PERSON>ype<PERSON>, z.ZodTypeAny>[];\n  embedders?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  indexers?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  evaluators?: Action<z.ZodTypeAny, z.ZodTypeAny>[];\n  /** @deprecated */\n  flowStateStore?: Provider<any> | Provider<any>[];\n  /** @deprecated */\n  traceStore?: Provider<any> | Provider<any>[];\n  /** @deprecated */\n  telemetry?: any;\n}\n\nexport type Plugin<T extends any[]> = (...args: T) => PluginProvider;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAA,iBAAA,CAAA;AAAA,OAAA,OAAA,GAAA,aAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/reflection.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport express from 'express';\nimport fs from 'fs/promises';\nimport getPort, { makeRange } from 'get-port';\nimport type { Server } from 'http';\nimport path from 'path';\nimport * as z from 'zod';\nimport {\n  StatusCodes,\n  runWithStreamingCallback,\n  type Status,\n} from './action.js';\nimport { GENKIT_REFLECTION_API_SPEC_VERSION, GENKIT_VERSION } from './index.js';\nimport { logger } from './logging.js';\nimport type { Registry } from './registry.js';\nimport { toJsonSchema } from './schema.js';\nimport { flushTracing, setTelemetryServerUrl } from './tracing.js';\n\n// TODO: Move this to common location for schemas.\nexport const RunActionResponseSchema = z.object({\n  result: z.unknown().optional(),\n  error: z.unknown().optional(),\n  telemetry: z\n    .object({\n      traceId: z.string().optional(),\n    })\n    .optional(),\n});\nexport type RunActionResponse = z.infer<typeof RunActionResponseSchema>;\n\nexport interface ReflectionServerOptions {\n  /** Port to run the server on. Actual port may be different if chosen port is occupied. Defaults to 3100. */\n  port?: number;\n  /** Body size limit for the server. Defaults to `30mb`. */\n  bodyLimit?: string;\n  /** Configured environments. Defaults to `dev`. */\n  configuredEnvs?: string[];\n}\n\n/**\n * Reflection server exposes an API for inspecting and interacting with Genkit in development.\n *\n * This is for use in development environments.\n *\n * @hidden\n */\nexport class ReflectionServer {\n  /** List of all running servers needed to be cleaned up on process exit. */\n  private static RUNNING_SERVERS: ReflectionServer[] = [];\n\n  /** Registry instance to be used for API calls. */\n  private registry: Registry;\n  /** Options for the reflection server. */\n  private options: ReflectionServerOptions;\n  /** Port the server is actually running on. This may differ from `options.port` if the original was occupied. Null if server is not running. */\n  private port: number | null = null;\n  /** Express server instance. Null if server is not running. */\n  private server: Server | null = null;\n  /** Path to the runtime file. Null if server is not running. */\n  private runtimeFilePath: string | null = null;\n\n  constructor(registry: Registry, options?: ReflectionServerOptions) {\n    this.registry = registry;\n    this.options = {\n      port: 3100,\n      bodyLimit: '30mb',\n      configuredEnvs: ['dev'],\n      ...options,\n    };\n  }\n\n  /**\n   * Finds a free port to run the server on based on the original chosen port and environment.\n   */\n  async findPort(): Promise<number> {\n    const chosenPort = this.options.port!;\n    const freePort = await getPort({\n      port: makeRange(chosenPort, chosenPort + 100),\n    });\n    if (freePort !== chosenPort) {\n      logger.warn(\n        `Port ${chosenPort} is already in use, using next available port ${freePort} instead.`\n      );\n    }\n    return freePort;\n  }\n\n  /**\n   * Starts the server.\n   *\n   * The server will be registered to be shut down on process exit.\n   */\n  async start() {\n    const server = express();\n\n    server.use(express.json({ limit: this.options.bodyLimit }));\n    server.use((req, res, next) => {\n      res.header('x-genkit-version', GENKIT_VERSION);\n      next();\n    });\n\n    server.get('/api/__health', async (_, response) => {\n      await this.registry.listActions();\n      response.status(200).send('OK');\n    });\n\n    server.get('/api/__quitquitquit', async (_, response) => {\n      logger.debug('Received quitquitquit');\n      response.status(200).send('OK');\n      await this.stop();\n    });\n\n    server.get('/api/actions', async (_, response, next) => {\n      logger.debug('Fetching actions.');\n      try {\n        const actions = await this.registry.listResolvableActions();\n        const convertedActions = {};\n        Object.keys(actions).forEach((key) => {\n          const action = actions[key];\n          convertedActions[key] = {\n            key,\n            name: action.name,\n            description: action.description,\n            metadata: action.metadata,\n          };\n          if (action.inputSchema || action.inputJsonSchema) {\n            convertedActions[key].inputSchema = toJsonSchema({\n              schema: action.inputSchema,\n              jsonSchema: action.inputJsonSchema,\n            });\n          }\n          if (action.outputSchema || action.outputJsonSchema) {\n            convertedActions[key].outputSchema = toJsonSchema({\n              schema: action.outputSchema,\n              jsonSchema: action.outputJsonSchema,\n            });\n          }\n        });\n        response.send(convertedActions);\n      } catch (err) {\n        const { message, stack } = err as Error;\n        next({ message, stack });\n      }\n    });\n\n    server.post('/api/runAction', async (request, response, next) => {\n      const { key, input, context, telemetryLabels } = request.body;\n      const { stream } = request.query;\n      logger.debug(`Running action \\`${key}\\` with stream=${stream}...`);\n      try {\n        const action = await this.registry.lookupAction(key);\n        if (!action) {\n          response.status(404).send(`action ${key} not found`);\n          return;\n        }\n        if (stream === 'true') {\n          try {\n            const callback = (chunk) => {\n              response.write(JSON.stringify(chunk) + '\\n');\n            };\n            const result = await runWithStreamingCallback(\n              this.registry,\n              callback,\n              () => action.run(input, { context, onChunk: callback })\n            );\n            await flushTracing();\n            response.write(\n              JSON.stringify({\n                result: result.result,\n                telemetry: {\n                  traceId: result.telemetry.traceId,\n                },\n              } as RunActionResponse)\n            );\n            response.end();\n          } catch (err) {\n            const { message, stack } = err as Error;\n            // since we're streaming, we must do special error handling here -- the headers are already sent.\n            const errorResponse: Status = {\n              code: StatusCodes.INTERNAL,\n              message,\n              details: {\n                stack,\n              },\n            };\n            if ((err as any).traceId) {\n              errorResponse.details.traceId = (err as any).traceId;\n            }\n            response.write(\n              JSON.stringify({\n                error: errorResponse,\n              } as RunActionResponse)\n            );\n            response.end();\n          }\n        } else {\n          const result = await action.run(input, { context, telemetryLabels });\n          await flushTracing();\n          response.send({\n            result: result.result,\n            telemetry: {\n              traceId: result.telemetry.traceId,\n            },\n          } as RunActionResponse);\n        }\n      } catch (err) {\n        const { message, stack, traceId } = err as any;\n        next({ message, stack, traceId });\n      }\n    });\n\n    server.get('/api/envs', async (_, response) => {\n      response.json(this.options.configuredEnvs);\n    });\n\n    server.post('/api/notify', async (request, response) => {\n      const { telemetryServerUrl, reflectionApiSpecVersion } = request.body;\n      if (!process.env.GENKIT_TELEMETRY_SERVER) {\n        if (typeof telemetryServerUrl === 'string') {\n          setTelemetryServerUrl(telemetryServerUrl);\n          logger.debug(\n            `Connected to telemetry server on ${telemetryServerUrl}`\n          );\n        }\n      }\n      if (reflectionApiSpecVersion !== GENKIT_REFLECTION_API_SPEC_VERSION) {\n        if (\n          !reflectionApiSpecVersion ||\n          reflectionApiSpecVersion < GENKIT_REFLECTION_API_SPEC_VERSION\n        ) {\n          logger.warn(\n            'WARNING: Genkit CLI version may be outdated. Please update `genkit-cli` to the latest version.'\n          );\n        } else {\n          logger.warn(\n            'Genkit CLI is newer than runtime library. Some feature may not be supported. ' +\n              'Consider upgrading your runtime library version (debug info: expected ' +\n              `${GENKIT_REFLECTION_API_SPEC_VERSION}, got ${reflectionApiSpecVersion}).`\n          );\n        }\n      }\n      response.status(200).send('OK');\n    });\n\n    server.use((err, req, res, next) => {\n      logger.error(err.stack);\n      const error = err as Error;\n      const { message, stack } = error;\n      const errorResponse: Status = {\n        code: StatusCodes.INTERNAL,\n        message,\n        details: {\n          stack,\n        },\n      };\n      if (err.traceId) {\n        errorResponse.details.traceId = err.traceId;\n      }\n      res.status(500).json(errorResponse);\n    });\n\n    this.port = await this.findPort();\n    this.server = server.listen(this.port, async () => {\n      logger.debug(\n        `Reflection server (${process.pid}) running on http://localhost:${this.port}`\n      );\n      ReflectionServer.RUNNING_SERVERS.push(this);\n      await this.writeRuntimeFile();\n    });\n  }\n\n  /**\n   * Stops the server and removes it from the list of running servers to clean up on exit.\n   */\n  async stop(): Promise<void> {\n    if (!this.server) {\n      return;\n    }\n    return new Promise<void>(async (resolve, reject) => {\n      await this.cleanupRuntimeFile();\n      this.server!.close(async (err) => {\n        if (err) {\n          logger.error(\n            `Error shutting down reflection server on port ${this.port}: ${err}`\n          );\n          reject(err);\n        }\n        const index = ReflectionServer.RUNNING_SERVERS.indexOf(this);\n        if (index > -1) {\n          ReflectionServer.RUNNING_SERVERS.splice(index, 1);\n        }\n        logger.debug(\n          `Reflection server on port ${this.port} has successfully shut down.`\n        );\n        this.port = null;\n        this.server = null;\n        resolve();\n      });\n    });\n  }\n\n  /**\n   * Writes the runtime file to the project root.\n   */\n  private async writeRuntimeFile() {\n    try {\n      const rootDir = await findProjectRoot();\n      const runtimesDir = path.join(rootDir, '.genkit', 'runtimes');\n      const date = new Date();\n      const time = date.getTime();\n      const timestamp = date.toISOString();\n      this.runtimeFilePath = path.join(\n        runtimesDir,\n        `${process.pid}-${time}.json`\n      );\n      const fileContent = JSON.stringify(\n        {\n          id: process.env.GENKIT_RUNTIME_ID || process.pid.toString(),\n          pid: process.pid,\n          reflectionServerUrl: `http://localhost:${this.port}`,\n          timestamp,\n          genkitVersion: `nodejs/${GENKIT_VERSION}`,\n          reflectionApiSpecVersion: GENKIT_REFLECTION_API_SPEC_VERSION,\n        },\n        null,\n        2\n      );\n      await fs.mkdir(runtimesDir, { recursive: true });\n      await fs.writeFile(this.runtimeFilePath, fileContent, 'utf8');\n      logger.debug(`Runtime file written: ${this.runtimeFilePath}`);\n    } catch (error) {\n      logger.error(`Error writing runtime file: ${error}`);\n    }\n  }\n\n  /**\n   * Cleans up the port file.\n   */\n  private async cleanupRuntimeFile() {\n    if (!this.runtimeFilePath) {\n      return;\n    }\n    try {\n      const fileContent = await fs.readFile(this.runtimeFilePath, 'utf8');\n      const data = JSON.parse(fileContent);\n      if (data.pid === process.pid) {\n        await fs.unlink(this.runtimeFilePath);\n        logger.debug(`Runtime file cleaned up: ${this.runtimeFilePath}`);\n      }\n    } catch (error) {\n      logger.error(`Error cleaning up runtime file: ${error}`);\n    }\n  }\n\n  /**\n   * Stops all running reflection servers.\n   */\n  static async stopAll() {\n    return Promise.all(\n      ReflectionServer.RUNNING_SERVERS.map((server) => server.stop())\n    );\n  }\n}\n\n/**\n * Finds the project root by looking for a `package.json` file.\n */\nasync function findProjectRoot(): Promise<string> {\n  let currentDir = process.cwd();\n  while (currentDir !== path.parse(currentDir).root) {\n    const packageJsonPath = path.join(currentDir, 'package.json');\n    try {\n      await fs.access(packageJsonPath);\n      return currentDir;\n    } catch {\n      currentDir = path.dirname(currentDir);\n    }\n  }\n  throw new Error('Could not find project root (package.json not found)');\n}\n\n// TODO: Verify that this works.\nif (typeof module !== 'undefined' && 'hot' in module) {\n  (module as any).hot.accept();\n  (module as any).hot.dispose(async () => {\n    logger.debug('Cleaning up reflection server(s) before module reload...');\n    await ReflectionServer.stopAll();\n  });\n}\n"], "names": ["getPort", "express", "path", "fs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,qBAAA,CAAA;AAAA,SAAA,oBAAA;IAAA,kBAAA,IAAA;IAAA,yBAAA,IAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,iBAAoB;AACpB,IAAA,kBAAe;AACf,IAAA,kBAAmC;AAEnC,IAAA,cAAiB;AACjB,IAAA,IAAmB;AACnB,IAAA,gBAIO;AACP,IAAA,eAAmE;AACnE,IAAA,iBAAuB;AAEvB,IAAA,gBAA6B;AAC7B,IAAA,iBAAoD;AAG7C,MAAM,0BAA0B,EAAE,MAAA,CAAO;IAC9C,QAAQ,EAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC7B,OAAO,EAAE,OAAA,CAAQ,EAAE,QAAA,CAAS;IAC5B,WAAW,EACR,MAAA,CAAO;QACN,SAAS,EAAE,MAAA,CAAO,EAAE,QAAA,CAAS;IAC/B,CAAC,EACA,QAAA,CAAS;AACd,CAAC;AAmBM,MAAM,iBAAiB;IAAA,yEAAA,GAE5B,OAAe,kBAAsC,CAAC,CAAA,CAAA;IAAA,gDAAA,GAG9C,SAAA;IAAA,uCAAA,GAEA,QAAA;IAAA,6IAAA,GAEA,OAAsB,KAAA;IAAA,4DAAA,GAEtB,SAAwB,KAAA;IAAA,6DAAA,GAExB,kBAAiC,KAAA;IAEzC,YAAY,QAAA,EAAoB,OAAA,CAAmC;QACjE,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,OAAA,GAAU;YACb,MAAM;YACN,WAAW;YACX,gBAAgB;gBAAC,KAAK;aAAA;YACtB,GAAG,OAAA;QACL;IACF;IAAA;;GAAA,GAKA,MAAM,WAA4B;QAChC,MAAM,aAAa,IAAA,CAAK,OAAA,CAAQ,IAAA;QAChC,MAAM,WAAW,MAAA,CAAA,GAAM,gBAAAA,OAAAA,EAAQ;YAC7B,MAAA,CAAA,GAAM,gBAAA,SAAA,EAAU,YAAY,aAAa,GAAG;QAC9C,CAAC;QACD,IAAI,aAAa,YAAY;YAC3B,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,KAAA,EAAQ,UAAU,CAAA,8CAAA,EAAiD,QAAQ,CAAA,SAAA,CAAA;QAE/E;QACA,OAAO;IACT;IAAA;;;;GAAA,GAOA,MAAM,QAAQ;QACZ,MAAM,SAAA,CAAA,GAAS,eAAAC,OAAAA,EAAQ;QAEvB,OAAO,GAAA,CAAI,eAAAA,OAAAA,CAAQ,IAAA,CAAK;YAAE,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAA;QAAU,CAAC,CAAC;QAC1D,OAAO,GAAA,CAAI,CAAC,KAAK,KAAK,SAAS;YAC7B,IAAI,MAAA,CAAO,oBAAoB,aAAA,cAAc;YAC7C,KAAK;QACP,CAAC;QAED,OAAO,GAAA,CAAI,iBAAiB,OAAO,GAAG,aAAa;YACjD,MAAM,IAAA,CAAK,QAAA,CAAS,WAAA,CAAY;YAChC,SAAS,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,IAAI;QAChC,CAAC;QAED,OAAO,GAAA,CAAI,uBAAuB,OAAO,GAAG,aAAa;YACvD,eAAA,MAAA,CAAO,KAAA,CAAM,uBAAuB;YACpC,SAAS,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,IAAI;YAC9B,MAAM,IAAA,CAAK,IAAA,CAAK;QAClB,CAAC;QAED,OAAO,GAAA,CAAI,gBAAgB,OAAO,GAAG,UAAU,SAAS;YACtD,eAAA,MAAA,CAAO,KAAA,CAAM,mBAAmB;YAChC,IAAI;gBACF,MAAM,UAAU,MAAM,IAAA,CAAK,QAAA,CAAS,qBAAA,CAAsB;gBAC1D,MAAM,mBAAmB,CAAC;gBAC1B,OAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,QAAQ;oBACpC,MAAM,SAAS,OAAA,CAAQ,GAAG,CAAA;oBAC1B,gBAAA,CAAiB,GAAG,CAAA,GAAI;wBACtB;wBACA,MAAM,OAAO,IAAA;wBACb,aAAa,OAAO,WAAA;wBACpB,UAAU,OAAO,QAAA;oBACnB;oBACA,IAAI,OAAO,WAAA,IAAe,OAAO,eAAA,EAAiB;wBAChD,gBAAA,CAAiB,GAAG,CAAA,CAAE,WAAA,GAAA,CAAA,GAAc,cAAA,YAAA,EAAa;4BAC/C,QAAQ,OAAO,WAAA;4BACf,YAAY,OAAO,eAAA;wBACrB,CAAC;oBACH;oBACA,IAAI,OAAO,YAAA,IAAgB,OAAO,gBAAA,EAAkB;wBAClD,gBAAA,CAAiB,GAAG,CAAA,CAAE,YAAA,GAAA,CAAA,GAAe,cAAA,YAAA,EAAa;4BAChD,QAAQ,OAAO,YAAA;4BACf,YAAY,OAAO,gBAAA;wBACrB,CAAC;oBACH;gBACF,CAAC;gBACD,SAAS,IAAA,CAAK,gBAAgB;YAChC,EAAA,OAAS,KAAK;gBACZ,MAAM,EAAE,OAAA,EAAS,KAAA,CAAM,CAAA,GAAI;gBAC3B,KAAK;oBAAE;oBAAS;gBAAM,CAAC;YACzB;QACF,CAAC;QAED,OAAO,IAAA,CAAK,kBAAkB,OAAO,SAAS,UAAU,SAAS;YAC/D,MAAM,EAAE,GAAA,EAAK,KAAA,EAAO,OAAA,EAAS,eAAA,CAAgB,CAAA,GAAI,QAAQ,IAAA;YACzD,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,QAAQ,KAAA;YAC3B,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,iBAAA,EAAoB,GAAG,CAAA,eAAA,EAAkB,MAAM,CAAA,GAAA,CAAK;YACjE,IAAI;gBACF,MAAM,SAAS,MAAM,IAAA,CAAK,QAAA,CAAS,YAAA,CAAa,GAAG;gBACnD,IAAI,CAAC,QAAQ;oBACX,SAAS,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,CAAA,OAAA,EAAU,GAAG,CAAA,UAAA,CAAY;oBACnD;gBACF;gBACA,IAAI,WAAW,QAAQ;oBACrB,IAAI;wBACF,MAAM,WAAW,CAAC,UAAU;4BAC1B,SAAS,KAAA,CAAM,KAAK,SAAA,CAAU,KAAK,IAAI,IAAI;wBAC7C;wBACA,MAAM,SAAS,MAAA,CAAA,GAAM,cAAA,wBAAA,EACnB,IAAA,CAAK,QAAA,EACL,UACA,IAAM,OAAO,GAAA,CAAI,OAAO;gCAAE;gCAAS,SAAS;4BAAS,CAAC;wBAExD,MAAA,CAAA,GAAM,eAAA,YAAA,EAAa;wBACnB,SAAS,KAAA,CACP,KAAK,SAAA,CAAU;4BACb,QAAQ,OAAO,MAAA;4BACf,WAAW;gCACT,SAAS,OAAO,SAAA,CAAU,OAAA;4BAC5B;wBACF,CAAsB;wBAExB,SAAS,GAAA,CAAI;oBACf,EAAA,OAAS,KAAK;wBACZ,MAAM,EAAE,OAAA,EAAS,KAAA,CAAM,CAAA,GAAI;wBAE3B,MAAM,gBAAwB;4BAC5B,MAAM,cAAA,WAAA,CAAY,QAAA;4BAClB;4BACA,SAAS;gCACP;4BACF;wBACF;wBACA,IAAK,IAAY,OAAA,EAAS;4BACxB,cAAc,OAAA,CAAQ,OAAA,GAAW,IAAY,OAAA;wBAC/C;wBACA,SAAS,KAAA,CACP,KAAK,SAAA,CAAU;4BACb,OAAO;wBACT,CAAsB;wBAExB,SAAS,GAAA,CAAI;oBACf;gBACF,OAAO;oBACL,MAAM,SAAS,MAAM,OAAO,GAAA,CAAI,OAAO;wBAAE;wBAAS;oBAAgB,CAAC;oBACnE,MAAA,CAAA,GAAM,eAAA,YAAA,EAAa;oBACnB,SAAS,IAAA,CAAK;wBACZ,QAAQ,OAAO,MAAA;wBACf,WAAW;4BACT,SAAS,OAAO,SAAA,CAAU,OAAA;wBAC5B;oBACF,CAAsB;gBACxB;YACF,EAAA,OAAS,KAAK;gBACZ,MAAM,EAAE,OAAA,EAAS,KAAA,EAAO,OAAA,CAAQ,CAAA,GAAI;gBACpC,KAAK;oBAAE;oBAAS;oBAAO;gBAAQ,CAAC;YAClC;QACF,CAAC;QAED,OAAO,GAAA,CAAI,aAAa,OAAO,GAAG,aAAa;YAC7C,SAAS,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,cAAc;QAC3C,CAAC;QAED,OAAO,IAAA,CAAK,eAAe,OAAO,SAAS,aAAa;YACtD,MAAM,EAAE,kBAAA,EAAoB,wBAAA,CAAyB,CAAA,GAAI,QAAQ,IAAA;YACjE,IAAI,CAAC,QAAQ,GAAA,CAAI,uBAAA,EAAyB;gBACxC,IAAI,OAAO,uBAAuB,UAAU;oBAC1C,CAAA,GAAA,eAAA,qBAAA,EAAsB,kBAAkB;oBACxC,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,iCAAA,EAAoC,kBAAkB,EAAA;gBAE1D;YACF;YACA,IAAI,6BAA6B,aAAA,kCAAA,EAAoC;gBACnE,IACE,CAAC,4BACD,2BAA2B,aAAA,kCAAA,EAC3B;oBACA,eAAA,MAAA,CAAO,IAAA,CACL;gBAEJ,OAAO;oBACL,eAAA,MAAA,CAAO,IAAA,CACL,CAAA,mJAAA,EAEK,aAAA,kCAAkC,CAAA,MAAA,EAAS,wBAAwB,CAAA,EAAA,CAAA;gBAE5E;YACF;YACA,SAAS,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,IAAI;QAChC,CAAC;QAED,OAAO,GAAA,CAAI,CAAC,KAAK,KAAK,KAAK,SAAS;YAClC,eAAA,MAAA,CAAO,KAAA,CAAM,IAAI,KAAK;YACtB,MAAM,QAAQ;YACd,MAAM,EAAE,OAAA,EAAS,KAAA,CAAM,CAAA,GAAI;YAC3B,MAAM,gBAAwB;gBAC5B,MAAM,cAAA,WAAA,CAAY,QAAA;gBAClB;gBACA,SAAS;oBACP;gBACF;YACF;YACA,IAAI,IAAI,OAAA,EAAS;gBACf,cAAc,OAAA,CAAQ,OAAA,GAAU,IAAI,OAAA;YACtC;YACA,IAAI,MAAA,CAAO,GAAG,EAAE,IAAA,CAAK,aAAa;QACpC,CAAC;QAED,IAAA,CAAK,IAAA,GAAO,MAAM,IAAA,CAAK,QAAA,CAAS;QAChC,IAAA,CAAK,MAAA,GAAS,OAAO,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,YAAY;YACjD,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,mBAAA,EAAsB,QAAQ,GAAG,CAAA,8BAAA,EAAiC,IAAA,CAAK,IAAI,EAAA;YAE7E,iBAAiB,eAAA,CAAgB,IAAA,CAAK,IAAI;YAC1C,MAAM,IAAA,CAAK,gBAAA,CAAiB;QAC9B,CAAC;IACH;IAAA;;GAAA,GAKA,MAAM,OAAsB;QAC1B,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAChB;QACF;QACA,OAAO,IAAI,QAAc,OAAO,SAAS,WAAW;YAClD,MAAM,IAAA,CAAK,kBAAA,CAAmB;YAC9B,IAAA,CAAK,MAAA,CAAQ,KAAA,CAAM,OAAO,QAAQ;gBAChC,IAAI,KAAK;oBACP,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,8CAAA,EAAiD,IAAA,CAAK,IAAI,CAAA,EAAA,EAAK,GAAG,EAAA;oBAEpE,OAAO,GAAG;gBACZ;gBACA,MAAM,QAAQ,iBAAiB,eAAA,CAAgB,OAAA,CAAQ,IAAI;gBAC3D,IAAI,QAAQ,CAAA,GAAI;oBACd,iBAAiB,eAAA,CAAgB,MAAA,CAAO,OAAO,CAAC;gBAClD;gBACA,eAAA,MAAA,CAAO,KAAA,CACL,CAAA,0BAAA,EAA6B,IAAA,CAAK,IAAI,CAAA,4BAAA,CAAA;gBAExC,IAAA,CAAK,IAAA,GAAO;gBACZ,IAAA,CAAK,MAAA,GAAS;gBACd,QAAQ;YACV,CAAC;QACH,CAAC;IACH;IAAA;;GAAA,GAKA,MAAc,mBAAmB;QAC/B,IAAI;YACF,MAAM,UAAU,MAAM,gBAAgB;YACtC,MAAM,cAAc,YAAAC,OAAAA,CAAK,IAAA,CAAK,SAAS,WAAW,UAAU;YAC5D,MAAM,OAAO,aAAA,GAAA,IAAI,KAAK;YACtB,MAAM,OAAO,KAAK,OAAA,CAAQ;YAC1B,MAAM,YAAY,KAAK,WAAA,CAAY;YACnC,IAAA,CAAK,eAAA,GAAkB,YAAAA,OAAAA,CAAK,IAAA,CAC1B,aACA,GAAG,QAAQ,GAAG,CAAA,CAAA,EAAI,IAAI,CAAA,KAAA,CAAA;YAExB,MAAM,cAAc,KAAK,SAAA,CACvB;gBACE,IAAI,QAAQ,GAAA,CAAI,iBAAA,IAAqB,QAAQ,GAAA,CAAI,QAAA,CAAS;gBAC1D,KAAK,QAAQ,GAAA;gBACb,qBAAqB,CAAA,iBAAA,EAAoB,IAAA,CAAK,IAAI,EAAA;gBAClD;gBACA,eAAe,CAAA,OAAA,EAAU,aAAA,cAAc,EAAA;gBACvC,0BAA0B,aAAA,kCAAA;YAC5B,GACA,MACA;YAEF,MAAM,gBAAAC,OAAAA,CAAG,KAAA,CAAM,aAAa;gBAAE,WAAW;YAAK,CAAC;YAC/C,MAAM,gBAAAA,OAAAA,CAAG,SAAA,CAAU,IAAA,CAAK,eAAA,EAAiB,aAAa,MAAM;YAC5D,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,sBAAA,EAAyB,IAAA,CAAK,eAAe,EAAE;QAC9D,EAAA,OAAS,OAAO;YACd,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,4BAAA,EAA+B,KAAK,EAAE;QACrD;IACF;IAAA;;GAAA,GAKA,MAAc,qBAAqB;QACjC,IAAI,CAAC,IAAA,CAAK,eAAA,EAAiB;YACzB;QACF;QACA,IAAI;YACF,MAAM,cAAc,MAAM,gBAAAA,OAAAA,CAAG,QAAA,CAAS,IAAA,CAAK,eAAA,EAAiB,MAAM;YAClE,MAAM,OAAO,KAAK,KAAA,CAAM,WAAW;YACnC,IAAI,KAAK,GAAA,KAAQ,QAAQ,GAAA,EAAK;gBAC5B,MAAM,gBAAAA,OAAAA,CAAG,MAAA,CAAO,IAAA,CAAK,eAAe;gBACpC,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,yBAAA,EAA4B,IAAA,CAAK,eAAe,EAAE;YACjE;QACF,EAAA,OAAS,OAAO;YACd,eAAA,MAAA,CAAO,KAAA,CAAM,CAAA,gCAAA,EAAmC,KAAK,EAAE;QACzD;IACF;IAAA;;GAAA,GAKA,aAAa,UAAU;QACrB,OAAO,QAAQ,GAAA,CACb,iBAAiB,eAAA,CAAgB,GAAA,CAAI,CAAC,SAAW,OAAO,IAAA,CAAK,CAAC;IAElE;AACF;AAKA,eAAe,kBAAmC;IAChD,IAAI,aAAa,QAAQ,GAAA,CAAI;IAC7B,MAAO,eAAe,YAAAD,OAAAA,CAAK,KAAA,CAAM,UAAU,EAAE,IAAA,CAAM;QACjD,MAAM,kBAAkB,YAAAA,OAAAA,CAAK,IAAA,CAAK,YAAY,cAAc;QAC5D,IAAI;YACF,MAAM,gBAAAC,OAAAA,CAAG,MAAA,CAAO,eAAe;YAC/B,OAAO;QACT,EAAA,OAAQ;YACN,aAAa,YAAAD,OAAAA,CAAK,OAAA,CAAQ,UAAU;QACtC;IACF;IACA,MAAM,IAAI,MAAM,sDAAsD;AACxE;AAGA,IAAI,OAAO,wCAAW,eAAe,SAAS,QAAQ;IACnD,OAAe,GAAA,CAAI,MAAA,CAAO;IAC1B,OAAe,GAAA,CAAI,OAAA,CAAQ,YAAY;QACtC,eAAA,MAAA,CAAO,KAAA,CAAM,0DAA0D;QACvE,MAAM,iBAAiB,OAAA,CAAQ;IACjC,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2899, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/telemetryTypes.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { NodeSDKConfiguration } from '@opentelemetry/sdk-node';\n\n/**\n * Provides a {NodeSDKConfiguration} configuration for use with the\n * Open-Telemetry SDK. This configuration allows plugins to specify how and\n * where open telemetry data will be exported.\n */\nexport type TelemetryConfig = Partial<NodeSDKConfiguration>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAA,yBAAA,CAAA;AAAA,OAAA,OAAA,GAAA,aAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2923, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from './__codegen/version.js';\n\n/**\n * Genkit library version.\n */\nexport const GENKIT_VERSION = version;\n\n/**\n * Genkit client header for API calls.\n */\nexport const GENKIT_CLIENT_HEADER = `genkit-node/${GENKIT_VERSION} gl-node/${process.versions.node}`;\nexport const GENKIT_REFLECTION_API_SPEC_VERSION = 1;\n\nexport { z } from 'zod';\nexport * from './action.js';\nexport {\n  OperationSchema,\n  defineBackgroundAction,\n  type BackgroundAction,\n  type BackgroundActionFnArg,\n  type BackgroundActionParams,\n  type BackgroundActionRunOptions,\n  type Operation,\n} from './background-action.js';\nexport {\n  apiKey,\n  getContext,\n  runWithContext,\n  type ActionContext,\n  type ApiKeyContext,\n  type ContextProvider,\n  type RequestData,\n} from './context.js';\nexport {\n  GenkitError,\n  UnstableApiError,\n  UserFacingError,\n  assertUnstable,\n  getCallableJSON,\n  getHttpStatus,\n  type StatusName,\n} from './error.js';\nexport {\n  defineFlow,\n  run,\n  type Flow,\n  type FlowConfig,\n  type FlowFn,\n  type FlowSideChannel,\n} from './flow.js';\nexport * from './plugin.js';\nexport * from './reflection.js';\nexport { defineJsonSchema, defineSchema, type JSONSchema } from './schema.js';\nexport * from './telemetryTypes.js';\nexport * from './utils.js';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,gBAAA,CAAA;AAAA,SAAA,eAAA;IAAA,sBAAA,IAAA;IAAA,oCAAA,IAAA;IAAA,gBAAA,IAAA;IAAA,aAAA,IAAA,aAAA,WAAA;IAAA,iBAAA,IAAA,yBAAA,eAAA;IAAA,kBAAA,IAAA,aAAA,gBAAA;IAAA,iBAAA,IAAA,aAAA,eAAA;IAAA,QAAA,IAAA,eAAA,MAAA;IAAA,gBAAA,IAAA,aAAA,cAAA;IAAA,wBAAA,IAAA,yBAAA,sBAAA;IAAA,YAAA,IAAA,YAAA,UAAA;IAAA,kBAAA,IAAA,cAAA,gBAAA;IAAA,cAAA,IAAA,cAAA,YAAA;IAAA,iBAAA,IAAA,aAAA,eAAA;IAAA,YAAA,IAAA,eAAA,UAAA;IAAA,eAAA,IAAA,aAAA,aAAA;IAAA,KAAA,IAAA,YAAA,GAAA;IAAA,gBAAA,IAAA,eAAA,cAAA;IAAA,GAAA,IAAA,WAAA,CAAA;AAAA;AAAA,OAAA,OAAA,GAAA,aAAA;AAgBA,IAAA,iBAAwB;AAaxB,IAAA,aAAkB;AAClB,WAAA,eAAc,kJA9Bd,OAAA,OAAA;AA+BA,IAAA,2BAQO;AACP,IAAA,iBAQO;AACP,IAAA,eAQO;AACP,IAAA,cAOO;AACP,WAAA,eAAc,kJAlEd,OAAA,OAAA;AAmEA,WAAA,eAAc,sJAnEd,OAAA,OAAA;AAoEA,IAAA,gBAAgE;AAChE,WAAA,eAAc,0JArEd,OAAA,OAAA;AAsEA,WAAA,eAAc,iJAtEd,OAAA,OAAA;AAqBO,MAAM,iBAAiB,eAAA,OAAA;AAKvB,MAAM,uBAAuB,CAAA,YAAA,EAAe,cAAc,CAAA,SAAA,EAAY,QAAQ,QAAA,CAAS,IAAI,EAAA;AAC3F,MAAM,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3018, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/logging.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\n\nconst loggerKey = '__genkit_logger';\n\nconst _defaultLogger = {\n  shouldLog(targetLevel: string) {\n    return LOG_LEVELS.indexOf(this.level) <= LOG_LEVELS.indexOf(targetLevel);\n  },\n  debug(...args: any) {\n    this.shouldLog('debug') && console.debug(...args);\n  },\n  info(...args: any) {\n    this.shouldLog('info') && console.info(...args);\n  },\n  warn(...args: any) {\n    this.shouldLog('warn') && console.warn(...args);\n  },\n  error(...args: any) {\n    this.shouldLog('error') && console.error(...args);\n  },\n  level: 'info',\n};\n\nfunction getLogger() {\n  if (!global[loggerKey]) {\n    global[loggerKey] = _defaultLogger;\n  }\n  return global[loggerKey];\n}\n\nclass Logger {\n  readonly defaultLogger = _defaultLogger;\n\n  init(fn: any) {\n    global[loggerKey] = fn;\n  }\n\n  info(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().info.apply(getLogger(), args);\n  }\n  debug(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().debug.apply(getLogger(), args);\n  }\n  error(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().error.apply(getLogger(), args);\n  }\n  warn(...args: any) {\n    // eslint-disable-next-line prefer-spread\n    getLogger().warn.apply(getLogger(), args);\n  }\n\n  setLogLevel(level: 'error' | 'warn' | 'info' | 'debug') {\n    getLogger().level = level;\n  }\n\n  logStructured(msg: string, metadata: any) {\n    getLogger().info(msg, metadata);\n  }\n\n  logStructuredError(msg: string, metadata: any) {\n    getLogger().error(msg, metadata);\n  }\n}\n\n/**\n * Genkit logger.\n *\n * ```ts\n * import { logger } from 'genkit/logging';\n *\n * logger.setLogLevel('debug');\n * ```\n */\nexport const logger = new Logger();\n"], "names": [], "mappings": ";;;AAgBA,MAAM,aAAa;IAAC;IAAS;IAAQ;IAAQ,OAAO;CAAA;AAEpD,MAAM,YAAY;AAElB,MAAM,iBAAiB;IACrB,WAAU,WAAA,EAAqB;QAC7B,OAAO,WAAW,OAAA,CAAQ,IAAA,CAAK,KAAK,KAAK,WAAW,OAAA,CAAQ,WAAW;IACzE;IACA,OAAA,GAAS,IAAA,EAAW;QAClB,IAAA,CAAK,SAAA,CAAU,OAAO,KAAK,QAAQ,KAAA,CAAM,GAAG,IAAI;IAClD;IACA,MAAA,GAAQ,IAAA,EAAW;QACjB,IAAA,CAAK,SAAA,CAAU,MAAM,KAAK,QAAQ,IAAA,CAAK,GAAG,IAAI;IAChD;IACA,MAAA,GAAQ,IAAA,EAAW;QACjB,IAAA,CAAK,SAAA,CAAU,MAAM,KAAK,QAAQ,IAAA,CAAK,GAAG,IAAI;IAChD;IACA,OAAA,GAAS,IAAA,EAAW;QAClB,IAAA,CAAK,SAAA,CAAU,OAAO,KAAK,QAAQ,KAAA,CAAM,GAAG,IAAI;IAClD;IACA,OAAO;AACT;AAEA,SAAS,YAAY;IACnB,IAAI,CAAC,MAAA,CAAO,SAAS,CAAA,EAAG;QACtB,MAAA,CAAO,SAAS,CAAA,GAAI;IACtB;IACA,OAAO,MAAA,CAAO,SAAS,CAAA;AACzB;AAEA,MAAM,OAAO;IACF,gBAAgB,eAAA;IAEzB,KAAK,EAAA,EAAS;QACZ,MAAA,CAAO,SAAS,CAAA,GAAI;IACtB;IAEA,KAAA,GAAQ,IAAA,EAAW;QAEjB,UAAU,EAAE,IAAA,CAAK,KAAA,CAAM,UAAU,GAAG,IAAI;IAC1C;IACA,MAAA,GAAS,IAAA,EAAW;QAElB,UAAU,EAAE,KAAA,CAAM,KAAA,CAAM,UAAU,GAAG,IAAI;IAC3C;IACA,MAAA,GAAS,IAAA,EAAW;QAElB,UAAU,EAAE,KAAA,CAAM,KAAA,CAAM,UAAU,GAAG,IAAI;IAC3C;IACA,KAAA,GAAQ,IAAA,EAAW;QAEjB,UAAU,EAAE,IAAA,CAAK,KAAA,CAAM,UAAU,GAAG,IAAI;IAC1C;IAEA,YAAY,KAAA,EAA4C;QACtD,UAAU,EAAE,KAAA,GAAQ;IACtB;IAEA,cAAc,GAAA,EAAa,QAAA,EAAe;QACxC,UAAU,EAAE,IAAA,CAAK,KAAK,QAAQ;IAChC;IAEA,mBAAmB,GAAA,EAAa,QAAA,EAAe;QAC7C,UAAU,EAAE,KAAA,CAAM,KAAK,QAAQ;IACjC;AACF;AAWO,MAAM,SAAS,IAAI,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/index.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from './__codegen/version.js';\n\n/**\n * Genkit library version.\n */\nexport const GENKIT_VERSION = version;\n\n/**\n * Genkit client header for API calls.\n */\nexport const GENKIT_CLIENT_HEADER = `genkit-node/${GENKIT_VERSION} gl-node/${process.versions.node}`;\nexport const GENKIT_REFLECTION_API_SPEC_VERSION = 1;\n\nexport { z } from 'zod';\nexport * from './action.js';\nexport {\n  OperationSchema,\n  defineBackgroundAction,\n  type BackgroundAction,\n  type BackgroundActionFnArg,\n  type BackgroundActionParams,\n  type BackgroundActionRunOptions,\n  type Operation,\n} from './background-action.js';\nexport {\n  apiKey,\n  getContext,\n  runWithContext,\n  type ActionContext,\n  type ApiKeyContext,\n  type ContextProvider,\n  type RequestData,\n} from './context.js';\nexport {\n  GenkitError,\n  UnstableApiError,\n  UserFacingError,\n  assertUnstable,\n  getCallableJSON,\n  getHttpStatus,\n  type StatusName,\n} from './error.js';\nexport {\n  defineFlow,\n  run,\n  type Flow,\n  type FlowConfig,\n  type FlowFn,\n  type FlowSideChannel,\n} from './flow.js';\nexport * from './plugin.js';\nexport * from './reflection.js';\nexport { defineJsonSchema, defineSchema, type JSONSchema } from './schema.js';\nexport * from './telemetryTypes.js';\nexport * from './utils.js';\n"], "names": [], "mappings": ";;;;;AAgBA,SAAS,eAAe;AAcxB,cAAc;AACd;AASA;AASA;AASA;AAQA,cAAc;AACd,cAAc;AACd,SAAS,kBAAkB,oBAAqC;AAChE,cAAc;AACd,cAAc;;AAjDP,MAAM,8PAAiB,UAAA;AAKvB,MAAM,uBAAuB,CAAA,YAAA,EAAe,cAAc,CAAA,SAAA,EAAY,QAAQ,QAAA,CAAS,IAAI,EAAA;AAC3F,MAAM,qCAAqC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/%40genkit-ai%2Bcore%401.14.0/node_modules/%40genkit-ai/core/src/schema.ts"], "sourcesContent": ["/**\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport Ajv, { type ErrorObject, type JSONSchemaType } from 'ajv';\nimport addFormats from 'ajv-formats';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { GenkitError } from './error.js';\nimport type { Registry } from './registry.js';\nconst ajv = new Ajv();\naddFormats(ajv);\n\nexport { z }; // provide a consistent zod to use throughout genkit\n\n/**\n * JSON schema.\n */\nexport type JSONSchema = JSONSchemaType<any> | any;\n\nconst jsonSchemas = new WeakMap<z.ZodTypeAny, JSONSchema>();\nconst validators = new WeakMap<JSONSchema, ReturnType<typeof ajv.compile>>();\n\n/**\n * Wrapper object for various ways schema can be provided.\n */\nexport interface ProvidedSchema {\n  jsonSchema?: JSONSchema;\n  schema?: z.ZodTypeAny;\n}\n\n/**\n * Schema validation error.\n */\nexport class ValidationError extends GenkitError {\n  constructor({\n    data,\n    errors,\n    schema,\n  }: {\n    data: any;\n    errors: ValidationErrorDetail[];\n    schema: JSONSchema;\n  }) {\n    super({\n      status: 'INVALID_ARGUMENT',\n      message: `Schema validation failed. Parse Errors:\\n\\n${errors.map((e) => `- ${e.path}: ${e.message}`).join('\\n')}\\n\\nProvided data:\\n\\n${JSON.stringify(data, null, 2)}\\n\\nRequired JSON schema:\\n\\n${JSON.stringify(schema, null, 2)}`,\n      detail: { errors, schema },\n    });\n  }\n}\n\n/**\n * Convertes a Zod schema into a JSON schema, utilizing an in-memory cache for known objects.\n * @param options Provide a json schema and/or zod schema. JSON schema has priority.\n * @returns A JSON schema.\n */\nexport function toJsonSchema({\n  jsonSchema,\n  schema,\n}: ProvidedSchema): JSONSchema | undefined {\n  // if neither jsonSchema or schema is present return undefined\n  if (!jsonSchema && !schema) return null;\n  if (jsonSchema) return jsonSchema;\n  if (jsonSchemas.has(schema!)) return jsonSchemas.get(schema!)!;\n  const outSchema = zodToJsonSchema(schema!, {\n    $refStrategy: 'none',\n    removeAdditionalStrategy: 'strict',\n  });\n  jsonSchemas.set(schema!, outSchema as JSONSchema);\n  return outSchema as JSONSchema;\n}\n\n/**\n * Schema validation error details.\n */\nexport interface ValidationErrorDetail {\n  path: string;\n  message: string;\n}\n\nfunction toErrorDetail(error: ErrorObject): ValidationErrorDetail {\n  return {\n    path: error.instancePath.substring(1).replace(/\\//g, '.') || '(root)',\n    message: error.message!,\n  };\n}\n\n/**\n * Validation response.\n */\nexport type ValidationResponse =\n  | { valid: true; errors: never }\n  | { valid: false; errors: ErrorObject[] };\n\n/**\n * Validates the provided data against the provided schema.\n */\nexport function validateSchema(\n  data: unknown,\n  options: ProvidedSchema\n): { valid: boolean; errors?: any[]; schema: JSONSchema } {\n  const toValidate = toJsonSchema(options);\n  if (!toValidate) {\n    return { valid: true, schema: toValidate };\n  }\n  const validator = validators.get(toValidate) || ajv.compile(toValidate);\n  const valid = validator(data) as boolean;\n  const errors = validator.errors?.map((e) => e);\n  return { valid, errors: errors?.map(toErrorDetail), schema: toValidate };\n}\n\n/**\n * Parses raw data object agaisnt the provided schema.\n */\nexport function parseSchema<T = unknown>(\n  data: unknown,\n  options: ProvidedSchema\n): T {\n  const { valid, errors, schema } = validateSchema(data, options);\n  if (!valid) throw new ValidationError({ data, errors: errors!, schema });\n  return data as T;\n}\n\n/**\n * Registers provided schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineSchema<T extends z.ZodTypeAny>(\n  registry: Registry,\n  name: string,\n  schema: T\n): T {\n  registry.registerSchema(name, { schema });\n  return schema;\n}\n\n/**\n * Registers provided JSON schema as a named schema object in the Genkit registry.\n *\n * @hidden\n */\nexport function defineJsonSchema(\n  registry: Registry,\n  name: string,\n  jsonSchema: JSONSchema\n) {\n  registry.registerSchema(name, { jsonSchema });\n  return jsonSchema;\n}\n"], "names": [], "mappings": ";;;;;;;;AAgBA,OAAO,SAAoD;AAC3D,OAAO,gBAAgB;AAEvB,OAAO,qBAAqB;;AAC5B,SAAS,mBAAmB;;;;;;AAE5B,MAAM,MAAM,wLAAI,UAAA,CAAI;mOACpB,UAAA,EAAW,GAAG;AASd,MAAM,cAAc,aAAA,GAAA,IAAI,QAAkC;AAC1D,MAAM,aAAa,aAAA,GAAA,IAAI,QAAoD;AAapE,MAAM,mPAAwB,cAAA,CAAY;IAC/C,YAAY,EACV,IAAA,EACA,MAAA,EACA,MAAA,EACF,CAIG;QACD,KAAA,CAAM;YACJ,QAAQ;YACR,SAAS,CAAA;;AAAA,EAA8C,OAAO,GAAA,CAAI,CAAC,IAAM,CAAA,EAAA,EAAK,EAAE,IAAI,CAAA,EAAA,EAAK,EAAE,OAAO,EAAE,EAAE,IAAA,CAAK,IAAI,CAAC,CAAA;;;;AAAA,EAAyB,KAAK,SAAA,CAAU,MAAM,MAAM,CAAC,CAAC,CAAA;;;;AAAA,EAAgC,KAAK,SAAA,CAAU,QAAQ,MAAM,CAAC,CAAC,EAAA;YACrO,QAAQ;gBAAE;gBAAQ;YAAO;QAC3B,CAAC;IACH;AACF;AAOO,SAAS,aAAa,EAC3B,UAAA,EACA,MAAA,EACF,EAA2C;IAEzC,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAA,OAAO;IACnC,IAAI,WAAY,CAAA,OAAO;IACvB,IAAI,YAAY,GAAA,CAAI,MAAO,EAAG,CAAA,OAAO,YAAY,GAAA,CAAI,MAAO;IAC5D,MAAM,aAAY,+RAAA,EAAgB,QAAS;QACzC,cAAc;QACd,0BAA0B;IAC5B,CAAC;IACD,YAAY,GAAA,CAAI,QAAS,SAAuB;IAChD,OAAO;AACT;AAUA,SAAS,cAAc,KAAA,EAA2C;IAChE,OAAO;QACL,MAAM,MAAM,YAAA,CAAa,SAAA,CAAU,CAAC,EAAE,OAAA,CAAQ,OAAO,GAAG,KAAK;QAC7D,SAAS,MAAM,OAAA;IACjB;AACF;AAYO,SAAS,eACd,IAAA,EACA,OAAA,EACwD;IACxD,MAAM,aAAa,aAAa,OAAO;IACvC,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAW;IAC3C;IACA,MAAM,YAAY,WAAW,GAAA,CAAI,UAAU,KAAK,IAAI,OAAA,CAAQ,UAAU;IACtE,MAAM,QAAQ,UAAU,IAAI;IAC5B,MAAM,SAAS,UAAU,MAAA,EAAQ,IAAI,CAAC,IAAM,CAAC;IAC7C,OAAO;QAAE;QAAO,QAAQ,QAAQ,IAAI,aAAa;QAAG,QAAQ;IAAW;AACzE;AAKO,SAAS,YACd,IAAA,EACA,OAAA,EACG;IACH,MAAM,EAAE,KAAA,EAAO,MAAA,EAAQ,MAAA,CAAO,CAAA,GAAI,eAAe,MAAM,OAAO;IAC9D,IAAI,CAAC,MAAO,CAAA,MAAM,IAAI,gBAAgB;QAAE;QAAM;QAAiB;IAAO,CAAC;IACvE,OAAO;AACT;AAOO,SAAS,aACd,QAAA,EACA,IAAA,EACA,MAAA,EACG;IACH,SAAS,cAAA,CAAe,MAAM;QAAE;IAAO,CAAC;IACxC,OAAO;AACT;AAOO,SAAS,iBACd,QAAA,EACA,IAAA,EACA,UAAA,EACA;IACA,SAAS,cAAA,CAAe,MAAM;QAAE;IAAW,CAAC;IAC5C,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}