{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,4WAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,kXAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,kXAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kXAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,qZAAC,kXAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,kXAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,kXAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kXAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,kXAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,qZAAC,gRAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,kXAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,kXAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kXAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,kXAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kXAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,qZAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,qZAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,qZAAC;4BAAI,WAAU;;gCACZ,uBAAS,qZAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,qZAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,qZAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,qZAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\n\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\nlet userIsLoggedIn = false;\nlet userProjects: Project[] = [];\n\nexport async function login(data: any) {\n  console.log(\"Login attempt:\", data);\n  // In a real app, you'd verify credentials against a database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: '<EMAIL>' } };\n}\n\nexport async function logout() {\n  userIsLoggedIn = false;\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  console.log(\"Registration attempt:\", data);\n  // In a real app, you'd create a new user in the database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: data.email } };\n}\n\nexport async function getAuthState() {\n    return { isLoggedIn: userIsLoggedIn };\n}\n\nexport async function saveProject(project: Project) {\n  if (!userIsLoggedIn) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n  // In a real app, you'd save this to a database like Firestore.\n  console.log(\"Saving project:\", project.name);\n  const existingIndex = userProjects.findIndex(p => p.id === project.id);\n  if (existingIndex > -1) {\n    userProjects[existingIndex] = project;\n  } else {\n    userProjects.push(project);\n  }\n  return { success: true, message: \"Project saved successfully!\" };\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const result = await writingImprovementSuggestions(input);\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(error);\n    return { success: false, error: \"Failed to get analysis. Please try again.\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const character = await genChar();\n        return { success: true, character };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate character. Please try again.\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const outline = await genNovelOutline(input);\n        return { success: true, outline };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate novel outline. Please try again.\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IA+BsB,eAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\n\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\nlet userIsLoggedIn = false;\nlet userProjects: Project[] = [];\n\nexport async function login(data: any) {\n  console.log(\"Login attempt:\", data);\n  // In a real app, you'd verify credentials against a database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: '<EMAIL>' } };\n}\n\nexport async function logout() {\n  userIsLoggedIn = false;\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  console.log(\"Registration attempt:\", data);\n  // In a real app, you'd create a new user in the database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: data.email } };\n}\n\nexport async function getAuthState() {\n    return { isLoggedIn: userIsLoggedIn };\n}\n\nexport async function saveProject(project: Project) {\n  if (!userIsLoggedIn) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n  // In a real app, you'd save this to a database like Firestore.\n  console.log(\"Saving project:\", project.name);\n  const existingIndex = userProjects.findIndex(p => p.id === project.id);\n  if (existingIndex > -1) {\n    userProjects[existingIndex] = project;\n  } else {\n    userProjects.push(project);\n  }\n  return { success: true, message: \"Project saved successfully!\" };\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const result = await writingImprovementSuggestions(input);\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(error);\n    return { success: false, error: \"Failed to get analysis. Please try again.\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const character = await genChar();\n        return { success: true, character };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate character. Please try again.\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const outline = await genNovelOutline(input);\n        return { success: true, outline };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate novel outline. Please try again.\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IAYsB,QAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\n\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\nlet userIsLoggedIn = false;\nlet userProjects: Project[] = [];\n\nexport async function login(data: any) {\n  console.log(\"Login attempt:\", data);\n  // In a real app, you'd verify credentials against a database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: '<EMAIL>' } };\n}\n\nexport async function logout() {\n  userIsLoggedIn = false;\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  console.log(\"Registration attempt:\", data);\n  // In a real app, you'd create a new user in the database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: data.email } };\n}\n\nexport async function getAuthState() {\n    return { isLoggedIn: userIsLoggedIn };\n}\n\nexport async function saveProject(project: Project) {\n  if (!userIsLoggedIn) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n  // In a real app, you'd save this to a database like Firestore.\n  console.log(\"Saving project:\", project.name);\n  const existingIndex = userProjects.findIndex(p => p.id === project.id);\n  if (existingIndex > -1) {\n    userProjects[existingIndex] = project;\n  } else {\n    userProjects.push(project);\n  }\n  return { success: true, message: \"Project saved successfully!\" };\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const result = await writingImprovementSuggestions(input);\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(error);\n    return { success: false, error: \"Failed to get analysis. Please try again.\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const character = await genChar();\n        return { success: true, character };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate character. Please try again.\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const outline = await genNovelOutline(input);\n        return { success: true, outline };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate novel outline. Please try again.\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IAmBsB,SAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\n\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\nlet userIsLoggedIn = false;\nlet userProjects: Project[] = [];\n\nexport async function login(data: any) {\n  console.log(\"Login attempt:\", data);\n  // In a real app, you'd verify credentials against a database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: '<EMAIL>' } };\n}\n\nexport async function logout() {\n  userIsLoggedIn = false;\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  console.log(\"Registration attempt:\", data);\n  // In a real app, you'd create a new user in the database.\n  userIsLoggedIn = true;\n  return { success: true, user: { name: 'Demo User', email: data.email } };\n}\n\nexport async function getAuthState() {\n    return { isLoggedIn: userIsLoggedIn };\n}\n\nexport async function saveProject(project: Project) {\n  if (!userIsLoggedIn) {\n    return { success: false, error: \"User not authenticated.\" };\n  }\n  // In a real app, you'd save this to a database like Firestore.\n  console.log(\"Saving project:\", project.name);\n  const existingIndex = userProjects.findIndex(p => p.id === project.id);\n  if (existingIndex > -1) {\n    userProjects[existingIndex] = project;\n  } else {\n    userProjects.push(project);\n  }\n  return { success: true, message: \"Project saved successfully!\" };\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const result = await writingImprovementSuggestions(input);\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(error);\n    return { success: false, error: \"Failed to get analysis. Please try again.\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const character = await genChar();\n        return { success: true, character };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate character. Please try again.\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const outline = await genNovelOutline(input);\n        return { success: true, outline };\n    } catch (error) {\n        console.error(error);\n        return { success: false, error: \"Failed to generate novel outline. Please try again.\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IAwBsB,WAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/hooks/use-auth.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport * as authActions from '@/app/actions';\nimport { useRouter } from 'next/navigation';\n\ninterface AuthState {\n  isLoggedIn: boolean;\n  user: { name: string; email: string } | null;\n}\n\ninterface AuthContextType {\n  authState: AuthState;\n  loading: boolean;\n  login: (data: any) => Promise<any>;\n  logout: () => Promise<void>;\n  register: (data: any) => Promise<any>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [authState, setAuthState] = useState<AuthState>({ isLoggedIn: false, user: null });\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const { isLoggedIn } = await authActions.getAuthState();\n        setAuthState({ isLoggedIn, user: isLoggedIn ? { name: 'Demo User', email: '<EMAIL>' } : null });\n      } catch (error) {\n        console.error(\"Failed to get auth state\", error);\n        setAuthState({ isLoggedIn: false, user: null });\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, []);\n\n  const login = async (data: any) => {\n    const response = await authActions.login(data);\n    if (response.success) {\n      setAuthState({ isLoggedIn: true, user: response.user });\n    }\n    return response;\n  };\n\n  const logout = async () => {\n    await authActions.logout();\n    setAuthState({ isLoggedIn: false, user: null });\n  };\n\n  const register = async (data: any) => {\n    const response = await authActions.register(data);\n    if (response.success) {\n      setAuthState({ isLoggedIn: true, user: response.user });\n    }\n    return response;\n  };\n\n  return (\n    <AuthContext.Provider value={{ authState, loading, login, logout, register }}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAa;QAAE,YAAY;QAAO,MAAM;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,kJAAA,CAAA,eAAwB,AAAD;gBACpD,aAAa;oBAAE;oBAAY,MAAM,aAAa;wBAAE,MAAM;wBAAa,OAAO;oBAAmB,IAAI;gBAAK;YACxG,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,aAAa;oBAAE,YAAY;oBAAO,MAAM;gBAAK;YAC/C,SAAU;gBACR,WAAW;YACb;QACF;QACA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,MAAM,WAAW,MAAM,CAAA,GAAA,kJAAA,CAAA,QAAiB,AAAD,EAAE;QACzC,IAAI,SAAS,OAAO,EAAE;YACpB,aAAa;gBAAE,YAAY;gBAAM,MAAM,SAAS,IAAI;YAAC;QACvD;QACA,OAAO;IACT;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,kJAAA,CAAA,SAAkB,AAAD;QACvB,aAAa;YAAE,YAAY;YAAO,MAAM;QAAK;IAC/C;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,WAAW,MAAM,CAAA,GAAA,kJAAA,CAAA,WAAoB,AAAD,EAAE;QAC5C,IAAI,SAAS,OAAO,EAAE;YACpB,aAAa;gBAAE,YAAY;gBAAM,MAAM,SAAS,IAAI;YAAC;QACvD;QACA,OAAO;IACT;IAEA,qBACE,qZAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAS;YAAO;YAAQ;QAAS;kBACxE;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}