module.exports = {

"[project]/node_modules/.pnpm/node-fetch@3.3.2/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/6c2a7_node-fetch_src_utils_multipart-parser_f39141ec.js",
  "server/chunks/ssr/node_modules__pnpm_3145e12a._.js",
  "server/chunks/ssr/[root-of-the-server]__bbe795ed._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/node-fetch@3.3.2/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript)");
    });
});
}}),

};