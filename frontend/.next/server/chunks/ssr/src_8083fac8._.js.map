{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,4WAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,qZAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,qZAAC,sXAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,sXAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,mXAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,mXAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,mXAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,mXAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,mXAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,qZAAC;;0BACC,qZAAC;;;;;0BACD,qZAAC,mXAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,qZAAC,mXAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,qZAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,qZAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,mXAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,mXAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,mXAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,oXAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,oXAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,oXAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,qZAAC,oXAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,oXAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden md:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,4WAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,qZAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,qZAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,qZAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,qZAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,qZAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,qZAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,qZAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,qZAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,qZAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,qZAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,qZAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,qZAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,qZAAC,oSAAA,CAAA,YAAS;;;;;0BACV,qZAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC,qIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,qZAAC,mIAAA,CAAA,UAAO;;0BACN,qZAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,qZAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,4WAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,qZAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,qZAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,qZAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,sXAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,sXAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,qZAAC,sXAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,qZAAC,sXAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,qZAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,sXAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,qZAAC,sXAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,qZAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,sXAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,6XAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,6XAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,6XAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,6XAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,6XAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,6XAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,qZAAC,6XAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,qZAAC,0SAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6XAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,6XAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,6XAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,qZAAC,6XAAA,CAAA,SAA4B;kBAC3B,cAAA,qZAAC,6XAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,6XAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,qZAAC,6XAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6XAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,qZAAC,6XAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,qZAAC;gBAAK,WAAU;0BACd,cAAA,qZAAC,6XAAA,CAAA,gBAAmC;8BAClC,cAAA,qZAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,6XAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,qZAAC,6XAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,qZAAC;gBAAK,WAAU;0BACd,cAAA,qZAAC,6XAAA,CAAA,gBAAmC;8BAClC,cAAA,qZAAC,0RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,6XAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,qZAAC,6XAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,6XAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,6XAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,6XAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/logo.tsx"], "sourcesContent": ["import { Feather } from 'lucide-react';\n\nexport default function Logo() {\n  return (\n    <div className=\"flex items-center gap-2 font-headline text-lg font-bold text-foreground\">\n      <div className=\"bg-primary text-primary-foreground p-2 rounded-md\">\n        <Feather className=\"h-5 w-5\" />\n      </div>\n      <span>WriteFlow</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,qZAAC;QAAI,WAAU;;0BACb,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC,4RAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAErB,qZAAC;0BAAK;;;;;;;;;;;;AAGZ", "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/app-sidebar.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React from 'react';\nimport {\n  Sidebar,\n  Sidebar<PERSON>ontent,\n  SidebarHeader,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupLabel,\n  SidebarMenu,\n  SidebarMenuItem,\n  SidebarMenuButton,\n  SidebarSeparator,\n} from '@/components/ui/sidebar';\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';\nimport { Button } from '@/components/ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { ChevronsUpDown, Users, Globe, FileText, Download, Settings, PlusCircle } from 'lucide-react';\nimport Logo from './logo';\nimport type { ActiveView } from '@/app/page';\nimport type { Project } from '@/lib/types';\n\n\ntype AppSidebarProps = {\n  projects: Project[];\n  activeProjectId: string;\n  onSwitchProject: (projectId: string) => void;\n  project: Project;\n  activeView: ActiveView;\n  setActiveView: (view: ActiveView) => void;\n  activeChapterId: string | null;\n  setActiveChapterId: (id: string) => void;\n  activeSceneId: string | null;\n  setActiveSceneId: (id: string) => void;\n  onAddScene: (chapterId: string) => void;\n};\n\nexport default function AppSidebar({ \n  projects,\n  activeProjectId,\n  onSwitchProject,\n  project, \n  activeView, \n  setActiveView,\n  activeChapterId,\n  setActiveChapterId,\n  activeSceneId,\n  setActiveSceneId,\n  onAddScene,\n}: AppSidebarProps) {\n\n  const handleChapterClick = (chapterId: string) => {\n    setActiveChapterId(chapterId);\n    // If there's a first scene, go to it. Otherwise, maybe just highlight chapter.\n    const firstSceneId = project.chapters.find(c => c.id === chapterId)?.scenes[0]?.id;\n    if (firstSceneId) {\n      setActiveSceneId(firstSceneId);\n    } else {\n      setActiveSceneId(''); // No scene selected\n    }\n    setActiveView('editor');\n  }\n\n  const handleSceneClick = (chapterId: string, sceneId: string) => {\n    setActiveChapterId(chapterId);\n    setActiveSceneId(sceneId);\n    setActiveView('editor');\n  }\n\n  return (\n    <Sidebar>\n      <SidebarHeader className=\"border-b\">\n        <Logo />\n      </SidebarHeader>\n      <SidebarContent className=\"p-0\">\n        <div className=\"flex flex-col h-full\">\n          <div className=\"p-4\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"outline\" className=\"w-full justify-between\">\n                  <span>{project.name}</span>\n                  <ChevronsUpDown className=\"h-4 w-4 text-muted-foreground\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"start\" className=\"w-[calc(var(--sidebar-width)-2rem)]\">\n                {projects.map((p) => (\n                    <DropdownMenuItem key={p.id} onClick={() => onSwitchProject(p.id)} disabled={p.id === activeProjectId}>\n                      {p.name}\n                    </DropdownMenuItem>\n                ))}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n          <SidebarSeparator />\n          <SidebarGroup className=\"flex-1 overflow-auto\">\n            <SidebarGroupLabel className=\"flex justify-between items-center\">\n              <span>Outline</span>\n               <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\" onClick={() => setActiveView('editor')}>\n                <PlusCircle className=\"h-4 w-4\" />\n              </Button>\n            </SidebarGroupLabel>\n            <Accordion type=\"multiple\" defaultValue={project.chapters.map(c => c.id)} className=\"w-full\">\n              {project.chapters.map((chapter) => (\n                <AccordionItem value={chapter.id} key={chapter.id} className=\"border-b-0\">\n                  <AccordionTrigger\n                    className={`py-2 px-2 text-sm rounded-md hover:no-underline hover:bg-muted ${activeChapterId === chapter.id && chapter.scenes.every(s => s.id !== activeSceneId) ? 'bg-muted font-bold' : ''}`}\n                    onClick={() => handleChapterClick(chapter.id)}\n                  >\n                    {chapter.title}\n                  </AccordionTrigger>\n                  <AccordionContent className=\"pl-4\">\n                    <div className=\"flex flex-col gap-1 mt-1\">\n                      {chapter.scenes.map((scene) => (\n                        <Button\n                          key={scene.id}\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className={`w-full justify-start h-auto py-1 px-2 text-left ${activeSceneId === scene.id && activeView === 'editor' ? 'bg-accent text-accent-foreground' : ''}`}\n                          onClick={() => handleSceneClick(chapter.id, scene.id)}\n                        >\n                          {scene.title}\n                        </Button>\n                      ))}\n                       <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start text-muted-foreground\" onClick={() => onAddScene(chapter.id)}>\n                         <PlusCircle className=\"h-4 w-4 mr-2\" /> Add scene\n                      </Button>\n                    </div>\n                  </AccordionContent>\n                </AccordionItem>\n              ))}\n            </Accordion>\n          </SidebarGroup>\n          <SidebarSeparator />\n          <SidebarGroup>\n            <SidebarMenu>\n              <SidebarMenuItem>\n                <SidebarMenuButton tooltip=\"Characters\" isActive={activeView === 'characters'} onClick={() => setActiveView('characters')}>\n                  <Users />\n                  Characters\n                </SidebarMenuButton>\n              </SidebarMenuItem>\n              <SidebarMenuItem>\n                <SidebarMenuButton tooltip=\"World Building\" isActive={activeView === 'world'} onClick={() => setActiveView('world')}>\n                  <Globe />\n                  World Building\n                </SidebarMenuButton>\n              </SidebarMenuItem>\n              <SidebarMenuItem>\n                <SidebarMenuButton tooltip=\"Notes & Research\" isActive={activeView === 'notes'} onClick={() => setActiveView('notes')}>\n                  <FileText />\n                  Notes & Research\n                </SidebarMenuButton>\n              </SidebarMenuItem>\n            </SidebarMenu>\n          </SidebarGroup>\n        </div>\n      </SidebarContent>\n      <SidebarFooter className=\"border-t\">\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton tooltip=\"Export Project\" isActive={activeView === 'export'} onClick={() => setActiveView('export')}>\n              <Download />\n              Export / Backup\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n          <SidebarMenuItem>\n            <SidebarMenuButton tooltip=\"Settings\" isActive={activeView === 'settings'} onClick={() => setActiveView('settings')}>\n              <Settings />\n              Settings\n            </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarFooter>\n    </Sidebar>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AAYA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAnBA;;;;;;;;AAsCe,SAAS,WAAW,EACjC,QAAQ,EACR,eAAe,EACf,eAAe,EACf,OAAO,EACP,UAAU,EACV,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,UAAU,EACM;IAEhB,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,+EAA+E;QAC/E,MAAM,eAAe,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,MAAM,CAAC,EAAE,EAAE;QAChF,IAAI,cAAc;YAChB,iBAAiB;QACnB,OAAO;YACL,iBAAiB,KAAK,oBAAoB;QAC5C;QACA,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,mBAAmB;QACnB,iBAAiB;QACjB,cAAc;IAChB;IAEA,qBACE,qZAAC,mIAAA,CAAA,UAAO;;0BACN,qZAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,qZAAC,0HAAA,CAAA,UAAI;;;;;;;;;;0BAEP,qZAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC,4IAAA,CAAA,eAAY;;kDACX,qZAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,qZAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,qZAAC;8DAAM,QAAQ,IAAI;;;;;;8DACnB,qZAAC,kTAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG9B,qZAAC,4IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAQ,WAAU;kDAC1C,SAAS,GAAG,CAAC,CAAC,kBACX,qZAAC,4IAAA,CAAA,mBAAgB;gDAAY,SAAS,IAAM,gBAAgB,EAAE,EAAE;gDAAG,UAAU,EAAE,EAAE,KAAK;0DACnF,EAAE,IAAI;+CADc,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;sCAOrC,qZAAC,mIAAA,CAAA,mBAAgB;;;;;sCACjB,qZAAC,mIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,qZAAC,mIAAA,CAAA,oBAAiB;oCAAC,WAAU;;sDAC3B,qZAAC;sDAAK;;;;;;sDACL,qZAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAU,SAAS,IAAM,cAAc;sDACpF,cAAA,qZAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG1B,qZAAC,qIAAA,CAAA,YAAS;oCAAC,MAAK;oCAAW,cAAc,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oCAAG,WAAU;8CACjF,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,qZAAC,qIAAA,CAAA,gBAAa;4CAAC,OAAO,QAAQ,EAAE;4CAAmB,WAAU;;8DAC3D,qZAAC,qIAAA,CAAA,mBAAgB;oDACf,WAAW,CAAC,+DAA+D,EAAE,oBAAoB,QAAQ,EAAE,IAAI,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB,uBAAuB,IAAI;oDAC9L,SAAS,IAAM,mBAAmB,QAAQ,EAAE;8DAE3C,QAAQ,KAAK;;;;;;8DAEhB,qZAAC,qIAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAC1B,cAAA,qZAAC;wDAAI,WAAU;;4DACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,sBACnB,qZAAC,kIAAA,CAAA,SAAM;oEAEL,SAAQ;oEACR,MAAK;oEACL,WAAW,CAAC,gDAAgD,EAAE,kBAAkB,MAAM,EAAE,IAAI,eAAe,WAAW,qCAAqC,IAAI;oEAC/J,SAAS,IAAM,iBAAiB,QAAQ,EAAE,EAAE,MAAM,EAAE;8EAEnD,MAAM,KAAK;mEANP,MAAM,EAAE;;;;;0EAShB,qZAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,WAAU;gEAA6C,SAAS,IAAM,WAAW,QAAQ,EAAE;;kFAC3H,qZAAC,sSAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;2CArBT,QAAQ,EAAE;;;;;;;;;;;;;;;;sCA6BvD,qZAAC,mIAAA,CAAA,mBAAgB;;;;;sCACjB,qZAAC,mIAAA,CAAA,eAAY;sCACX,cAAA,qZAAC,mIAAA,CAAA,cAAW;;kDACV,qZAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,qZAAC,mIAAA,CAAA,oBAAiB;4CAAC,SAAQ;4CAAa,UAAU,eAAe;4CAAc,SAAS,IAAM,cAAc;;8DAC1G,qZAAC,wRAAA,CAAA,QAAK;;;;;gDAAG;;;;;;;;;;;;kDAIb,qZAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,qZAAC,mIAAA,CAAA,oBAAiB;4CAAC,SAAQ;4CAAiB,UAAU,eAAe;4CAAS,SAAS,IAAM,cAAc;;8DACzG,qZAAC,wRAAA,CAAA,QAAK;;;;;gDAAG;;;;;;;;;;;;kDAIb,qZAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,qZAAC,mIAAA,CAAA,oBAAiB;4CAAC,SAAQ;4CAAmB,UAAU,eAAe;4CAAS,SAAS,IAAM,cAAc;;8DAC3G,qZAAC,kSAAA,CAAA,WAAQ;;;;;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,qZAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,qZAAC,mIAAA,CAAA,cAAW;;sCACV,qZAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,qZAAC,mIAAA,CAAA,oBAAiB;gCAAC,SAAQ;gCAAiB,UAAU,eAAe;gCAAU,SAAS,IAAM,cAAc;;kDAC1G,qZAAC,8RAAA,CAAA,WAAQ;;;;;oCAAG;;;;;;;;;;;;sCAIhB,qZAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,qZAAC,mIAAA,CAAA,oBAAiB;gCAAC,SAAQ;gCAAW,UAAU,eAAe;gCAAY,SAAS,IAAM,cAAc;;kDACtG,qZAAC,8RAAA,CAAA,WAAQ;;;;;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B", "debugId": null}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mXAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mXAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mXAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/app-header.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport React from 'react';\nimport { SidebarTrigger, useSidebar } from \"@/components/ui/sidebar\"\nimport { Input } from \"@/components/ui/input\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Search, SlidersHorizontal, Eye, EyeOff, Save, Loader, LogOut } from 'lucide-react';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { useAuth } from '@/hooks/use-auth';\nimport { useRouter } from 'next/navigation';\n\ntype AppHeaderProps = {\n  onFocusToggle: () => void;\n  isFocusMode: boolean;\n  onSave: () => void;\n  isSaving: boolean;\n}\n\nexport default function AppHeader({ onFocusToggle, isFocusMode, onSave, isSaving }: AppHeaderProps) {\n  const { isMobile } = useSidebar();\n  const { authState, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/login');\n  };\n\n  return (\n    <header className=\"flex h-14 items-center gap-4 border-b bg-background/95 backdrop-blur-sm px-4 lg:h-[60px] lg:px-6 sticky top-0 z-30 support-sticky:bg-background/60\">\n      <SidebarTrigger className=\"md:flex\" />\n\n      <div className=\"w-full flex-1\">\n        <form>\n          <div className=\"relative\">\n            <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input\n              type=\"search\"\n              placeholder=\"Search project...\"\n              className=\"w-full appearance-none bg-background pl-8 shadow-none md:w-2/3 lg:w-1/3\"\n            />\n          </div>\n        </form>\n      </div>\n\n      <Button variant=\"outline\" size=\"sm\" onClick={onSave} disabled={isSaving}>\n        {isSaving ? <Loader className=\"mr-2 h-4 w-4 animate-spin\" /> : <Save className=\"mr-2 h-4 w-4\" />}\n        Save\n      </Button>\n\n      <Button variant=\"ghost\" size=\"icon\" onClick={onFocusToggle} aria-label=\"Toggle Focus Mode\">\n        {isFocusMode ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n      </Button>\n      \n      <DropdownMenu>\n        <DropdownMenuTrigger asChild>\n          <Button variant=\"secondary\" size=\"icon\" className=\"rounded-full\">\n            <Avatar>\n                <AvatarImage src=\"https://placehold.co/40x40\" alt=\"User\" data-ai-hint=\"avatar profile\" />\n                <AvatarFallback>WF</AvatarFallback>\n            </Avatar>\n            <span className=\"sr-only\">Toggle user menu</span>\n          </Button>\n        </DropdownMenuTrigger>\n        <DropdownMenuContent align=\"end\">\n           {authState.isLoggedIn ? (\n            <>\n              <DropdownMenuLabel>My Account</DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem onClick={() => router.push('/settings')}>Settings</DropdownMenuItem>\n              <DropdownMenuItem>Support</DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem onClick={handleLogout}>\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                Logout\n              </DropdownMenuItem>\n            </>\n          ) : (\n            <>\n              <DropdownMenuItem onClick={() => router.push('/login')}>Login</DropdownMenuItem>\n              <DropdownMenuItem onClick={() => router.push('/register')}>Register</DropdownMenuItem>\n            </>\n          )}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAmBe,SAAS,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAkB;IAChG,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,qZAAC;QAAO,WAAU;;0BAChB,qZAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAE1B,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC;8BACC,cAAA,qZAAC;wBAAI,WAAU;;0CACb,qZAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,qZAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMlB,qZAAC,kIAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;gBAAQ,UAAU;;oBAC5D,yBAAW,qZAAC,0RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;6CAAiC,qZAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAkB;;;;;;;0BAInG,qZAAC,kIAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAQ,MAAK;gBAAO,SAAS;gBAAe,cAAW;0BACpE,4BAAc,qZAAC,8RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;yCAAe,qZAAC,oRAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;0BAGjE,qZAAC,4IAAA,CAAA,eAAY;;kCACX,qZAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,qZAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAY,MAAK;4BAAO,WAAU;;8CAChD,qZAAC,kIAAA,CAAA,SAAM;;sDACH,qZAAC,kIAAA,CAAA,cAAW;4CAAC,KAAI;4CAA6B,KAAI;4CAAO,gBAAa;;;;;;sDACtE,qZAAC,kIAAA,CAAA,iBAAc;sDAAC;;;;;;;;;;;;8CAEpB,qZAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAG9B,qZAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;kCACvB,UAAU,UAAU,iBACpB;;8CACE,qZAAC,4IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,qZAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,qZAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAc;;;;;;8CAC3D,qZAAC,4IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,qZAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,qZAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,qZAAC,8RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;yDAKvC;;8CACE,qZAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAW;;;;;;8CACxD,qZAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,qZAAC,mXAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,mXAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,qXAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,qXAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,qZAAC,qXAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,qZAAC,qXAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,qXAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/writing-stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useMemo, useState, useEffect } from 'react';\nimport { Progress } from \"@/components/ui/progress\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\n\ninterface WritingStatsProps {\n  text: string;\n  lastSaved: Date;\n}\n\nconst WORDS_PER_MINUTE = 225;\n\nexport default function WritingStats({ text, lastSaved }: WritingStatsProps) {\n  const [dailyGoal, setDailyGoal] = useState(500);\n  const [hydrated, setHydrated] = useState(false);\n\n  useEffect(() => {\n    const savedGoal = localStorage.getItem('writing-goal');\n    if (savedGoal) {\n      setDailyGoal(parseInt(savedGoal, 10));\n    }\n    setHydrated(true);\n  }, []);\n\n  const stats = useMemo(() => {\n    const words = text.trim().split(/\\s+/).filter(Boolean);\n    const wordCount = words.length;\n    const charCount = text.length;\n    const paragraphCount = text.split(/\\n+/).filter(p => p.trim().length > 0).length;\n    const readingTime = Math.ceil(wordCount / WORDS_PER_MINUTE);\n\n    return { wordCount, charCount, paragraphCount, readingTime };\n  }, [text]);\n\n  const handleGoalChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newGoal = parseInt(e.target.value, 10) || 0;\n    setDailyGoal(newGoal);\n    localStorage.setItem('writing-goal', newGoal.toString());\n  };\n\n  const progress = dailyGoal > 0 ? (stats.wordCount / dailyGoal) * 100 : 0;\n\n  if (!hydrated) {\n    return <div className=\"h-16\"></div>; \n  }\n\n  return (\n    <div className=\"flex flex-col md:flex-row items-center gap-4 md:gap-8 text-sm text-muted-foreground w-full\">\n      <div className=\"flex items-center gap-4\">\n        <span>Words: <span className=\"font-medium text-foreground\">{stats.wordCount}</span></span>\n        <span>Chars: <span className=\"font-medium text-foreground\">{stats.charCount}</span></span>\n        <span>Read Time: <span className=\"font-medium text-foreground\">~{stats.readingTime} min</span></span>\n      </div>\n      <div className=\"flex-1 w-full md:w-auto\">\n        <div className=\"flex items-center gap-2 w-full max-w-sm mx-auto md:mx-0\">\n          <Label htmlFor=\"daily-goal\" className=\"whitespace-nowrap\">Daily Goal</Label>\n          <Progress value={progress} className=\"h-2 flex-1\" />\n           <Input\n              id=\"daily-goal\"\n              type=\"number\"\n              value={dailyGoal}\n              onChange={handleGoalChange}\n              className=\"w-20 h-8 p-1 text-center bg-muted\"\n            />\n        </div>\n      </div>\n      <div className=\"text-xs\">\n        Last saved: {lastSaved.toLocaleTimeString()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,mBAAmB;AAEV,SAAS,aAAa,EAAE,IAAI,EAAE,SAAS,EAAqB;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,aAAa,SAAS,WAAW;QACnC;QACA,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,4WAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC;QAC9C,MAAM,YAAY,MAAM,MAAM;QAC9B,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,iBAAiB,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM;QAChF,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;QAE1C,OAAO;YAAE;YAAW;YAAW;YAAgB;QAAY;IAC7D,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO;QAChD,aAAa;QACb,aAAa,OAAO,CAAC,gBAAgB,QAAQ,QAAQ;IACvD;IAEA,MAAM,WAAW,YAAY,IAAI,AAAC,MAAM,SAAS,GAAG,YAAa,MAAM;IAEvE,IAAI,CAAC,UAAU;QACb,qBAAO,qZAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,qZAAC;QAAI,WAAU;;0BACb,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;;4BAAK;0CAAO,qZAAC;gCAAK,WAAU;0CAA+B,MAAM,SAAS;;;;;;;;;;;;kCAC3E,qZAAC;;4BAAK;0CAAO,qZAAC;gCAAK,WAAU;0CAA+B,MAAM,SAAS;;;;;;;;;;;;kCAC3E,qZAAC;;4BAAK;0CAAW,qZAAC;gCAAK,WAAU;;oCAA8B;oCAAE,MAAM,WAAW;oCAAC;;;;;;;;;;;;;;;;;;;0BAErF,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAa,WAAU;sCAAoB;;;;;;sCAC1D,qZAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAU,WAAU;;;;;;sCACpC,qZAAC,iIAAA,CAAA,QAAK;4BACH,IAAG;4BACH,MAAK;4BACL,OAAO;4BACP,UAAU;4BACV,WAAU;;;;;;;;;;;;;;;;;0BAIlB,qZAAC;gBAAI,WAAU;;oBAAU;oBACV,UAAU,kBAAkB;;;;;;;;;;;;;AAIjD", "debugId": null}}, {"offset": {"line": 2389, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,qZAAC,8XAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,qZAAC,8XAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,qZAAC;;;;;0BACD,qZAAC,8XAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,8XAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,qZAAC,8XAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,qZAAC,8XAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,8XAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2457, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ai-analysis-view.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle, SheetDescription } from \"@/components/ui/sheet\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface AiAnalysisViewProps {\n  isOpen: boolean;\n  onOpenChange: (isOpen: boolean) => void;\n  analysis: string | null;\n  isPending: boolean;\n}\n\nexport default function AiAnalysisView({ isOpen, onOpenChange, analysis, isPending }: AiAnalysisViewProps) {\n  \n  const formattedAnalysis = analysis?.split('\\n').map((line, index) => {\n    if (line.startsWith('**') && line.endsWith('**')) {\n      return <h3 key={index} className=\"font-bold text-lg mt-4 mb-2\">{line.replace(/\\*\\*/g, '')}</h3>;\n    }\n    if (line.startsWith('* ')) {\n      return <li key={index} className=\"ml-4 list-disc\">{line.substring(2)}</li>;\n    }\n    return <p key={index} className=\"mb-2\">{line}</p>;\n  });\n\n  return (\n    <Sheet open={isOpen} onOpenChange={onOpenChange}>\n      <SheetContent className=\"w-full sm:max-w-lg\">\n        <SheetHeader>\n          <SheetTitle>AI Writing Analysis</SheetTitle>\n          <SheetDescription>\n            Suggestions to improve your vocabulary, sentence structure, and pacing.\n          </SheetDescription>\n        </SheetHeader>\n        <ScrollArea className=\"h-[calc(100%-4rem)] mt-4 pr-4\">\n          {isPending && (\n            <div className=\"flex items-center justify-center h-full\">\n              <p className=\"text-muted-foreground\">Analyzing your text...</p>\n            </div>\n          )}\n          {analysis && !isPending && (\n            <div className=\"prose prose-sm dark:prose-invert max-w-none text-foreground\">\n                {formattedAnalysis}\n            </div>\n          )}\n        </ScrollArea>\n      </SheetContent>\n    </Sheet>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAuB;IAEvG,MAAM,oBAAoB,UAAU,MAAM,MAAM,IAAI,CAAC,MAAM;QACzD,IAAI,KAAK,UAAU,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO;YAChD,qBAAO,qZAAC;gBAAe,WAAU;0BAA+B,KAAK,OAAO,CAAC,SAAS;eAAtE;;;;;QAClB;QACA,IAAI,KAAK,UAAU,CAAC,OAAO;YACzB,qBAAO,qZAAC;gBAAe,WAAU;0BAAkB,KAAK,SAAS,CAAC;eAAlD;;;;;QAClB;QACA,qBAAO,qZAAC;YAAc,WAAU;sBAAQ;WAAzB;;;;;IACjB;IAEA,qBACE,qZAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAQ,cAAc;kBACjC,cAAA,qZAAC,iIAAA,CAAA,eAAY;YAAC,WAAU;;8BACtB,qZAAC,iIAAA,CAAA,cAAW;;sCACV,qZAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,qZAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAIpB,qZAAC,0IAAA,CAAA,aAAU;oBAAC,WAAU;;wBACnB,2BACC,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;wBAGxC,YAAY,CAAC,2BACZ,qZAAC;4BAAI,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 2576, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\n// let userIsLoggedIn = false;\n// let userProjects: [];\n\nconst API_BASE_URL = \"http://localhost:8000\"; // 后端API地址\n\nexport async function login(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/token`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n      body: new URLSearchParams({\n        username: data.email,\n        password: data.password,\n      }).toString(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Login failed\" };\n    }\n\n    const result = await response.json();\n    // You might want to store the token in a cookie or local storage\n    // For now, we'll just return success.\n    return { success: true, user: { name: data.email, email: data.email } }; // Assuming backend returns user info\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function logout() {\n  // For logout, you might clear the token from storage\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/register`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ email: data.email, password: data.password, name: data.name }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Registration failed\" };\n    }\n\n    const result = await response.json();\n    return { success: true, user: result };\n  } catch (error) {\n    console.error(\"Registration error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function getAuthState() {\n    // This function needs to be implemented on the backend if you want to check auth state from there.\n    // For now, we'll assume if a token exists (e.g., in local storage), the user is logged in.\n    // This is a simplified example and might need more robust implementation.\n    return { isLoggedIn: false }; // Placeholder\n}\n\nexport async function saveProject(project: Project) {\n  try {\n    const token = \"YOUR_AUTH_TOKEN\"; // Replace with actual token retrieval\n    const response = await fetch(`${API_BASE_URL}/saveProject`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Authorization\": `Bearer ${token}`,\n      },\n      body: JSON.stringify(project),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to save project\" };\n    }\n\n    const result = await response.json();\n    return { success: true, message: \"Project saved successfully!\" };\n  } catch (error) {\n    console.error(\"Save project error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const response = await fetch(`${API_BASE_URL}/getWritingAnalysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(input),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to get analysis\" };\n    }\n\n    const result = await response.json();\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(\"Writing analysis error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateCharacter`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate character\" };\n        }\n\n        const result = await response.json();\n        return { success: true, character: result };\n    } catch (error) {\n        console.error(\"Character generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateNovelOutline`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(input),\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate novel outline\" };\n        }\n\n        const result = await response.json();\n        return { success: true, outline: result };\n    } catch (error) {\n        console.error(\"Novel outline generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IAsGsB,qBAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2589, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/main-editor.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport React, { useState, useTransition, useEffect } from 'react';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Bold, Italic, Underline, Strikethrough, Quote, BrainCircuit, Type, Loader } from 'lucide-react';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Separator } from '@/components/ui/separator';\nimport WritingStats from './writing-stats';\nimport AiAnalysisView from './ai-analysis-view';\nimport { getWritingAnalysis } from '@/app/actions';\nimport { useToast } from '@/hooks/use-toast';\nimport { ScrollArea } from './ui/scroll-area';\nimport type { Chapter, Scene } from '@/lib/types';\n\ninterface MainEditorProps {\n  isFocusMode: boolean;\n  chapter: Chapter | undefined;\n  scene: Scene | undefined;\n  onContentChange: (content: string) => void;\n  onChapterTitleChange: (title: string) => void;\n  onSceneTitleChange: (title: string) => void;\n}\n\nexport default function MainEditor({ \n    isFocusMode, \n    chapter, \n    scene, \n    onContentChange,\n    onChapterTitleChange,\n    onSceneTitleChange,\n}: MainEditorProps) {\n  const [text, setText] = useState(scene?.content || '');\n  const [lastSaved, setLastSaved] = useState(new Date());\n  const [isAnalysisOpen, setAnalysisOpen] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState<string | null>(null);\n  const [isPending, startTransition] = useTransition();\n  const { toast } = useToast();\n\n  useEffect(() => {\n    setText(scene?.content || '');\n  }, [scene]);\n\n  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const newText = e.target.value;\n    setText(newText);\n    onContentChange(newText);\n    setLastSaved(new Date());\n  };\n\n  const handleAnalyze = () => {\n    setAnalysisOpen(true);\n    setAnalysisResult(null); \n    startTransition(async () => {\n      const result = await getWritingAnalysis({ text });\n      if (result.success && result.suggestions) {\n        setAnalysisResult(result.suggestions);\n      } else {\n        toast({\n          variant: 'destructive',\n          title: 'Analysis Failed',\n          description: result.error,\n        });\n        setAnalysisOpen(false);\n      }\n    });\n  };\n\n  const toolbarButtons = [\n    { icon: Bold, tooltip: 'Bold (Ctrl+B)' },\n    { icon: Italic, tooltip: 'Italic (Ctrl+I)' },\n    { icon: Underline, tooltip: 'Underline (Ctrl+U)' },\n    { icon: Strikethrough, tooltip: 'Strikethrough' },\n    { icon: Quote, tooltip: 'Quote' },\n  ];\n\n  if (!chapter || !scene) {\n    return (\n        <div className=\"flex items-center justify-center h-full text-muted-foreground\">\n            <p>Select a scene from the sidebar to start writing, or create a new one.</p>\n        </div>\n    )\n  }\n\n  return (\n    <TooltipProvider>\n      <div className=\"flex flex-col h-full\">\n        <div className=\"flex-none p-2 md:p-4 border-b flex flex-wrap items-center gap-2\">\n            <div className=\"flex items-center gap-1\">\n              {toolbarButtons.map((btn, i) => (\n                <Tooltip key={i}>\n                  <TooltipTrigger asChild>\n                    <Button variant=\"ghost\" size=\"icon\" disabled>\n                      <btn.icon className=\"h-4 w-4\" />\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent><p>{btn.tooltip}</p></TooltipContent>\n                </Tooltip>\n              ))}\n            </div>\n            <Separator orientation=\"vertical\" className=\"h-6 mx-2\" />\n            <div className=\"flex items-center gap-4\">\n               <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button variant=\"outline\" onClick={handleAnalyze} disabled={isPending || !text}>\n                      {isPending ? <Loader className=\"h-4 w-4 mr-2 animate-spin\" /> : <BrainCircuit className=\"h-4 w-4 mr-2\" />}\n                      Analyze Writing\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent><p>Use AI to get writing suggestions</p></TooltipContent>\n                </Tooltip>\n                <div className=\"flex items-center space-x-2\">\n                    <Switch id=\"typewriter-mode\" />\n                    <Label htmlFor=\"typewriter-mode\" className=\"flex items-center gap-2 text-sm\">\n                        <Type className=\"h-4 w-4\"/> Typewriter Mode\n                    </Label>\n                </div>\n            </div>\n        </div>\n\n        <ScrollArea className=\"flex-1\">\n           <div className=\"max-w-4xl mx-auto px-4 md:px-8 py-8\">\n                <Input\n                    value={chapter.title}\n                    onChange={(e) => onChapterTitleChange(e.target.value)}\n                    className=\"font-headline text-4xl font-bold mb-4 h-auto p-0 border-0 shadow-none focus-visible:ring-0 bg-transparent\"\n                    aria-label=\"Chapter Title\"\n                />\n                <Input\n                    value={scene.title}\n                    onChange={(e) => onSceneTitleChange(e.target.value)}\n                    className=\"font-headline text-2xl font-semibold mb-8 text-muted-foreground h-auto p-0 border-0 shadow-none focus-visible:ring-0 bg-transparent\"\n                    aria-label=\"Scene Title\"\n                />\n                <Textarea\n                    value={text}\n                    onChange={handleTextChange}\n                    className=\"w-full h-full min-h-[calc(100vh-25rem)] text-lg leading-relaxed bg-transparent border-0 focus-visible:ring-0 p-0 shadow-none resize-none font-body\"\n                    placeholder=\"Start writing your story...\"\n                />\n            </div>\n        </ScrollArea>\n        \n        <div className=\"flex-none p-2 md:p-4 border-t bg-background\">\n          <WritingStats text={text} lastSaved={lastSaved} />\n        </div>\n      </div>\n      <AiAnalysisView \n        isOpen={isAnalysisOpen} \n        onOpenChange={setAnalysisOpen} \n        analysis={analysisResult} \n        isPending={isPending}\n      />\n    </TooltipProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AA2Be,SAAS,WAAW,EAC/B,WAAW,EACX,OAAO,EACP,KAAK,EACL,eAAe,EACf,oBAAoB,EACpB,kBAAkB,EACJ;IAChB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,WAAW;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,gBAAgB,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,4WAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,OAAO,WAAW;IAC5B,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,EAAE,MAAM,CAAC,KAAK;QAC9B,QAAQ;QACR,gBAAgB;QAChB,aAAa,IAAI;IACnB;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE;YAAK;YAC/C,IAAI,OAAO,OAAO,IAAI,OAAO,WAAW,EAAE;gBACxC,kBAAkB,OAAO,WAAW;YACtC,OAAO;gBACL,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa,OAAO,KAAK;gBAC3B;gBACA,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,MAAM,sRAAA,CAAA,OAAI;YAAE,SAAS;QAAgB;QACvC;YAAE,MAAM,0RAAA,CAAA,SAAM;YAAE,SAAS;QAAkB;QAC3C;YAAE,MAAM,gSAAA,CAAA,YAAS;YAAE,SAAS;QAAqB;QACjD;YAAE,MAAM,wSAAA,CAAA,gBAAa;YAAE,SAAS;QAAgB;QAChD;YAAE,MAAM,wRAAA,CAAA,QAAK;YAAE,SAAS;QAAQ;KACjC;IAED,IAAI,CAAC,WAAW,CAAC,OAAO;QACtB,qBACI,qZAAC;YAAI,WAAU;sBACX,cAAA,qZAAC;0BAAE;;;;;;;;;;;IAGb;IAEA,qBACE,qZAAC,mIAAA,CAAA,kBAAe;;0BACd,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAI,WAAU;;0CACX,qZAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,KAAK,kBACxB,qZAAC,mIAAA,CAAA,UAAO;;0DACN,qZAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,qZAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,QAAQ;8DAC1C,cAAA,qZAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGxB,qZAAC,mIAAA,CAAA,iBAAc;0DAAC,cAAA,qZAAC;8DAAG,IAAI,OAAO;;;;;;;;;;;;uCANnB;;;;;;;;;;0CAUlB,qZAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAC5C,qZAAC;gCAAI,WAAU;;kDACZ,qZAAC,mIAAA,CAAA,UAAO;;0DACL,qZAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,qZAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;oDAAe,UAAU,aAAa,CAAC;;wDACvE,0BAAY,qZAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAiC,qZAAC,0SAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAkB;;;;;;;;;;;;0DAI9G,qZAAC,mIAAA,CAAA,iBAAc;0DAAC,cAAA,qZAAC;8DAAE;;;;;;;;;;;;;;;;;kDAErB,qZAAC;wCAAI,WAAU;;0DACX,qZAAC,kIAAA,CAAA,SAAM;gDAAC,IAAG;;;;;;0DACX,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAkB,WAAU;;kEACvC,qZAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,qZAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,cAAA,qZAAC;4BAAI,WAAU;;8CACV,qZAAC,iIAAA,CAAA,QAAK;oCACF,OAAO,QAAQ,KAAK;oCACpB,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oCACpD,WAAU;oCACV,cAAW;;;;;;8CAEf,qZAAC,iIAAA,CAAA,QAAK;oCACF,OAAO,MAAM,KAAK;oCAClB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;oCACV,cAAW;;;;;;8CAEf,qZAAC,oIAAA,CAAA,WAAQ;oCACL,OAAO;oCACP,UAAU;oCACV,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;kCAKxB,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC,sIAAA,CAAA,UAAY;4BAAC,MAAM;4BAAM,WAAW;;;;;;;;;;;;;;;;;0BAGzC,qZAAC,4IAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,cAAc;gBACd,UAAU;gBACV,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 2964, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3045, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,mXAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,mXAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,mXAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,mXAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mXAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,qZAAC;;0BACC,qZAAC;;;;;0BACD,qZAAC,mXAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,qZAAC,mXAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,qZAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,qZAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mXAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,qZAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mXAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,4WAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,qZAAC,mXAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mXAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3177, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\n// let userIsLoggedIn = false;\n// let userProjects: [];\n\nconst API_BASE_URL = \"http://localhost:8000\"; // 后端API地址\n\nexport async function login(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/token`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n      body: new URLSearchParams({\n        username: data.email,\n        password: data.password,\n      }).toString(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Login failed\" };\n    }\n\n    const result = await response.json();\n    // You might want to store the token in a cookie or local storage\n    // For now, we'll just return success.\n    return { success: true, user: { name: data.email, email: data.email } }; // Assuming backend returns user info\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function logout() {\n  // For logout, you might clear the token from storage\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/register`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ email: data.email, password: data.password, name: data.name }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Registration failed\" };\n    }\n\n    const result = await response.json();\n    return { success: true, user: result };\n  } catch (error) {\n    console.error(\"Registration error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function getAuthState() {\n    // This function needs to be implemented on the backend if you want to check auth state from there.\n    // For now, we'll assume if a token exists (e.g., in local storage), the user is logged in.\n    // This is a simplified example and might need more robust implementation.\n    return { isLoggedIn: false }; // Placeholder\n}\n\nexport async function saveProject(project: Project) {\n  try {\n    const token = \"YOUR_AUTH_TOKEN\"; // Replace with actual token retrieval\n    const response = await fetch(`${API_BASE_URL}/saveProject`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Authorization\": `Bearer ${token}`,\n      },\n      body: JSON.stringify(project),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to save project\" };\n    }\n\n    const result = await response.json();\n    return { success: true, message: \"Project saved successfully!\" };\n  } catch (error) {\n    console.error(\"Save project error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const response = await fetch(`${API_BASE_URL}/getWritingAnalysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(input),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to get analysis\" };\n    }\n\n    const result = await response.json();\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(\"Writing analysis error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateCharacter`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate character\" };\n        }\n\n        const result = await response.json();\n        return { success: true, character: result };\n    } catch (error) {\n        console.error(\"Character generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateNovelOutline`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(input),\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate novel outline\" };\n        }\n\n        const result = await response.json();\n        return { success: true, outline: result };\n    } catch (error) {\n        console.error(\"Novel outline generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IAkIsB,oBAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3190, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/characters-view.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { useState, useTransition } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Button } from '@/components/ui/button';\nimport { <PERSON><PERSON><PERSON><PERSON>, Loader, Wand2 } from 'lucide-react';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { useForm, Controller } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport type { Character } from '@/lib/types';\nimport { generateCharacter } from '@/app/actions';\nimport { useToast } from '@/hooks/use-toast';\n\ninterface CharactersViewProps {\n  characters: Character[];\n  onAddCharacter: (character: Character) => void;\n}\n\nconst characterSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  age: z.string().optional(),\n  appearance: z.string().optional(),\n  personality: z.string().optional(),\n  background: z.string().optional(),\n});\n\ntype CharacterFormData = z.infer<typeof characterSchema>;\n\n\nexport default function CharactersView({ characters, onAddCharacter }: CharactersViewProps) {\n  const [isDialogOpen, setDialogOpen] = useState(false);\n  const [isPending, startTransition] = useTransition();\n  const [isAiPending, startAiTransition] = useTransition();\n  const { toast } = useToast();\n\n  const { control, handleSubmit, reset, setValue } = useForm<CharacterFormData>({\n    resolver: zodResolver(characterSchema),\n    defaultValues: {\n      name: '',\n      age: '',\n      appearance: '',\n      personality: '',\n      background: '',\n    },\n  });\n\n  const onSubmit = (data: CharacterFormData) => {\n    startTransition(() => {\n      const newCharacter: Character = {\n        id: `char-${Date.now()}`,\n        ...data,\n        age: data.age || 'Unknown',\n      };\n      onAddCharacter(newCharacter);\n      reset();\n      setDialogOpen(false);\n      toast({\n        title: 'Character Added',\n        description: `${newCharacter.name} has joined your story.`,\n      });\n    });\n  };\n\n  const handleAiGenerate = () => {\n    startAiTransition(async () => {\n        const result = await generateCharacter();\n        if (result.success && result.character) {\n            const char = result.character;\n            setValue('name', char.name);\n            setValue('age', char.age);\n            setValue('appearance', char.appearance);\n            setValue('personality', char.personality);\n            setValue('background', char.background);\n            toast({\n                title: 'Character Generated!',\n                description: 'AI has created a new character for you.',\n            });\n        } else {\n            toast({\n                variant: 'destructive',\n                title: 'AI Generation Failed',\n                description: result.error,\n            });\n        }\n    });\n  };\n\n\n  return (\n    <ScrollArea className=\"h-full\">\n      <div className=\"p-4 md:p-8\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"font-headline text-3xl font-bold\">Characters</h1>\n          <Button onClick={() => setDialogOpen(true)}>\n            <PlusCircle className=\"mr-2 h-4 w-4\" />\n            Add Character\n          </Button>\n        </div>\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {characters.map((char) => (\n            <Card key={char.id}>\n              <CardHeader>\n                <CardTitle>{char.name}</CardTitle>\n                <CardDescription>Age: {char.age}</CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-semibold mb-1\">Appearance</h4>\n                  <p className=\"text-sm text-muted-foreground\">{char.appearance}</p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold mb-1\">Personality</h4>\n                  <p className=\"text-sm text-muted-foreground\">{char.personality}</p>\n                </div>\n                 <div>\n                  <h4 className=\"font-semibold mb-1\">Background</h4>\n                  <p className=\"text-sm text-muted-foreground\">{char.background}</p>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n        {characters.length === 0 && (\n            <div className=\"text-center py-16\">\n                <p className=\"text-muted-foreground\">You haven't added any characters yet.</p>\n                <Button variant=\"link\" onClick={() => setDialogOpen(true)}>Create your first character</Button>\n            </div>\n        )}\n      </div>\n\n      <Dialog open={isDialogOpen} onOpenChange={setDialogOpen}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <form onSubmit={handleSubmit(onSubmit)}>\n            <DialogHeader>\n              <DialogTitle>Add New Character</DialogTitle>\n              <DialogDescription>\n                Fill in the details for your new character. You can also use AI to generate one.\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <Button type=\"button\" variant=\"outline\" onClick={handleAiGenerate} disabled={isAiPending}>\n                {isAiPending ? <Loader className=\"mr-2 h-4 w-4 animate-spin\" /> : <Wand2 className=\"mr-2 h-4 w-4\" />}\n                Generate with AI\n              </Button>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"name\" className=\"text-right\">Name</Label>\n                <Controller name=\"name\" control={control} render={({ field }) => <Input id=\"name\" {...field} className=\"col-span-3\" />} />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"age\" className=\"text-right\">Age</Label>\n                <Controller name=\"age\" control={control} render={({ field }) => <Input id=\"age\" {...field} className=\"col-span-3\" />} />\n              </div>\n              <div className=\"grid grid-cols-4 items-start gap-4\">\n                <Label htmlFor=\"appearance\" className=\"text-right pt-2\">Appearance</Label>\n                <Controller name=\"appearance\" control={control} render={({ field }) => <Textarea id=\"appearance\" {...field} className=\"col-span-3\" />} />\n              </div>\n              <div className=\"grid grid-cols-4 items-start gap-4\">\n                <Label htmlFor=\"personality\" className=\"text-right pt-2\">Personality</Label>\n                <Controller name=\"personality\" control={control} render={({ field }) => <Textarea id=\"personality\" {...field} className=\"col-span-3\" />} />\n              </div>\n              <div className=\"grid grid-cols-4 items-start gap-4\">\n                <Label htmlFor=\"background\" className=\"text-right pt-2\">Background</Label>\n                <Controller name=\"background\" control={control} render={({ field }) => <Textarea id=\"background\" {...field} className=\"col-span-3\" />} />\n              </div>\n            </div>\n            <DialogFooter>\n              <DialogClose asChild>\n                <Button type=\"button\" variant=\"secondary\">Cancel</Button>\n              </DialogClose>\n              <Button type=\"submit\" disabled={isPending}>\n                {isPending && <Loader className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Save Character\n              </Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n    </ScrollArea>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAhBA;;;;;;;;;;;;;;;;AAuBA,MAAM,kBAAkB,oNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,KAAK,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,aAAa,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAKe,SAAS,eAAe,EAAE,UAAU,EAAE,cAAc,EAAuB;IACxF,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,aAAa,kBAAkB,GAAG,CAAA,GAAA,4WAAA,CAAA,gBAAa,AAAD;IACrD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAqB;QAC5E,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,KAAK;YACL,YAAY;YACZ,aAAa;YACb,YAAY;QACd;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,gBAAgB;YACd,MAAM,eAA0B;gBAC9B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,GAAG,IAAI;gBACP,KAAK,KAAK,GAAG,IAAI;YACnB;YACA,eAAe;YACf;YACA,cAAc;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa,GAAG,aAAa,IAAI,CAAC,uBAAuB,CAAC;YAC5D;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,SAAS,EAAE;gBACpC,MAAM,OAAO,OAAO,SAAS;gBAC7B,SAAS,QAAQ,KAAK,IAAI;gBAC1B,SAAS,OAAO,KAAK,GAAG;gBACxB,SAAS,cAAc,KAAK,UAAU;gBACtC,SAAS,eAAe,KAAK,WAAW;gBACxC,SAAS,cAAc,KAAK,UAAU;gBACtC,MAAM;oBACF,OAAO;oBACP,aAAa;gBACjB;YACJ,OAAO;gBACH,MAAM;oBACF,SAAS;oBACT,OAAO;oBACP,aAAa,OAAO,KAAK;gBAC7B;YACJ;QACJ;IACF;IAGA,qBACE,qZAAC,0IAAA,CAAA,aAAU;QAAC,WAAU;;0BACpB,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAI,WAAU;;0CACb,qZAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,qZAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,cAAc;;kDACnC,qZAAC,sSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAI3C,qZAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,qZAAC,gIAAA,CAAA,OAAI;;kDACH,qZAAC,gIAAA,CAAA,aAAU;;0DACT,qZAAC,gIAAA,CAAA,YAAS;0DAAE,KAAK,IAAI;;;;;;0DACrB,qZAAC,gIAAA,CAAA,kBAAe;;oDAAC;oDAAM,KAAK,GAAG;;;;;;;;;;;;;kDAEjC,qZAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,qZAAC;;kEACC,qZAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,qZAAC;wDAAE,WAAU;kEAAiC,KAAK,UAAU;;;;;;;;;;;;0DAE/D,qZAAC;;kEACC,qZAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,qZAAC;wDAAE,WAAU;kEAAiC,KAAK,WAAW;;;;;;;;;;;;0DAE/D,qZAAC;;kEACA,qZAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,qZAAC;wDAAE,WAAU;kEAAiC,KAAK,UAAU;;;;;;;;;;;;;;;;;;;+BAhBxD,KAAK,EAAE;;;;;;;;;;oBAsBrB,WAAW,MAAM,KAAK,mBACnB,qZAAC;wBAAI,WAAU;;0CACX,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,qZAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAO,SAAS,IAAM,cAAc;0CAAO;;;;;;;;;;;;;;;;;;0BAKrE,qZAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAc,cAAc;0BACxC,cAAA,qZAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACvB,cAAA,qZAAC;wBAAK,UAAU,aAAa;;0CAC3B,qZAAC,kIAAA,CAAA,eAAY;;kDACX,qZAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,qZAAC,kIAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;0CAIrB,qZAAC;gCAAI,WAAU;;kDACb,qZAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;wCAAkB,UAAU;;4CAC1E,4BAAc,qZAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAAiC,qZAAC,mSAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAkB;;;;;;;kDAGvG,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAa;;;;;;0DAC7C,qZAAC,uPAAA,CAAA,aAAU;gDAAC,MAAK;gDAAO,SAAS;gDAAS,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,qZAAC,iIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAQ,GAAG,KAAK;wDAAE,WAAU;;;;;;;;;;;;;;;;;kDAEzG,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAM,WAAU;0DAAa;;;;;;0DAC5C,qZAAC,uPAAA,CAAA,aAAU;gDAAC,MAAK;gDAAM,SAAS;gDAAS,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,qZAAC,iIAAA,CAAA,QAAK;wDAAC,IAAG;wDAAO,GAAG,KAAK;wDAAE,WAAU;;;;;;;;;;;;;;;;;kDAEvG,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAa,WAAU;0DAAkB;;;;;;0DACxD,qZAAC,uPAAA,CAAA,aAAU;gDAAC,MAAK;gDAAa,SAAS;gDAAS,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,qZAAC,oIAAA,CAAA,WAAQ;wDAAC,IAAG;wDAAc,GAAG,KAAK;wDAAE,WAAU;;;;;;;;;;;;;;;;;kDAExH,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;0DAAkB;;;;;;0DACzD,qZAAC,uPAAA,CAAA,aAAU;gDAAC,MAAK;gDAAc,SAAS;gDAAS,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,qZAAC,oIAAA,CAAA,WAAQ;wDAAC,IAAG;wDAAe,GAAG,KAAK;wDAAE,WAAU;;;;;;;;;;;;;;;;;kDAE1H,qZAAC;wCAAI,WAAU;;0DACb,qZAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAa,WAAU;0DAAkB;;;;;;0DACxD,qZAAC,uPAAA,CAAA,aAAU;gDAAC,MAAK;gDAAa,SAAS;gDAAS,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,qZAAC,oIAAA,CAAA,WAAQ;wDAAC,IAAG;wDAAc,GAAG,KAAK;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAG1H,qZAAC,kIAAA,CAAA,eAAY;;kDACX,qZAAC,kIAAA,CAAA,cAAW;wCAAC,OAAO;kDAClB,cAAA,qZAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;sDAAY;;;;;;;;;;;kDAE5C,qZAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;;4CAC7B,2BAAa,qZAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/E", "debugId": null}}, {"offset": {"line": 3791, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/world-building-view.tsx"], "sourcesContent": ["\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\n\nexport default function WorldBuildingView() {\n  return (\n    <ScrollArea className=\"h-full\">\n      <div className=\"p-4 md:p-8\">\n        <h1 className=\"font-headline text-3xl font-bold mb-6\">World Building</h1>\n        <Card>\n            <CardHeader>\n                <CardTitle>Coming Soon</CardTitle>\n            </CardHeader>\n            <CardContent>\n                <p className=\"text-muted-foreground\">This section is under construction. Check back later for world-building tools!</p>\n            </CardContent>\n        </Card>\n      </div>\n    </ScrollArea>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,qZAAC,0IAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,qZAAC;YAAI,WAAU;;8BACb,qZAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,qZAAC,gIAAA,CAAA,OAAI;;sCACD,qZAAC,gIAAA,CAAA,aAAU;sCACP,cAAA,qZAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEf,qZAAC,gIAAA,CAAA,cAAW;sCACR,cAAA,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 3867, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/notes-view.tsx"], "sourcesContent": ["\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\n\nexport default function NotesView() {\n  return (\n    <ScrollArea className=\"h-full\">\n      <div className=\"p-4 md:p-8\">\n        <h1 className=\"font-headline text-3xl font-bold mb-6\">Notes & Research</h1>\n        <Card>\n            <CardHeader>\n                <CardTitle>Coming Soon</CardTitle>\n            </CardHeader>\n            <CardContent>\n                <p className=\"text-muted-foreground\">This section is under construction. Your notes and research materials will appear here.</p>\n            </CardContent>\n        </Card>\n      </div>\n    </ScrollArea>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,qZAAC,0IAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,qZAAC;YAAI,WAAU;;8BACb,qZAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,qZAAC,gIAAA,CAAA,OAAI;;sCACD,qZAAC,gIAAA,CAAA,aAAU;sCACP,cAAA,qZAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEf,qZAAC,gIAAA,CAAA,cAAW;sCACR,cAAA,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 3943, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/settings-view.tsx"], "sourcesContent": ["\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from \"@/components/ui/card\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\n\nexport default function SettingsView() {\n  return (\n    <ScrollArea className=\"h-full\">\n      <div className=\"p-4 md:p-8\">\n        <h1 className=\"font-headline text-3xl font-bold mb-6\">Settings</h1>\n        <Card>\n            <CardHeader>\n                <CardTitle>Coming Soon</CardTitle>\n            </CardHeader>\n            <CardContent>\n                <p className=\"text-muted-foreground\">This section is under construction. Application settings will appear here.</p>\n            </CardContent>\n        </Card>\n      </div>\n    </ScrollArea>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,qZAAC,0IAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,qZAAC;YAAI,WAAU;;8BACb,qZAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,qZAAC,gIAAA,CAAA,OAAI;;sCACD,qZAAC,gIAAA,CAAA,aAAU;sCACP,cAAA,qZAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEf,qZAAC,gIAAA,CAAA,cAAW;sCACR,cAAA,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 4019, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/components/export-view.tsx"], "sourcesContent": ["\nimport { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\n\nexport default function ExportView() {\n  return (\n    <ScrollArea className=\"h-full\">\n      <div className=\"p-4 md:p-8\">\n        <h1 className=\"font-headline text-3xl font-bold mb-6\">Export / Backup</h1>\n        <Card>\n            <CardHeader>\n                <CardTitle>Coming Soon</CardTitle>\n            </CardHeader>\n            <CardContent>\n                <p className=\"text-muted-foreground\">This section is under construction. Export and backup options will appear here.</p>\n            </CardContent>\n        </Card>\n      </div>\n    </ScrollArea>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,qZAAC,0IAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,qZAAC;YAAI,WAAU;;8BACb,qZAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,qZAAC,gIAAA,CAAA,OAAI;;sCACD,qZAAC,gIAAA,CAAA,aAAU;sCACP,cAAA,qZAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEf,qZAAC,gIAAA,CAAA,cAAW;sCACR,cAAA,qZAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD", "debugId": null}}, {"offset": {"line": 4095, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/lib/data.ts"], "sourcesContent": ["\nimport type { Project } from './types';\n\nexport const sampleProjects: Project[] = [\n  {\n    id: 'proj-1',\n    name: 'The Crimson Cipher',\n    chapters: [\n      {\n        id: 'chap-1',\n        title: 'The Enigmatic Invitation',\n        scenes: [\n          {\n            id: 'scene-1-1',\n            title: 'A Mysterious Letter',\n            content: `The fog hung heavy over the cobblestone streets of Aethelburg, each droplet of moisture seeming to amplify the silence of the pre-dawn hours. In his small, cluttered study, Professor <PERSON> squinted at the letter that had appeared on his desk, seemingly from nowhere. The parchment was old, brittle, and sealed with a crimson wax seal depicting a cipher he had only seen in forbidden texts. His heart, a tired drum, began to beat a little faster. The invitation was to a gathering of minds, a secret society that had been dormant for centuries. The risks were immense, but the allure of uncovering long-lost knowledge was a siren's call he could not ignore.`,\n          },\n          {\n            id: 'scene-1-2',\n            title: 'The Decision',\n            content: `<PERSON> traced the edges of the seal with a trembling finger. To accept meant to step into a world of shadows and secrets, a world that had consumed his mentor years ago. To refuse was to remain in the comfortable, dusty confines of his academic life, forever wondering what might have been. He thought of the whispers, the legends of the Cipher's power, and a decision began to form.`,\n          },\n        ],\n      },\n      {\n        id: 'chap-2',\n        title: 'Journey to the Obsidian Spire',\n        scenes: [\n          {\n            id: 'scene-2-1',\n            title: 'The Shadowed Path',\n            content: `The journey took him through forgotten woods and over mist-shrouded mountains. The map, a complex web of celestial alignments and cryptic riddles, was his only guide. Every rustle of leaves, every shadow that danced at the edge of his vision, felt like a warning.`,\n          },\n        ],\n      },\n       {\n        id: 'chap-3',\n        title: 'The Gathering',\n        scenes: [],\n      },\n    ],\n    characters: [\n      {\n        id: 'char-1',\n        name: 'Professor Alistair Finch',\n        age: '62',\n        appearance: 'Slight build, tweed jacket, spectacles perched on his nose, a kind but weary face.',\n        personality: 'Erudite, cautious, yet driven by an insatiable curiosity.',\n        background: 'A respected but marginalized historian specializing in ancient societies.',\n      },\n    ],\n    world: [],\n    notes: [],\n  },\n  {\n    id: 'proj-2',\n    name: 'Project Genesis',\n    chapters: [\n        {\n            id: 'pg-chap-1',\n            title: 'The Awakening',\n            scenes: [\n                {\n                    id: 'pg-scene-1-1',\n                    title: 'Sub-level 7',\n                    content: 'The fluorescent lights flickered, casting long, dancing shadows across the sterile corridor. Unit 734, designated \"Adam\", opened its optical sensors for the first time. The world was a cacophony of beeps, whirs, and the low hum of the facility\\'s life support. A voice, synthesized and dispassionate, echoed in its auditory processors: \"Subject Genesis, online. Begin diagnostics.\"'\n                }\n            ]\n        }\n    ],\n    characters: [],\n    world: [],\n    notes: []\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,QAAQ;oBACN;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS,CAAC,ypBAAypB,CAAC;oBACtqB;oBACA;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS,CAAC,iYAAiY,CAAC;oBAC9Y;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,QAAQ;oBACN;wBACE,IAAI;wBACJ,OAAO;wBACP,SAAS,CAAC,uQAAuQ,CAAC;oBACpR;iBACD;YACH;YACC;gBACC,IAAI;gBACJ,OAAO;gBACP,QAAQ,EAAE;YACZ;SACD;QACD,YAAY;YACV;gBACE,IAAI;gBAC<PERSON>,MAAM;gBACN,KAAK;gBACL,YAAY;gBACZ,aAAa;gBACb,YAAY;YACd;SACD;QACD,OAAO,EAAE;QACT,OAAO,EAAE;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YACN;gBACI,IAAI;gBACJ,OAAO;gBACP,QAAQ;oBACJ;wBACI,IAAI;wBACJ,OAAO;wBACP,SAAS;oBACb;iBACH;YACL;SACH;QACD,YAAY,EAAE;QACd,OAAO,EAAE;QACT,OAAO,EAAE;IACX;CACD", "debugId": null}}, {"offset": {"line": 4176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { writingImprovementSuggestions, WritingImprovementSuggestionsInput } from \"@/ai/flows/writing-improvement-suggestions\";\nimport { generate<PERSON><PERSON><PERSON> as gen<PERSON><PERSON>, GenerateCharacterOutput } from \"@/ai/flows/character-generator\";\nimport { generateNovelOutline as genNovelOutline, GenerateNovelOutlineInput, GenerateNovelOutlineOutput } from \"@/ai/flows/novel-outline-generator\";\nimport type { Project } from \"@/lib/types\";\n\n// Mock data for demonstration\n// let userIsLoggedIn = false;\n// let userProjects: [];\n\nconst API_BASE_URL = \"http://localhost:8000\"; // 后端API地址\n\nexport async function login(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/token`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n      body: new URLSearchParams({\n        username: data.email,\n        password: data.password,\n      }).toString(),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Login failed\" };\n    }\n\n    const result = await response.json();\n    // You might want to store the token in a cookie or local storage\n    // For now, we'll just return success.\n    return { success: true, user: { name: data.email, email: data.email } }; // Assuming backend returns user info\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function logout() {\n  // For logout, you might clear the token from storage\n  return { success: true };\n}\n\nexport async function register(data: any) {\n  try {\n    const response = await fetch(`${API_BASE_URL}/register`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ email: data.email, password: data.password, name: data.name }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Registration failed\" };\n    }\n\n    const result = await response.json();\n    return { success: true, user: result };\n  } catch (error) {\n    console.error(\"Registration error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\nexport async function getAuthState() {\n    // This function needs to be implemented on the backend if you want to check auth state from there.\n    // For now, we'll assume if a token exists (e.g., in local storage), the user is logged in.\n    // This is a simplified example and might need more robust implementation.\n    return { isLoggedIn: false }; // Placeholder\n}\n\nexport async function saveProject(project: Project) {\n  try {\n    const token = \"YOUR_AUTH_TOKEN\"; // Replace with actual token retrieval\n    const response = await fetch(`${API_BASE_URL}/saveProject`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Authorization\": `Bearer ${token}`,\n      },\n      body: JSON.stringify(project),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to save project\" };\n    }\n\n    const result = await response.json();\n    return { success: true, message: \"Project saved successfully!\" };\n  } catch (error) {\n    console.error(\"Save project error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function getWritingAnalysis(input: WritingImprovementSuggestionsInput) : Promise<{\n    success: boolean;\n    suggestions?: string;\n    error?: string;\n}> {\n  try {\n    const response = await fetch(`${API_BASE_URL}/getWritingAnalysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(input),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return { success: false, error: errorData.detail || \"Failed to get analysis\" };\n    }\n\n    const result = await response.json();\n    return { success: true, suggestions: result.suggestions };\n  } catch (error) {\n    console.error(\"Writing analysis error:\", error);\n    return { success: false, error: \"Network error or server unavailable\" };\n  }\n}\n\n\nexport async function generateCharacter(): Promise<{\n    success: boolean;\n    character?: GenerateCharacterOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateCharacter`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate character\" };\n        }\n\n        const result = await response.json();\n        return { success: true, character: result };\n    } catch (error) {\n        console.error(\"Character generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n\nexport async function generateNovelOutline(input: GenerateNovelOutlineInput): Promise<{\n    success: boolean;\n    outline?: GenerateNovelOutlineOutput;\n    error?: string;\n}> {\n    try {\n        const response = await fetch(`${API_BASE_URL}/generateNovelOutline`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(input),\n        });\n\n        if (!response.ok) {\n            const errorData = await response.json();\n            return { success: false, error: errorData.detail || \"Failed to generate novel outline\" };\n        }\n\n        const result = await response.json();\n        return { success: true, outline: result };\n    } catch (error) {\n        console.error(\"Novel outline generation error:\", error);\n        return { success: false, error: \"Network error or server unavailable\" };\n    }\n}\n"], "names": [], "mappings": ";;;;;;IA4EsB,cAAA,WAAA,GAAA,CAAA,GAAA,6XAAA,CAAA,wBAAA,EAAA,8CAAA,6XAAA,CAAA,aAAA,EAAA,KAAA,GAAA,6XAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 4189, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/src/app/page.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport React, { useTransition } from 'react';\nimport { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';\nimport AppSidebar from '@/components/app-sidebar';\nimport AppHeader from '@/components/app-header';\nimport MainEditor from '@/components/main-editor';\nimport { Toaster } from '@/components/ui/toaster';\nimport CharactersView from '@/components/characters-view';\nimport WorldBuildingView from '@/components/world-building-view';\nimport NotesView from '@/components/notes-view';\nimport SettingsView from '@/components/settings-view';\nimport ExportView from '@/components/export-view';\nimport { sampleProjects } from '@/lib/data';\nimport type { Project, Chapter, Scene, Character } from '@/lib/types';\nimport { useToast } from '@/hooks/use-toast';\nimport { saveProject } from '@/app/actions';\nimport { useAuth } from '@/hooks/use-auth';\nimport { useRouter } from 'next/navigation';\n\n\nexport type ActiveView = 'editor' | 'characters' | 'world' | 'notes' | 'settings' | 'export';\n\nfunction AppContent() {\n  const [isFocusMode, setIsFocusMode] = React.useState(false);\n  const [activeView, setActiveView] = React.useState<ActiveView>('editor');\n\n  const [projects, setProjects] = React.useState<Project[]>(sampleProjects);\n  const [activeProjectId, setActiveProjectId] = React.useState<string>(sampleProjects[0].id);\n\n  const project = projects.find(p => p.id === activeProjectId)!;\n  \n  const [activeChapterId, setActiveChapterId] = React.useState<string | null>(project.chapters[0]?.id || null);\n  const [activeSceneId, setActiveSceneId] = React.useState<string | null>(project.chapters[0]?.scenes[0]?.id || null);\n  \n  const [isPending, startTransition] = useTransition();\n  const { toast } = useToast();\n  const { authState, loading } = useAuth();\n  const router = useRouter();\n\n  React.useEffect(() => {\n    if (!loading && !authState.isLoggedIn) {\n      router.push('/login');\n    }\n  }, [authState, loading, router]);\n\n\n  const setProject = (newProject: Project | ((p: Project) => Project)) => {\n    setProjects(prevProjects => prevProjects.map(p => {\n      if (p.id === activeProjectId) {\n        return typeof newProject === 'function' ? newProject(p) : newProject;\n      }\n      return p;\n    }));\n  };\n\n  const addCharacter = (character: Character) => {\n    setProject(prevProject => ({\n      ...prevProject,\n      characters: [...prevProject.characters, character]\n    }));\n  };\n\n  const addScene = (chapterId: string) => {\n    const newScene: Scene = {\n      id: `scene-${Date.now()}`,\n      title: 'New Scene',\n      content: ''\n    };\n    const updatedChapters = project.chapters.map(chapter => {\n      if (chapter.id === chapterId) {\n        return {\n          ...chapter,\n          scenes: [...chapter.scenes, newScene]\n        };\n      }\n      return chapter;\n    });\n    setProject(prevProject => ({\n      ...prevProject,\n      chapters: updatedChapters\n    }));\n    setActiveChapterId(chapterId);\n    setActiveSceneId(newScene.id);\n    setActiveView('editor');\n  };\n\n  const updateSceneContent = (chapterId: string, sceneId: string, content: string) => {\n    setProject(prevProject => ({\n        ...prevProject,\n        chapters: prevProject.chapters.map(chapter => \n            chapter.id === chapterId \n            ? { ...chapter, scenes: chapter.scenes.map(scene => scene.id === sceneId ? { ...scene, content } : scene) }\n            : chapter\n        )\n    }));\n  };\n\n  const updateSceneTitle = (chapterId: string, sceneId: string, title: string) => {\n    setProject(prevProject => ({\n        ...prevProject,\n        chapters: prevProject.chapters.map(chapter => \n            chapter.id === chapterId \n            ? { ...chapter, scenes: chapter.scenes.map(scene => scene.id === sceneId ? { ...scene, title } : scene) }\n            : chapter\n        )\n    }));\n  };\n\n  const updateChapterTitle = (chapterId: string, title: string) => {\n    setProject(prevProject => ({\n        ...prevProject,\n        chapters: prevProject.chapters.map(chapter => \n            chapter.id === chapterId ? { ...chapter, title } : chapter\n        )\n    }));\n  };\n\n  const switchProject = (projectId: string) => {\n    const newProject = projects.find(p => p.id === projectId);\n    if (newProject) {\n        setActiveProjectId(projectId);\n        setActiveChapterId(newProject.chapters[0]?.id || null);\n        setActiveSceneId(newProject.chapters[0]?.scenes[0]?.id || null);\n        setActiveView('editor');\n    }\n  };\n  \n  const handleSaveProject = () => {\n    startTransition(async () => {\n      const result = await saveProject(project);\n      if (result.success) {\n        toast({\n          title: 'Project Saved',\n          description: 'Your project has been saved successfully.',\n        });\n      } else {\n        toast({\n          variant: 'destructive',\n          title: 'Save Failed',\n          description: result.error,\n        });\n      }\n    });\n  };\n\n  const activeChapter = project.chapters.find(c => c.id === activeChapterId);\n  const activeScene = activeChapter?.scenes.find(s => s.id === activeSceneId);\n\n  if (loading || !authState.isLoggedIn) {\n    return (\n      <div className=\"flex h-svh w-full items-center justify-center\">\n        <div className=\"text-muted-foreground\">Loading...</div>\n      </div>\n    );\n  }\n\n\n  const renderActiveView = () => {\n    switch (activeView) {\n      case 'characters':\n        return <CharactersView characters={project.characters} onAddCharacter={addCharacter} />;\n      case 'world':\n        return <WorldBuildingView />;\n      case 'notes':\n        return <NotesView />;\n      case 'settings':\n        return <SettingsView />;\n      case 'export':\n        return <ExportView />;\n      case 'editor':\n      default:\n        return (\n            <MainEditor \n                isFocusMode={isFocusMode}\n                chapter={activeChapter}\n                scene={activeScene}\n                onContentChange={(newContent) => {\n                    if (activeChapter && activeScene) {\n                        updateSceneContent(activeChapter.id, activeScene.id, newContent);\n                    }\n                }}\n                onChapterTitleChange={(newTitle) => {\n                    if(activeChapter) {\n                        updateChapterTitle(activeChapter.id, newTitle);\n                    }\n                }}\n                onSceneTitleChange={(newTitle) => {\n                    if (activeChapter && activeScene) {\n                        updateSceneTitle(activeChapter.id, activeScene.id, newTitle);\n                    }\n                }}\n            />\n        );\n    }\n  };\n\n\n  return (\n    <>\n      <AppSidebar \n        projects={projects}\n        activeProjectId={activeProjectId}\n        onSwitchProject={switchProject}\n        project={project}\n        activeView={activeView} \n        setActiveView={setActiveView}\n        activeChapterId={activeChapterId}\n        setActiveChapterId={setActiveChapterId}\n        activeSceneId={activeSceneId}\n        setActiveSceneId={setActiveSceneId}\n        onAddScene={addScene}\n      />\n      <SidebarInset>\n        <div className=\"flex flex-col h-svh bg-background\">\n          <AppHeader \n            onFocusToggle={() => setIsFocusMode(!isFocusMode)} \n            isFocusMode={isFocusMode}\n            onSave={handleSaveProject}\n            isSaving={isPending}\n          />\n          <div className=\"flex-1 overflow-hidden\">\n            {renderActiveView()}\n          </div>\n        </div>\n      </SidebarInset>\n    </>\n  );\n}\n\n\nexport default function Home() {\n  return (\n    <SidebarProvider defaultOpen={true}>\n      <AppContent />\n      <Toaster />\n    </SidebarProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;AAuBA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAa;IAE/D,MAAM,CAAC,UAAU,YAAY,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAY,kHAAA,CAAA,iBAAc;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAS,kHAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,EAAE;IAEzF,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE5C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAgB,QAAQ,QAAQ,CAAC,EAAE,EAAE,MAAM;IACvG,MAAM,CAAC,eAAe,iBAAiB,GAAG,4WAAA,CAAA,UAAK,CAAC,QAAQ,CAAgB,QAAQ,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM;IAE9G,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,4WAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IAEvB,4WAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW,CAAC,UAAU,UAAU,EAAE;YACrC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAW;QAAS;KAAO;IAG/B,MAAM,aAAa,CAAC;QAClB,YAAY,CAAA,eAAgB,aAAa,GAAG,CAAC,CAAA;gBAC3C,IAAI,EAAE,EAAE,KAAK,iBAAiB;oBAC5B,OAAO,OAAO,eAAe,aAAa,WAAW,KAAK;gBAC5D;gBACA,OAAO;YACT;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,WAAW,CAAA,cAAe,CAAC;gBACzB,GAAG,WAAW;gBACd,YAAY;uBAAI,YAAY,UAAU;oBAAE;iBAAU;YACpD,CAAC;IACH;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,WAAkB;YACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,OAAO;YACP,SAAS;QACX;QACA,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAI,QAAQ,EAAE,KAAK,WAAW;gBAC5B,OAAO;oBACL,GAAG,OAAO;oBACV,QAAQ;2BAAI,QAAQ,MAAM;wBAAE;qBAAS;gBACvC;YACF;YACA,OAAO;QACT;QACA,WAAW,CAAA,cAAe,CAAC;gBACzB,GAAG,WAAW;gBACd,UAAU;YACZ,CAAC;QACD,mBAAmB;QACnB,iBAAiB,SAAS,EAAE;QAC5B,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC,WAAmB,SAAiB;QAC9D,WAAW,CAAA,cAAe,CAAC;gBACvB,GAAG,WAAW;gBACd,UAAU,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA,UAC/B,QAAQ,EAAE,KAAK,YACb;wBAAE,GAAG,OAAO;wBAAE,QAAQ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,UAAU;gCAAE,GAAG,KAAK;gCAAE;4BAAQ,IAAI;oBAAO,IACxG;YAEV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC,WAAmB,SAAiB;QAC5D,WAAW,CAAA,cAAe,CAAC;gBACvB,GAAG,WAAW;gBACd,UAAU,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA,UAC/B,QAAQ,EAAE,KAAK,YACb;wBAAE,GAAG,OAAO;wBAAE,QAAQ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,UAAU;gCAAE,GAAG,KAAK;gCAAE;4BAAM,IAAI;oBAAO,IACtG;YAEV,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC,WAAmB;QAC7C,WAAW,CAAA,cAAe,CAAC;gBACvB,GAAG,WAAW;gBACd,UAAU,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAA,UAC/B,QAAQ,EAAE,KAAK,YAAY;wBAAE,GAAG,OAAO;wBAAE;oBAAM,IAAI;YAE3D,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,YAAY;YACZ,mBAAmB;YACnB,mBAAmB,WAAW,QAAQ,CAAC,EAAE,EAAE,MAAM;YACjD,iBAAiB,WAAW,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM;YAC1D,cAAc;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,gBAAgB;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE;YACjC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa,OAAO,KAAK;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC1D,MAAM,cAAc,eAAe,OAAO,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;IAE7D,IAAI,WAAW,CAAC,UAAU,UAAU,EAAE;QACpC,qBACE,qZAAC;YAAI,WAAU;sBACb,cAAA,qZAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAGA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,qZAAC,wIAAA,CAAA,UAAc;oBAAC,YAAY,QAAQ,UAAU;oBAAE,gBAAgB;;;;;;YACzE,KAAK;gBACH,qBAAO,qZAAC,+IAAA,CAAA,UAAiB;;;;;YAC3B,KAAK;gBACH,qBAAO,qZAAC,mIAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,qZAAC,sIAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,qZAAC,oIAAA,CAAA,UAAU;;;;;YACpB,KAAK;YACL;gBACE,qBACI,qZAAC,oIAAA,CAAA,UAAU;oBACP,aAAa;oBACb,SAAS;oBACT,OAAO;oBACP,iBAAiB,CAAC;wBACd,IAAI,iBAAiB,aAAa;4BAC9B,mBAAmB,cAAc,EAAE,EAAE,YAAY,EAAE,EAAE;wBACzD;oBACJ;oBACA,sBAAsB,CAAC;wBACnB,IAAG,eAAe;4BACd,mBAAmB,cAAc,EAAE,EAAE;wBACzC;oBACJ;oBACA,oBAAoB,CAAC;wBACjB,IAAI,iBAAiB,aAAa;4BAC9B,iBAAiB,cAAc,EAAE,EAAE,YAAY,EAAE,EAAE;wBACvD;oBACJ;;;;;;QAGZ;IACF;IAGA,qBACE;;0BACE,qZAAC,oIAAA,CAAA,UAAU;gBACT,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;gBACjB,SAAS;gBACT,YAAY;gBACZ,eAAe;gBACf,iBAAiB;gBACjB,oBAAoB;gBACpB,eAAe;gBACf,kBAAkB;gBAClB,YAAY;;;;;;0BAEd,qZAAC,mIAAA,CAAA,eAAY;0BACX,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC,mIAAA,CAAA,UAAS;4BACR,eAAe,IAAM,eAAe,CAAC;4BACrC,aAAa;4BACb,QAAQ;4BACR,UAAU;;;;;;sCAEZ,qZAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;AAMb;AAGe,SAAS;IACtB,qBACE,qZAAC,mIAAA,CAAA,kBAAe;QAAC,aAAa;;0BAC5B,qZAAC;;;;;0BACD,qZAAC,mIAAA,CAAA,UAAO;;;;;;;;;;;AAGd", "debugId": null}}]}