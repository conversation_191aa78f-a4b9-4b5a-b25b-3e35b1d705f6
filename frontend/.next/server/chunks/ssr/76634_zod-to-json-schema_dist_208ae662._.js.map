{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/Options.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getDefaultOptions = exports.defaultOptions = exports.jsonDescription = exports.ignoreOverride = void 0;\nexports.ignoreOverride = Symbol(\"Let zodToJsonSchema decide on which parser to use\");\nconst jsonDescription = (jsonSchema, def) => {\n    if (def.description) {\n        try {\n            return {\n                ...jsonSchema,\n                ...JSON.parse(def.description),\n            };\n        }\n        catch { }\n    }\n    return jsonSchema;\n};\nexports.jsonDescription = jsonDescription;\nexports.defaultOptions = {\n    name: undefined,\n    $refStrategy: \"root\",\n    basePath: [\"#\"],\n    effectStrategy: \"input\",\n    pipeStrategy: \"all\",\n    dateStrategy: \"format:date-time\",\n    mapStrategy: \"entries\",\n    removeAdditionalStrategy: \"passthrough\",\n    allowedAdditionalProperties: true,\n    rejectedAdditionalProperties: false,\n    definitionPath: \"definitions\",\n    target: \"jsonSchema7\",\n    strictUnions: false,\n    definitions: {},\n    errorMessages: false,\n    markdownDescription: false,\n    patternStrategy: \"escape\",\n    applyRegexFlags: false,\n    emailStrategy: \"format:email\",\n    base64Strategy: \"contentEncoding:base64\",\n    nameStrategy: \"ref\",\n    openAiAnyTypeName: \"OpenAiAnyType\"\n};\nconst getDefaultOptions = (options) => (typeof options === \"string\"\n    ? {\n        ...exports.defaultOptions,\n        name: options,\n    }\n    : {\n        ...exports.defaultOptions,\n        ...options,\n    });\nexports.getDefaultOptions = getDefaultOptions;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,cAAc,GAAG,KAAK;AAC7G,QAAQ,cAAc,GAAG,OAAO;AAChC,MAAM,kBAAkB,CAAC,YAAY;IACjC,IAAI,IAAI,WAAW,EAAE;QACjB,IAAI;YACA,OAAO;gBACH,GAAG,UAAU;gBACb,GAAG,KAAK,KAAK,CAAC,IAAI,WAAW,CAAC;YAClC;QACJ,EACA,OAAM,CAAE;IACZ;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG;AAC1B,QAAQ,cAAc,GAAG;IACrB,MAAM;IACN,cAAc;IACd,UAAU;QAAC;KAAI;IACf,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,aAAa;IACb,0BAA0B;IAC1B,6BAA6B;IAC7B,8BAA8B;IAC9B,gBAAgB;IAChB,QAAQ;IACR,cAAc;IACd,aAAa,CAAC;IACd,eAAe;IACf,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,mBAAmB;AACvB;AACA,MAAM,oBAAoB,CAAC,UAAa,OAAO,YAAY,WACrD;QACE,GAAG,QAAQ,cAAc;QACzB,MAAM;IACV,IACE;QACE,GAAG,QAAQ,cAAc;QACzB,GAAG,OAAO;IACd;AACJ,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/Refs.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRefs = void 0;\nconst Options_js_1 = require(\"./Options.js\");\nconst getRefs = (options) => {\n    const _options = (0, Options_js_1.getDefaultOptions)(options);\n    const currentPath = _options.name !== undefined\n        ? [..._options.basePath, _options.definitionPath, _options.name]\n        : _options.basePath;\n    return {\n        ..._options,\n        flags: { hasReferencedOpenAiAnyType: false },\n        currentPath: currentPath,\n        propertyPath: undefined,\n        seen: new Map(Object.entries(_options.definitions).map(([name, def]) => [\n            def._def,\n            {\n                def: def._def,\n                path: [..._options.basePath, _options.definitionPath, name],\n                // Resolution of references will be forced even though seen, so it's ok that the schema is undefined here for now.\n                jsonSchema: undefined,\n            },\n        ])),\n    };\n};\nexports.getRefs = getRefs;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM;AACN,MAAM,UAAU,CAAC;IACb,MAAM,WAAW,CAAC,GAAG,aAAa,iBAAiB,EAAE;IACrD,MAAM,cAAc,SAAS,IAAI,KAAK,YAChC;WAAI,SAAS,QAAQ;QAAE,SAAS,cAAc;QAAE,SAAS,IAAI;KAAC,GAC9D,SAAS,QAAQ;IACvB,OAAO;QACH,GAAG,QAAQ;QACX,OAAO;YAAE,4BAA4B;QAAM;QAC3C,aAAa;QACb,cAAc;QACd,MAAM,IAAI,IAAI,OAAO,OAAO,CAAC,SAAS,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,GAAK;gBACpE,IAAI,IAAI;gBACR;oBACI,KAAK,IAAI,IAAI;oBACb,MAAM;2BAAI,SAAS,QAAQ;wBAAE,SAAS,cAAc;wBAAE;qBAAK;oBAC3D,kHAAkH;oBAClH,YAAY;gBAChB;aACH;IACL;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/errorMessages.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setResponseValueAndErrors = exports.addErrorMessage = void 0;\nfunction addErrorMessage(res, key, errorMessage, refs) {\n    if (!refs?.errorMessages)\n        return;\n    if (errorMessage) {\n        res.errorMessage = {\n            ...res.errorMessage,\n            [key]: errorMessage,\n        };\n    }\n}\nexports.addErrorMessage = addErrorMessage;\nfunction setResponseValueAndErrors(res, key, value, errorMessage, refs) {\n    res[key] = value;\n    addErrorMessage(res, key, errorMessage, refs);\n}\nexports.setResponseValueAndErrors = setResponseValueAndErrors;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG,QAAQ,eAAe,GAAG,KAAK;AACnE,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI;IACjD,IAAI,CAAC,MAAM,eACP;IACJ,IAAI,cAAc;QACd,IAAI,YAAY,GAAG;YACf,GAAG,IAAI,YAAY;YACnB,CAAC,IAAI,EAAE;QACX;IACJ;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,0BAA0B,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI;IAClE,GAAG,CAAC,IAAI,GAAG;IACX,gBAAgB,KAAK,KAAK,cAAc;AAC5C;AACA,QAAQ,yBAAyB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/getRelativePath.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRelativePath = void 0;\nconst getRelativePath = (pathA, pathB) => {\n    let i = 0;\n    for (; i < pathA.length && i < pathB.length; i++) {\n        if (pathA[i] !== pathB[i])\n            break;\n    }\n    return [(pathA.length - i).toString(), ...pathB.slice(i)].join(\"/\");\n};\nexports.getRelativePath = getRelativePath;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM,kBAAkB,CAAC,OAAO;IAC5B,IAAI,IAAI;IACR,MAAO,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE,IAAK;QAC9C,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EACrB;IACR;IACA,OAAO;QAAC,CAAC,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ;WAAO,MAAM,KAAK,CAAC;KAAG,CAAC,IAAI,CAAC;AACnE;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/any.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseAnyDef = void 0;\nconst getRelativePath_js_1 = require(\"../getRelativePath.js\");\nfunction parseAnyDef(refs) {\n    if (refs.target !== \"openAi\") {\n        return {};\n    }\n    const anyDefinitionPath = [\n        ...refs.basePath,\n        refs.definitionPath,\n        refs.openAiAnyTypeName,\n    ];\n    refs.flags.hasReferencedOpenAiAnyType = true;\n    return {\n        $ref: refs.$refStrategy === \"relative\"\n            ? (0, getRelativePath_js_1.getRelativePath)(anyDefinitionPath, refs.currentPath)\n            : anyDefinitionPath.join(\"/\"),\n    };\n}\nexports.parseAnyDef = parseAnyDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,SAAS,YAAY,IAAI;IACrB,IAAI,KAAK,MAAM,KAAK,UAAU;QAC1B,OAAO,CAAC;IACZ;IACA,MAAM,oBAAoB;WACnB,KAAK,QAAQ;QAChB,KAAK,cAAc;QACnB,KAAK,iBAAiB;KACzB;IACD,KAAK,KAAK,CAAC,0BAA0B,GAAG;IACxC,OAAO;QACH,MAAM,KAAK,YAAY,KAAK,aACtB,CAAC,GAAG,qBAAqB,eAAe,EAAE,mBAAmB,KAAK,WAAW,IAC7E,kBAAkB,IAAI,CAAC;IACjC;AACJ;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/array.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseArrayDef = void 0;\nconst zod_1 = require(\"zod\");\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseArrayDef(def, refs) {\n    const res = {\n        type: \"array\",\n    };\n    if (def.type?._def &&\n        def.type?._def?.typeName !== zod_1.ZodFirstPartyTypeKind.ZodAny) {\n        res.items = (0, parseDef_js_1.parseDef)(def.type._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"items\"],\n        });\n    }\n    if (def.minLength) {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minItems\", def.minLength.value, def.minLength.message, refs);\n    }\n    if (def.maxLength) {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maxItems\", def.maxLength.value, def.maxLength.message, refs);\n    }\n    if (def.exactLength) {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minItems\", def.exactLength.value, def.exactLength.message, refs);\n        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maxItems\", def.exactLength.value, def.exactLength.message, refs);\n    }\n    return res;\n}\nexports.parseArrayDef = parseArrayDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,IAAI,IAAI,EAAE,QACV,IAAI,IAAI,EAAE,MAAM,aAAa,MAAM,qBAAqB,CAAC,MAAM,EAAE;QACjE,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;YACnD,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAQ;QAC/C;IACJ;IACA,IAAI,IAAI,SAAS,EAAE;QACf,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,YAAY,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;IACnH;IACA,IAAI,IAAI,SAAS,EAAE;QACf,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,YAAY,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;IACnH;IACA,IAAI,IAAI,WAAW,EAAE;QACjB,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,YAAY,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;QACnH,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,YAAY,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;IACvH;IACA,OAAO;AACX;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/bigint.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseBigintDef = void 0;\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nfunction parseBigintDef(def, refs) {\n    const res = {\n        type: \"integer\",\n        format: \"int64\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\nexports.parseBigintDef = parseBigintDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,MAAM,MAAM;QACR,MAAM;QACN,QAAQ;IACZ;IACA,IAAI,CAAC,IAAI,MAAM,EACX,OAAO;IACX,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClG,OACK;wBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC3G;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAClG;gBACA;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClG,OACK;wBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC3G;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAClG;gBACA;YACJ,KAAK;gBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,cAAc,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBACjG;QACR;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/boolean.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseBooleanDef = void 0;\nfunction parseBooleanDef() {\n    return {\n        type: \"boolean\",\n    };\n}\nexports.parseBooleanDef = parseBooleanDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,SAAS;IACL,OAAO;QACH,MAAM;IACV;AACJ;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/branded.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseBrandedDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseBrandedDef(_def, refs) {\n    return (0, parseDef_js_1.parseDef)(_def.type._def, refs);\n}\nexports.parseBrandedDef = parseBrandedDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,SAAS,gBAAgB,IAAI,EAAE,IAAI;IAC/B,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE;AACvD;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/catch.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseCatchDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst parseCatchDef = (def, refs) => {\n    return (0, parseDef_js_1.parseDef)(def.innerType._def, refs);\n};\nexports.parseCatchDef = parseCatchDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM,gBAAgB,CAAC,KAAK;IACxB,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;AAC3D;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/date.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseDateDef = void 0;\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nfunction parseDateDef(def, refs, overrideDateStrategy) {\n    const strategy = overrideDateStrategy ?? refs.dateStrategy;\n    if (Array.isArray(strategy)) {\n        return {\n            anyOf: strategy.map((item, i) => parseDateDef(def, refs, item)),\n        };\n    }\n    switch (strategy) {\n        case \"string\":\n        case \"format:date-time\":\n            return {\n                type: \"string\",\n                format: \"date-time\",\n            };\n        case \"format:date\":\n            return {\n                type: \"string\",\n                format: \"date\",\n            };\n        case \"integer\":\n            return integerDateParser(def, refs);\n    }\n}\nexports.parseDateDef = parseDateDef;\nconst integerDateParser = (def, refs) => {\n    const res = {\n        type: \"integer\",\n        format: \"unix-time\",\n    };\n    if (refs.target === \"openApi3\") {\n        return res;\n    }\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minimum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n            case \"max\":\n                (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maximum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n        }\n    }\n    return res;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,KAAK;AAC5B,MAAM;AACN,SAAS,aAAa,GAAG,EAAE,IAAI,EAAE,oBAAoB;IACjD,MAAM,WAAW,wBAAwB,KAAK,YAAY;IAC1D,IAAI,MAAM,OAAO,CAAC,WAAW;QACzB,OAAO;YACH,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM,IAAM,aAAa,KAAK,MAAM;QAC7D;IACJ;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;QACJ,KAAK;YACD,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;QACJ,KAAK;YACD,OAAO,kBAAkB,KAAK;IACtC;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,MAAM,oBAAoB,CAAC,KAAK;IAC5B,MAAM,MAAM;QACR,MAAM;QACN,QAAQ;IACZ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;IACX;IACA,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAC7E,MAAM,OAAO,EAAE;gBACf;YACJ,KAAK;gBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAC7E,MAAM,OAAO,EAAE;gBACf;QACR;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/default.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseDefaultDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseDefaultDef(_def, refs) {\n    return {\n        ...(0, parseDef_js_1.parseDef)(_def.innerType._def, refs),\n        default: _def.defaultValue(),\n    };\n}\nexports.parseDefaultDef = parseDefaultDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,SAAS,gBAAgB,IAAI,EAAE,IAAI;IAC/B,OAAO;QACH,GAAG,CAAC,GAAG,cAAc,QAAQ,EAAE,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK;QACzD,SAAS,KAAK,YAAY;IAC9B;AACJ;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/effects.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseEffectsDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst any_js_1 = require(\"./any.js\");\nfunction parseEffectsDef(_def, refs) {\n    return refs.effectStrategy === \"input\"\n        ? (0, parseDef_js_1.parseDef)(_def.schema._def, refs)\n        : (0, any_js_1.parseAnyDef)(refs);\n}\nexports.parseEffectsDef = parseEffectsDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,MAAM;AACN,SAAS,gBAAgB,IAAI,EAAE,IAAI;IAC/B,OAAO,KAAK,cAAc,KAAK,UACzB,CAAC,GAAG,cAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,QAC9C,CAAC,GAAG,SAAS,WAAW,EAAE;AACpC;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/enum.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseEnumDef = void 0;\nfunction parseEnumDef(def) {\n    return {\n        type: \"string\",\n        enum: Array.from(def.values),\n    };\n}\nexports.parseEnumDef = parseEnumDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,KAAK;AAC5B,SAAS,aAAa,GAAG;IACrB,OAAO;QACH,MAAM;QACN,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM;IAC/B;AACJ;AACA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/intersection.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseIntersectionDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst isJsonSchema7AllOfType = (type) => {\n    if (\"type\" in type && type.type === \"string\")\n        return false;\n    return \"allOf\" in type;\n};\nfunction parseIntersectionDef(def, refs) {\n    const allOf = [\n        (0, parseDef_js_1.parseDef)(def.left._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n        }),\n        (0, parseDef_js_1.parseDef)(def.right._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"1\"],\n        }),\n    ].filter((x) => !!x);\n    let unevaluatedProperties = refs.target === \"jsonSchema2019-09\"\n        ? { unevaluatedProperties: false }\n        : undefined;\n    const mergedAllOf = [];\n    // If either of the schemas is an allOf, merge them into a single allOf\n    allOf.forEach((schema) => {\n        if (isJsonSchema7AllOfType(schema)) {\n            mergedAllOf.push(...schema.allOf);\n            if (schema.unevaluatedProperties === undefined) {\n                // If one of the schemas has no unevaluatedProperties set,\n                // the merged schema should also have no unevaluatedProperties set\n                unevaluatedProperties = undefined;\n            }\n        }\n        else {\n            let nestedSchema = schema;\n            if (\"additionalProperties\" in schema &&\n                schema.additionalProperties === false) {\n                const { additionalProperties, ...rest } = schema;\n                nestedSchema = rest;\n            }\n            else {\n                // As soon as one of the schemas has additionalProperties set not to false, we allow unevaluatedProperties\n                unevaluatedProperties = undefined;\n            }\n            mergedAllOf.push(nestedSchema);\n        }\n    });\n    return mergedAllOf.length\n        ? {\n            allOf: mergedAllOf,\n            ...unevaluatedProperties,\n        }\n        : undefined;\n}\nexports.parseIntersectionDef = parseIntersectionDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,oBAAoB,GAAG,KAAK;AACpC,MAAM;AACN,MAAM,yBAAyB,CAAC;IAC5B,IAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,UAChC,OAAO;IACX,OAAO,WAAW;AACtB;AACA,SAAS,qBAAqB,GAAG,EAAE,IAAI;IACnC,MAAM,QAAQ;QACV,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;YACvC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS;aAAI;QACpD;QACA,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE;YACxC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS;aAAI;QACpD;KACH,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;IAClB,IAAI,wBAAwB,KAAK,MAAM,KAAK,sBACtC;QAAE,uBAAuB;IAAM,IAC/B;IACN,MAAM,cAAc,EAAE;IACtB,uEAAuE;IACvE,MAAM,OAAO,CAAC,CAAC;QACX,IAAI,uBAAuB,SAAS;YAChC,YAAY,IAAI,IAAI,OAAO,KAAK;YAChC,IAAI,OAAO,qBAAqB,KAAK,WAAW;gBAC5C,0DAA0D;gBAC1D,kEAAkE;gBAClE,wBAAwB;YAC5B;QACJ,OACK;YACD,IAAI,eAAe;YACnB,IAAI,0BAA0B,UAC1B,OAAO,oBAAoB,KAAK,OAAO;gBACvC,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,GAAG;gBAC1C,eAAe;YACnB,OACK;gBACD,0GAA0G;gBAC1G,wBAAwB;YAC5B;YACA,YAAY,IAAI,CAAC;QACrB;IACJ;IACA,OAAO,YAAY,MAAM,GACnB;QACE,OAAO;QACP,GAAG,qBAAqB;IAC5B,IACE;AACV;AACA,QAAQ,oBAAoB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/literal.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseLiteralDef = void 0;\nfunction parseLiteralDef(def, refs) {\n    const parsedType = typeof def.value;\n    if (parsedType !== \"bigint\" &&\n        parsedType !== \"number\" &&\n        parsedType !== \"boolean\" &&\n        parsedType !== \"string\") {\n        return {\n            type: Array.isArray(def.value) ? \"array\" : \"object\",\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        return {\n            type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n            enum: [def.value],\n        };\n    }\n    return {\n        type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n        const: def.value,\n    };\n}\nexports.parseLiteralDef = parseLiteralDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,SAAS,gBAAgB,GAAG,EAAE,IAAI;IAC9B,MAAM,aAAa,OAAO,IAAI,KAAK;IACnC,IAAI,eAAe,YACf,eAAe,YACf,eAAe,aACf,eAAe,UAAU;QACzB,OAAO;YACH,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK,IAAI,UAAU;QAC/C;IACJ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;YACH,MAAM,eAAe,WAAW,YAAY;YAC5C,MAAM;gBAAC,IAAI,KAAK;aAAC;QACrB;IACJ;IACA,OAAO;QACH,MAAM,eAAe,WAAW,YAAY;QAC5C,OAAO,IAAI,KAAK;IACpB;AACJ;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/string.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseStringDef = exports.zodPatterns = void 0;\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nlet emojiRegex = undefined;\n/**\n * Generated from the regular expressions found here as of 2024-05-22:\n * https://github.com/colinhacks/zod/blob/master/src/types.ts.\n *\n * Expressions with /i flag have been changed accordingly.\n */\nexports.zodPatterns = {\n    /**\n     * `c` was changed to `[cC]` to replicate /i flag\n     */\n    cuid: /^[cC][^\\s-]{8,}$/,\n    cuid2: /^[0-9a-z]+$/,\n    ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,\n    /**\n     * `a-z` was added to replicate /i flag\n     */\n    email: /^(?!\\.)(?!.*\\.\\.)([a-zA-Z0-9_'+\\-\\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\\-]*\\.)+[a-zA-Z]{2,}$/,\n    /**\n     * Constructed a valid Unicode RegExp\n     *\n     * Lazily instantiate since this type of regex isn't supported\n     * in all envs (e.g. React Native).\n     *\n     * See:\n     * https://github.com/colinhacks/zod/issues/2433\n     * Fix in Zod:\n     * https://github.com/colinhacks/zod/commit/9340fd51e48576a75adc919bff65dbc4a5d4c99b\n     */\n    emoji: () => {\n        if (emojiRegex === undefined) {\n            emojiRegex = RegExp(\"^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$\", \"u\");\n        }\n        return emojiRegex;\n    },\n    /**\n     * Unused\n     */\n    uuid: /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/,\n    /**\n     * Unused\n     */\n    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,\n    ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/,\n    /**\n     * Unused\n     */\n    ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,\n    ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,\n    base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,\n    base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,\n    nanoid: /^[a-zA-Z0-9_-]{21}$/,\n    jwt: /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/,\n};\nfunction parseStringDef(def, refs) {\n    const res = {\n        type: \"string\",\n    };\n    if (def.checks) {\n        for (const check of def.checks) {\n            switch (check.kind) {\n                case \"min\":\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"max\":\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"email\":\n                    switch (refs.emailStrategy) {\n                        case \"format:email\":\n                            addFormat(res, \"email\", check.message, refs);\n                            break;\n                        case \"format:idn-email\":\n                            addFormat(res, \"idn-email\", check.message, refs);\n                            break;\n                        case \"pattern:zod\":\n                            addPattern(res, exports.zodPatterns.email, check.message, refs);\n                            break;\n                    }\n                    break;\n                case \"url\":\n                    addFormat(res, \"uri\", check.message, refs);\n                    break;\n                case \"uuid\":\n                    addFormat(res, \"uuid\", check.message, refs);\n                    break;\n                case \"regex\":\n                    addPattern(res, check.regex, check.message, refs);\n                    break;\n                case \"cuid\":\n                    addPattern(res, exports.zodPatterns.cuid, check.message, refs);\n                    break;\n                case \"cuid2\":\n                    addPattern(res, exports.zodPatterns.cuid2, check.message, refs);\n                    break;\n                case \"startsWith\":\n                    addPattern(res, RegExp(`^${escapeLiteralCheckValue(check.value, refs)}`), check.message, refs);\n                    break;\n                case \"endsWith\":\n                    addPattern(res, RegExp(`${escapeLiteralCheckValue(check.value, refs)}$`), check.message, refs);\n                    break;\n                case \"datetime\":\n                    addFormat(res, \"date-time\", check.message, refs);\n                    break;\n                case \"date\":\n                    addFormat(res, \"date\", check.message, refs);\n                    break;\n                case \"time\":\n                    addFormat(res, \"time\", check.message, refs);\n                    break;\n                case \"duration\":\n                    addFormat(res, \"duration\", check.message, refs);\n                    break;\n                case \"length\":\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"includes\": {\n                    addPattern(res, RegExp(escapeLiteralCheckValue(check.value, refs)), check.message, refs);\n                    break;\n                }\n                case \"ip\": {\n                    if (check.version !== \"v6\") {\n                        addFormat(res, \"ipv4\", check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addFormat(res, \"ipv6\", check.message, refs);\n                    }\n                    break;\n                }\n                case \"base64url\":\n                    addPattern(res, exports.zodPatterns.base64url, check.message, refs);\n                    break;\n                case \"jwt\":\n                    addPattern(res, exports.zodPatterns.jwt, check.message, refs);\n                    break;\n                case \"cidr\": {\n                    if (check.version !== \"v6\") {\n                        addPattern(res, exports.zodPatterns.ipv4Cidr, check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addPattern(res, exports.zodPatterns.ipv6Cidr, check.message, refs);\n                    }\n                    break;\n                }\n                case \"emoji\":\n                    addPattern(res, exports.zodPatterns.emoji(), check.message, refs);\n                    break;\n                case \"ulid\": {\n                    addPattern(res, exports.zodPatterns.ulid, check.message, refs);\n                    break;\n                }\n                case \"base64\": {\n                    switch (refs.base64Strategy) {\n                        case \"format:binary\": {\n                            addFormat(res, \"binary\", check.message, refs);\n                            break;\n                        }\n                        case \"contentEncoding:base64\": {\n                            (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"contentEncoding\", \"base64\", check.message, refs);\n                            break;\n                        }\n                        case \"pattern:zod\": {\n                            addPattern(res, exports.zodPatterns.base64, check.message, refs);\n                            break;\n                        }\n                    }\n                    break;\n                }\n                case \"nanoid\": {\n                    addPattern(res, exports.zodPatterns.nanoid, check.message, refs);\n                }\n                case \"toLowerCase\":\n                case \"toUpperCase\":\n                case \"trim\":\n                    break;\n                default:\n                    /* c8 ignore next */\n                    ((_) => { })(check);\n            }\n        }\n    }\n    return res;\n}\nexports.parseStringDef = parseStringDef;\nfunction escapeLiteralCheckValue(literal, refs) {\n    return refs.patternStrategy === \"escape\"\n        ? escapeNonAlphaNumeric(literal)\n        : literal;\n}\nconst ALPHA_NUMERIC = new Set(\"ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789\");\nfunction escapeNonAlphaNumeric(source) {\n    let result = \"\";\n    for (let i = 0; i < source.length; i++) {\n        if (!ALPHA_NUMERIC.has(source[i])) {\n            result += \"\\\\\";\n        }\n        result += source[i];\n    }\n    return result;\n}\n// Adds a \"format\" keyword to the schema. If a format exists, both formats will be joined in an allOf-node, along with subsequent ones.\nfunction addFormat(schema, value, message, refs) {\n    if (schema.format || schema.anyOf?.some((x) => x.format)) {\n        if (!schema.anyOf) {\n            schema.anyOf = [];\n        }\n        if (schema.format) {\n            schema.anyOf.push({\n                format: schema.format,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { format: schema.errorMessage.format },\n                }),\n            });\n            delete schema.format;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.format;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.anyOf.push({\n            format: value,\n            ...(message &&\n                refs.errorMessages && { errorMessage: { format: message } }),\n        });\n    }\n    else {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(schema, \"format\", value, message, refs);\n    }\n}\n// Adds a \"pattern\" keyword to the schema. If a pattern exists, both patterns will be joined in an allOf-node, along with subsequent ones.\nfunction addPattern(schema, regex, message, refs) {\n    if (schema.pattern || schema.allOf?.some((x) => x.pattern)) {\n        if (!schema.allOf) {\n            schema.allOf = [];\n        }\n        if (schema.pattern) {\n            schema.allOf.push({\n                pattern: schema.pattern,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { pattern: schema.errorMessage.pattern },\n                }),\n            });\n            delete schema.pattern;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.pattern;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.allOf.push({\n            pattern: stringifyRegExpWithFlags(regex, refs),\n            ...(message &&\n                refs.errorMessages && { errorMessage: { pattern: message } }),\n        });\n    }\n    else {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(schema, \"pattern\", stringifyRegExpWithFlags(regex, refs), message, refs);\n    }\n}\n// Mutate z.string.regex() in a best attempt to accommodate for regex flags when applyRegexFlags is true\nfunction stringifyRegExpWithFlags(regex, refs) {\n    if (!refs.applyRegexFlags || !regex.flags) {\n        return regex.source;\n    }\n    // Currently handled flags\n    const flags = {\n        i: regex.flags.includes(\"i\"),\n        m: regex.flags.includes(\"m\"),\n        s: regex.flags.includes(\"s\"), // `.` matches newlines\n    };\n    // The general principle here is to step through each character, one at a time, applying mutations as flags require. We keep track when the current character is escaped, and when it's inside a group /like [this]/ or (also) a range like /[a-z]/. The following is fairly brittle imperative code; edit at your peril!\n    const source = flags.i ? regex.source.toLowerCase() : regex.source;\n    let pattern = \"\";\n    let isEscaped = false;\n    let inCharGroup = false;\n    let inCharRange = false;\n    for (let i = 0; i < source.length; i++) {\n        if (isEscaped) {\n            pattern += source[i];\n            isEscaped = false;\n            continue;\n        }\n        if (flags.i) {\n            if (inCharGroup) {\n                if (source[i].match(/[a-z]/)) {\n                    if (inCharRange) {\n                        pattern += source[i];\n                        pattern += `${source[i - 2]}-${source[i]}`.toUpperCase();\n                        inCharRange = false;\n                    }\n                    else if (source[i + 1] === \"-\" && source[i + 2]?.match(/[a-z]/)) {\n                        pattern += source[i];\n                        inCharRange = true;\n                    }\n                    else {\n                        pattern += `${source[i]}${source[i].toUpperCase()}`;\n                    }\n                    continue;\n                }\n            }\n            else if (source[i].match(/[a-z]/)) {\n                pattern += `[${source[i]}${source[i].toUpperCase()}]`;\n                continue;\n            }\n        }\n        if (flags.m) {\n            if (source[i] === \"^\") {\n                pattern += `(^|(?<=[\\r\\n]))`;\n                continue;\n            }\n            else if (source[i] === \"$\") {\n                pattern += `($|(?=[\\r\\n]))`;\n                continue;\n            }\n        }\n        if (flags.s && source[i] === \".\") {\n            pattern += inCharGroup ? `${source[i]}\\r\\n` : `[${source[i]}\\r\\n]`;\n            continue;\n        }\n        pattern += source[i];\n        if (source[i] === \"\\\\\") {\n            isEscaped = true;\n        }\n        else if (inCharGroup && source[i] === \"]\") {\n            inCharGroup = false;\n        }\n        else if (!inCharGroup && source[i] === \"[\") {\n            inCharGroup = true;\n        }\n    }\n    try {\n        new RegExp(pattern);\n    }\n    catch {\n        console.warn(`Could not convert regex pattern at ${refs.currentPath.join(\"/\")} to a flag-independent form! Falling back to the flag-ignorant source`);\n        return regex.source;\n    }\n    return pattern;\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,QAAQ,WAAW,GAAG,KAAK;AACpD,MAAM;AACN,IAAI,aAAa;AACjB;;;;;CAKC,GACD,QAAQ,WAAW,GAAG;IAClB;;KAEC,GACD,MAAM;IACN,OAAO;IACP,MAAM;IACN;;KAEC,GACD,OAAO;IACP;;;;;;;;;;KAUC,GACD,OAAO;QACH,IAAI,eAAe,WAAW;YAC1B,aAAa,OAAO,wDAAwD;QAChF;QACA,OAAO;IACX;IACA;;KAEC,GACD,MAAM;IACN;;KAEC,GACD,MAAM;IACN,UAAU;IACV;;KAEC,GACD,MAAM;IACN,UAAU;IACV,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,KAAK;AACT;AACA,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,IAAI,MAAM,EAAE;QACZ,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;YAC5B,OAAQ,MAAM,IAAI;gBACd,KAAK;oBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WACvF,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WACvF,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBACD,OAAQ,KAAK,aAAa;wBACtB,KAAK;4BACD,UAAU,KAAK,SAAS,MAAM,OAAO,EAAE;4BACvC;wBACJ,KAAK;4BACD,UAAU,KAAK,aAAa,MAAM,OAAO,EAAE;4BAC3C;wBACJ,KAAK;4BACD,WAAW,KAAK,QAAQ,WAAW,CAAC,KAAK,EAAE,MAAM,OAAO,EAAE;4BAC1D;oBACR;oBACA;gBACJ,KAAK;oBACD,UAAU,KAAK,OAAO,MAAM,OAAO,EAAE;oBACrC;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,WAAW,KAAK,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC5C;gBACJ,KAAK;oBACD,WAAW,KAAK,QAAQ,WAAW,CAAC,IAAI,EAAE,MAAM,OAAO,EAAE;oBACzD;gBACJ,KAAK;oBACD,WAAW,KAAK,QAAQ,WAAW,CAAC,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC1D;gBACJ,KAAK;oBACD,WAAW,KAAK,OAAO,CAAC,CAAC,EAAE,wBAAwB,MAAM,KAAK,EAAE,OAAO,GAAG,MAAM,OAAO,EAAE;oBACzF;gBACJ,KAAK;oBACD,WAAW,KAAK,OAAO,GAAG,wBAAwB,MAAM,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,OAAO,EAAE;oBACzF;gBACJ,KAAK;oBACD,UAAU,KAAK,aAAa,MAAM,OAAO,EAAE;oBAC3C;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,UAAU,KAAK,YAAY,MAAM,OAAO,EAAE;oBAC1C;gBACJ,KAAK;oBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WACvF,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WACvF,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBAAY;wBACb,WAAW,KAAK,OAAO,wBAAwB,MAAM,KAAK,EAAE,QAAQ,MAAM,OAAO,EAAE;wBACnF;oBACJ;gBACA,KAAK;oBAAM;wBACP,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;wBAC1C;wBACA,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;wBAC1C;wBACA;oBACJ;gBACA,KAAK;oBACD,WAAW,KAAK,QAAQ,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,EAAE;oBAC9D;gBACJ,KAAK;oBACD,WAAW,KAAK,QAAQ,WAAW,CAAC,GAAG,EAAE,MAAM,OAAO,EAAE;oBACxD;gBACJ,KAAK;oBAAQ;wBACT,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,WAAW,KAAK,QAAQ,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;wBACjE;wBACA,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,WAAW,KAAK,QAAQ,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;wBACjE;wBACA;oBACJ;gBACA,KAAK;oBACD,WAAW,KAAK,QAAQ,WAAW,CAAC,KAAK,IAAI,MAAM,OAAO,EAAE;oBAC5D;gBACJ,KAAK;oBAAQ;wBACT,WAAW,KAAK,QAAQ,WAAW,CAAC,IAAI,EAAE,MAAM,OAAO,EAAE;wBACzD;oBACJ;gBACA,KAAK;oBAAU;wBACX,OAAQ,KAAK,cAAc;4BACvB,KAAK;gCAAiB;oCAClB,UAAU,KAAK,UAAU,MAAM,OAAO,EAAE;oCACxC;gCACJ;4BACA,KAAK;gCAA0B;oCAC3B,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,mBAAmB,UAAU,MAAM,OAAO,EAAE;oCACnG;gCACJ;4BACA,KAAK;gCAAe;oCAChB,WAAW,KAAK,QAAQ,WAAW,CAAC,MAAM,EAAE,MAAM,OAAO,EAAE;oCAC3D;gCACJ;wBACJ;wBACA;oBACJ;gBACA,KAAK;oBAAU;wBACX,WAAW,KAAK,QAAQ,WAAW,CAAC,MAAM,EAAE,MAAM,OAAO,EAAE;oBAC/D;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD;gBACJ;oBACI,kBAAkB,GAClB,CAAC,CAAC,KAAQ,CAAC,EAAE;YACrB;QACJ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,wBAAwB,OAAO,EAAE,IAAI;IAC1C,OAAO,KAAK,eAAe,KAAK,WAC1B,sBAAsB,WACtB;AACV;AACA,MAAM,gBAAgB,IAAI,IAAI;AAC9B,SAAS,sBAAsB,MAAM;IACjC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,IAAI,CAAC,cAAc,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/B,UAAU;QACd;QACA,UAAU,MAAM,CAAC,EAAE;IACvB;IACA,OAAO;AACX;AACA,uIAAuI;AACvI,SAAS,UAAU,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;IAC3C,IAAI,OAAO,MAAM,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,IAAM,EAAE,MAAM,GAAG;QACtD,IAAI,CAAC,OAAO,KAAK,EAAE;YACf,OAAO,KAAK,GAAG,EAAE;QACrB;QACA,IAAI,OAAO,MAAM,EAAE;YACf,OAAO,KAAK,CAAC,IAAI,CAAC;gBACd,QAAQ,OAAO,MAAM;gBACrB,GAAI,OAAO,YAAY,IACnB,KAAK,aAAa,IAAI;oBACtB,cAAc;wBAAE,QAAQ,OAAO,YAAY,CAAC,MAAM;oBAAC;gBACvD,CAAC;YACL;YACA,OAAO,OAAO,MAAM;YACpB,IAAI,OAAO,YAAY,EAAE;gBACrB,OAAO,OAAO,YAAY,CAAC,MAAM;gBACjC,IAAI,OAAO,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,KAAK,GAAG;oBAC/C,OAAO,OAAO,YAAY;gBAC9B;YACJ;QACJ;QACA,OAAO,KAAK,CAAC,IAAI,CAAC;YACd,QAAQ;YACR,GAAI,WACA,KAAK,aAAa,IAAI;gBAAE,cAAc;oBAAE,QAAQ;gBAAQ;YAAE,CAAC;QACnE;IACJ,OACK;QACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,QAAQ,UAAU,OAAO,SAAS;IACxF;AACJ;AACA,0IAA0I;AAC1I,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;IAC5C,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,IAAM,EAAE,OAAO,GAAG;QACxD,IAAI,CAAC,OAAO,KAAK,EAAE;YACf,OAAO,KAAK,GAAG,EAAE;QACrB;QACA,IAAI,OAAO,OAAO,EAAE;YAChB,OAAO,KAAK,CAAC,IAAI,CAAC;gBACd,SAAS,OAAO,OAAO;gBACvB,GAAI,OAAO,YAAY,IACnB,KAAK,aAAa,IAAI;oBACtB,cAAc;wBAAE,SAAS,OAAO,YAAY,CAAC,OAAO;oBAAC;gBACzD,CAAC;YACL;YACA,OAAO,OAAO,OAAO;YACrB,IAAI,OAAO,YAAY,EAAE;gBACrB,OAAO,OAAO,YAAY,CAAC,OAAO;gBAClC,IAAI,OAAO,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,KAAK,GAAG;oBAC/C,OAAO,OAAO,YAAY;gBAC9B;YACJ;QACJ;QACA,OAAO,KAAK,CAAC,IAAI,CAAC;YACd,SAAS,yBAAyB,OAAO;YACzC,GAAI,WACA,KAAK,aAAa,IAAI;gBAAE,cAAc;oBAAE,SAAS;gBAAQ;YAAE,CAAC;QACpE;IACJ,OACK;QACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,QAAQ,WAAW,yBAAyB,OAAO,OAAO,SAAS;IACzH;AACJ;AACA,wGAAwG;AACxG,SAAS,yBAAyB,KAAK,EAAE,IAAI;IACzC,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,MAAM,KAAK,EAAE;QACvC,OAAO,MAAM,MAAM;IACvB;IACA,0BAA0B;IAC1B,MAAM,QAAQ;QACV,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;QACxB,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;QACxB,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;IAC5B;IACA,yTAAyT;IACzT,MAAM,SAAS,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,KAAK,MAAM,MAAM;IAClE,IAAI,UAAU;IACd,IAAI,YAAY;IAChB,IAAI,cAAc;IAClB,IAAI,cAAc;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,IAAI,WAAW;YACX,WAAW,MAAM,CAAC,EAAE;YACpB,YAAY;YACZ;QACJ;QACA,IAAI,MAAM,CAAC,EAAE;YACT,IAAI,aAAa;gBACb,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU;oBAC1B,IAAI,aAAa;wBACb,WAAW,MAAM,CAAC,EAAE;wBACpB,WAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,WAAW;wBACtD,cAAc;oBAClB,OACK,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,UAAU;wBAC7D,WAAW,MAAM,CAAC,EAAE;wBACpB,cAAc;oBAClB,OACK;wBACD,WAAW,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI;oBACvD;oBACA;gBACJ;YACJ,OACK,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU;gBAC/B,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC;gBACrD;YACJ;QACJ;QACA,IAAI,MAAM,CAAC,EAAE;YACT,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;gBACnB,WAAW,CAAC,eAAe,CAAC;gBAC5B;YACJ,OACK,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;gBACxB,WAAW,CAAC,cAAc,CAAC;gBAC3B;YACJ;QACJ;QACA,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;YAC9B,WAAW,cAAc,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;YAClE;QACJ;QACA,WAAW,MAAM,CAAC,EAAE;QACpB,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;YACpB,YAAY;QAChB,OACK,IAAI,eAAe,MAAM,CAAC,EAAE,KAAK,KAAK;YACvC,cAAc;QAClB,OACK,IAAI,CAAC,eAAe,MAAM,CAAC,EAAE,KAAK,KAAK;YACxC,cAAc;QAClB;IACJ;IACA,IAAI;QACA,IAAI,OAAO;IACf,EACA,OAAM;QACF,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,qEAAqE,CAAC;QACpJ,OAAO,MAAM,MAAM;IACvB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/record.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseRecordDef = void 0;\nconst zod_1 = require(\"zod\");\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst string_js_1 = require(\"./string.js\");\nconst branded_js_1 = require(\"./branded.js\");\nconst any_js_1 = require(\"./any.js\");\nfunction parseRecordDef(def, refs) {\n    if (refs.target === \"openAi\") {\n        console.warn(\"Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.\");\n    }\n    if (refs.target === \"openApi3\" &&\n        def.keyType?._def.typeName === zod_1.ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            type: \"object\",\n            required: def.keyType._def.values,\n            properties: def.keyType._def.values.reduce((acc, key) => ({\n                ...acc,\n                [key]: (0, parseDef_js_1.parseDef)(def.valueType._def, {\n                    ...refs,\n                    currentPath: [...refs.currentPath, \"properties\", key],\n                }) ?? (0, any_js_1.parseAnyDef)(refs),\n            }), {}),\n            additionalProperties: refs.rejectedAdditionalProperties,\n        };\n    }\n    const schema = {\n        type: \"object\",\n        additionalProperties: (0, parseDef_js_1.parseDef)(def.valueType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        }) ?? refs.allowedAdditionalProperties,\n    };\n    if (refs.target === \"openApi3\") {\n        return schema;\n    }\n    if (def.keyType?._def.typeName === zod_1.ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.checks?.length) {\n        const { type, ...keyType } = (0, string_js_1.parseStringDef)(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    else if (def.keyType?._def.typeName === zod_1.ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            ...schema,\n            propertyNames: {\n                enum: def.keyType._def.values,\n            },\n        };\n    }\n    else if (def.keyType?._def.typeName === zod_1.ZodFirstPartyTypeKind.ZodBranded &&\n        def.keyType._def.type._def.typeName === zod_1.ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.type._def.checks?.length) {\n        const { type, ...keyType } = (0, branded_js_1.parseBrandedDef)(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    return schema;\n}\nexports.parseRecordDef = parseRecordDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,IAAI,KAAK,MAAM,KAAK,UAAU;QAC1B,QAAQ,IAAI,CAAC;IACjB;IACA,IAAI,KAAK,MAAM,KAAK,cAChB,IAAI,OAAO,EAAE,KAAK,aAAa,MAAM,qBAAqB,CAAC,OAAO,EAAE;QACpE,OAAO;YACH,MAAM;YACN,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,CAAC;oBACtD,GAAG,GAAG;oBACN,CAAC,IAAI,EAAE,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;wBACnD,GAAG,IAAI;wBACP,aAAa;+BAAI,KAAK,WAAW;4BAAE;4BAAc;yBAAI;oBACzD,MAAM,CAAC,GAAG,SAAS,WAAW,EAAE;gBACpC,CAAC,GAAG,CAAC;YACL,sBAAsB,KAAK,4BAA4B;QAC3D;IACJ;IACA,MAAM,SAAS;QACX,MAAM;QACN,sBAAsB,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;YAClE,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAuB;QAC9D,MAAM,KAAK,2BAA2B;IAC1C;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;IACX;IACA,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,MAAM,qBAAqB,CAAC,SAAS,IACpE,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;QACjC,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,cAAc,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QAC/E,OAAO;YACH,GAAG,MAAM;YACT,eAAe;QACnB;IACJ,OACK,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,MAAM,qBAAqB,CAAC,OAAO,EAAE;QACzE,OAAO;YACH,GAAG,MAAM;YACT,eAAe;gBACX,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC;QACJ;IACJ,OACK,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,MAAM,qBAAqB,CAAC,UAAU,IAC1E,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,qBAAqB,CAAC,SAAS,IAC7E,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,GAAG,CAAC,GAAG,aAAa,eAAe,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QACjF,OAAO;YACH,GAAG,MAAM;YACT,eAAe;QACnB;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/map.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseMapDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst record_js_1 = require(\"./record.js\");\nconst any_js_1 = require(\"./any.js\");\nfunction parseMapDef(def, refs) {\n    if (refs.mapStrategy === \"record\") {\n        return (0, record_js_1.parseRecordDef)(def, refs);\n    }\n    const keys = (0, parseDef_js_1.parseDef)(def.keyType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"0\"],\n    }) || (0, any_js_1.parseAnyDef)(refs);\n    const values = (0, parseDef_js_1.parseDef)(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"1\"],\n    }) || (0, any_js_1.parseAnyDef)(refs);\n    return {\n        type: \"array\",\n        maxItems: 125,\n        items: {\n            type: \"array\",\n            items: [keys, values],\n            minItems: 2,\n            maxItems: 2,\n        },\n    };\n}\nexports.parseMapDef = parseMapDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,YAAY,GAAG,EAAE,IAAI;IAC1B,IAAI,KAAK,WAAW,KAAK,UAAU;QAC/B,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;IAChD;IACA,MAAM,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QACvD,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;YAAS;SAAI;IAC7D,MAAM,CAAC,GAAG,SAAS,WAAW,EAAE;IAChC,MAAM,SAAS,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QAC3D,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;YAAS;SAAI;IAC7D,MAAM,CAAC,GAAG,SAAS,WAAW,EAAE;IAChC,OAAO;QACH,MAAM;QACN,UAAU;QACV,OAAO;YACH,MAAM;YACN,OAAO;gBAAC;gBAAM;aAAO;YACrB,UAAU;YACV,UAAU;QACd;IACJ;AACJ;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/nativeEnum.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNativeEnumDef = void 0;\nfunction parseNativeEnumDef(def) {\n    const object = def.values;\n    const actualKeys = Object.keys(def.values).filter((key) => {\n        return typeof object[object[key]] !== \"number\";\n    });\n    const actualValues = actualKeys.map((key) => object[key]);\n    const parsedTypes = Array.from(new Set(actualValues.map((values) => typeof values)));\n    return {\n        type: parsedTypes.length === 1\n            ? parsedTypes[0] === \"string\"\n                ? \"string\"\n                : \"number\"\n            : [\"string\", \"number\"],\n        enum: actualValues,\n    };\n}\nexports.parseNativeEnumDef = parseNativeEnumDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,SAAS,mBAAmB,GAAG;IAC3B,MAAM,SAAS,IAAI,MAAM;IACzB,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/C,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;IAC1C;IACA,MAAM,eAAe,WAAW,GAAG,CAAC,CAAC,MAAQ,MAAM,CAAC,IAAI;IACxD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,SAAW,OAAO;IAC3E,OAAO;QACH,MAAM,YAAY,MAAM,KAAK,IACvB,WAAW,CAAC,EAAE,KAAK,WACf,WACA,WACJ;YAAC;YAAU;SAAS;QAC1B,MAAM;IACV;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/never.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNeverDef = void 0;\nconst any_js_1 = require(\"./any.js\");\nfunction parseNeverDef(refs) {\n    return refs.target === \"openAi\"\n        ? undefined\n        : {\n            not: (0, any_js_1.parseAnyDef)({\n                ...refs,\n                currentPath: [...refs.currentPath, \"not\"],\n            }),\n        };\n}\nexports.parseNeverDef = parseNeverDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,SAAS,cAAc,IAAI;IACvB,OAAO,KAAK,MAAM,KAAK,WACjB,YACA;QACE,KAAK,CAAC,GAAG,SAAS,WAAW,EAAE;YAC3B,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAM;QAC7C;IACJ;AACR;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/null.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNullDef = void 0;\nfunction parseNullDef(refs) {\n    return refs.target === \"openApi3\"\n        ? {\n            enum: [\"null\"],\n            nullable: true,\n        }\n        : {\n            type: \"null\",\n        };\n}\nexports.parseNullDef = parseNullDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,KAAK;AAC5B,SAAS,aAAa,IAAI;IACtB,OAAO,KAAK,MAAM,KAAK,aACjB;QACE,MAAM;YAAC;SAAO;QACd,UAAU;IACd,IACE;QACE,MAAM;IACV;AACR;AACA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/union.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseUnionDef = exports.primitiveMappings = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nexports.primitiveMappings = {\n    ZodString: \"string\",\n    ZodNumber: \"number\",\n    ZodBigInt: \"integer\",\n    ZodBoolean: \"boolean\",\n    ZodNull: \"null\",\n};\nfunction parseUnionDef(def, refs) {\n    if (refs.target === \"openApi3\")\n        return asAnyOf(def, refs);\n    const options = def.options instanceof Map ? Array.from(def.options.values()) : def.options;\n    // This blocks tries to look ahead a bit to produce nicer looking schemas with type array instead of anyOf.\n    if (options.every((x) => x._def.typeName in exports.primitiveMappings &&\n        (!x._def.checks || !x._def.checks.length))) {\n        // all types in union are primitive and lack checks, so might as well squash into {type: [...]}\n        const types = options.reduce((types, x) => {\n            const type = exports.primitiveMappings[x._def.typeName]; //Can be safely casted due to row 43\n            return type && !types.includes(type) ? [...types, type] : types;\n        }, []);\n        return {\n            type: types.length > 1 ? types : types[0],\n        };\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodLiteral\" && !x.description)) {\n        // all options literals\n        const types = options.reduce((acc, x) => {\n            const type = typeof x._def.value;\n            switch (type) {\n                case \"string\":\n                case \"number\":\n                case \"boolean\":\n                    return [...acc, type];\n                case \"bigint\":\n                    return [...acc, \"integer\"];\n                case \"object\":\n                    if (x._def.value === null)\n                        return [...acc, \"null\"];\n                case \"symbol\":\n                case \"undefined\":\n                case \"function\":\n                default:\n                    return acc;\n            }\n        }, []);\n        if (types.length === options.length) {\n            // all the literals are primitive, as far as null can be considered primitive\n            const uniqueTypes = types.filter((x, i, a) => a.indexOf(x) === i);\n            return {\n                type: uniqueTypes.length > 1 ? uniqueTypes : uniqueTypes[0],\n                enum: options.reduce((acc, x) => {\n                    return acc.includes(x._def.value) ? acc : [...acc, x._def.value];\n                }, []),\n            };\n        }\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodEnum\")) {\n        return {\n            type: \"string\",\n            enum: options.reduce((acc, x) => [\n                ...acc,\n                ...x._def.values.filter((x) => !acc.includes(x)),\n            ], []),\n        };\n    }\n    return asAnyOf(def, refs);\n}\nexports.parseUnionDef = parseUnionDef;\nconst asAnyOf = (def, refs) => {\n    const anyOf = (def.options instanceof Map\n        ? Array.from(def.options.values())\n        : def.options)\n        .map((x, i) => (0, parseDef_js_1.parseDef)(x._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", `${i}`],\n    }))\n        .filter((x) => !!x &&\n        (!refs.strictUnions ||\n            (typeof x === \"object\" && Object.keys(x).length > 0)));\n    return anyOf.length ? { anyOf } : undefined;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,QAAQ,iBAAiB,GAAG,KAAK;AACzD,MAAM;AACN,QAAQ,iBAAiB,GAAG;IACxB,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;AACb;AACA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,KAAK,MAAM,KAAK,YAChB,OAAO,QAAQ,KAAK;IACxB,MAAM,UAAU,IAAI,OAAO,YAAY,MAAM,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,IAAI,OAAO;IAC3F,2GAA2G;IAC3G,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,iBAAiB,IACjE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI;QAC5C,+FAA+F;QAC/F,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC,OAAO;YACjC,MAAM,OAAO,QAAQ,iBAAiB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,oCAAoC;YAC7F,OAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;mBAAI;gBAAO;aAAK,GAAG;QAC9D,GAAG,EAAE;QACL,OAAO;YACH,MAAM,MAAM,MAAM,GAAG,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC7C;IACJ,OACK,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,gBAAgB,CAAC,EAAE,WAAW,GAAG;QAC/E,uBAAuB;QACvB,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC,KAAK;YAC/B,MAAM,OAAO,OAAO,EAAE,IAAI,CAAC,KAAK;YAChC,OAAQ;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,OAAO;2BAAI;wBAAK;qBAAK;gBACzB,KAAK;oBACD,OAAO;2BAAI;wBAAK;qBAAU;gBAC9B,KAAK;oBACD,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,MACjB,OAAO;2BAAI;wBAAK;qBAAO;gBAC/B,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL;oBACI,OAAO;YACf;QACJ,GAAG,EAAE;QACL,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;YACjC,6EAA6E;YAC7E,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,GAAG,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO;YAC/D,OAAO;gBACH,MAAM,YAAY,MAAM,GAAG,IAAI,cAAc,WAAW,CAAC,EAAE;gBAC3D,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK;oBACvB,OAAO,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,MAAM;2BAAI;wBAAK,EAAE,IAAI,CAAC,KAAK;qBAAC;gBACpE,GAAG,EAAE;YACT;QACJ;IACJ,OACK,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,YAAY;QAC1D,OAAO;YACH,MAAM;YACN,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM;uBAC1B;uBACA,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,IAAI,QAAQ,CAAC;iBAChD,EAAE,EAAE;QACT;IACJ;IACA,OAAO,QAAQ,KAAK;AACxB;AACA,QAAQ,aAAa,GAAG;AACxB,MAAM,UAAU,CAAC,KAAK;IAClB,MAAM,QAAQ,CAAC,IAAI,OAAO,YAAY,MAChC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,MAC7B,IAAI,OAAO,EACZ,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,cAAc,QAAQ,EAAE,EAAE,IAAI,EAAE;YACnD,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS,GAAG,GAAG;aAAC;QACvD,IACK,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,KACjB,CAAC,CAAC,KAAK,YAAY,IACd,OAAO,MAAM,YAAY,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAE;IAC5D,OAAO,MAAM,MAAM,GAAG;QAAE;IAAM,IAAI;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/nullable.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNullableDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst union_js_1 = require(\"./union.js\");\nfunction parseNullableDef(def, refs) {\n    if ([\"ZodString\", \"ZodNumber\", \"ZodBigInt\", \"ZodBoolean\", \"ZodNull\"].includes(def.innerType._def.typeName) &&\n        (!def.innerType._def.checks || !def.innerType._def.checks.length)) {\n        if (refs.target === \"openApi3\") {\n            return {\n                type: union_js_1.primitiveMappings[def.innerType._def.typeName],\n                nullable: true,\n            };\n        }\n        return {\n            type: [\n                union_js_1.primitiveMappings[def.innerType._def.typeName],\n                \"null\",\n            ],\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        const base = (0, parseDef_js_1.parseDef)(def.innerType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath],\n        });\n        if (base && \"$ref\" in base)\n            return { allOf: [base], nullable: true };\n        return base && { ...base, nullable: true };\n    }\n    const base = (0, parseDef_js_1.parseDef)(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"0\"],\n    });\n    return base && { anyOf: [base, { type: \"null\" }] };\n}\nexports.parseNullableDef = parseNullableDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,SAAS,iBAAiB,GAAG,EAAE,IAAI;IAC/B,IAAI;QAAC;QAAa;QAAa;QAAa;QAAc;KAAU,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,KACrG,CAAC,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACnE,IAAI,KAAK,MAAM,KAAK,YAAY;YAC5B,OAAO;gBACH,MAAM,WAAW,iBAAiB,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC/D,UAAU;YACd;QACJ;QACA,OAAO;YACH,MAAM;gBACF,WAAW,iBAAiB,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACzD;aACH;QACL;IACJ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,MAAM,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;YACzD,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;aAAC;QACtC;QACA,IAAI,QAAQ,UAAU,MAClB,OAAO;YAAE,OAAO;gBAAC;aAAK;YAAE,UAAU;QAAK;QAC3C,OAAO,QAAQ;YAAE,GAAG,IAAI;YAAE,UAAU;QAAK;IAC7C;IACA,MAAM,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QACzD,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,OAAO,QAAQ;QAAE,OAAO;YAAC;YAAM;gBAAE,MAAM;YAAO;SAAE;IAAC;AACrD;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/number.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNumberDef = void 0;\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nfunction parseNumberDef(def, refs) {\n    const res = {\n        type: \"number\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"int\":\n                res.type = \"integer\";\n                (0, errorMessages_js_1.addErrorMessage)(res, \"type\", check.message, refs);\n                break;\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                (0, errorMessages_js_1.setResponseValueAndErrors)(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\nexports.parseNumberDef = parseNumberDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,CAAC,IAAI,MAAM,EACX,OAAO;IACX,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAI,IAAI,GAAG;gBACX,CAAC,GAAG,mBAAmB,eAAe,EAAE,KAAK,QAAQ,MAAM,OAAO,EAAE;gBACpE;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClG,OACK;wBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC3G;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAClG;gBACA;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClG,OACK;wBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC3G;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAClG;gBACA;YACJ,KAAK;gBACD,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,KAAK,cAAc,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBACjG;QACR;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/object.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseObjectDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseObjectDef(def, refs) {\n    const forceOptionalIntoNullable = refs.target === \"openAi\";\n    const result = {\n        type: \"object\",\n        properties: {},\n    };\n    const required = [];\n    const shape = def.shape();\n    for (const propName in shape) {\n        let propDef = shape[propName];\n        if (propDef === undefined || propDef._def === undefined) {\n            continue;\n        }\n        let propOptional = safeIsOptional(propDef);\n        if (propOptional && forceOptionalIntoNullable) {\n            if (propDef._def.typeName === \"ZodOptional\") {\n                propDef = propDef._def.innerType;\n            }\n            if (!propDef.isNullable()) {\n                propDef = propDef.nullable();\n            }\n            propOptional = false;\n        }\n        const parsedDef = (0, parseDef_js_1.parseDef)(propDef._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"properties\", propName],\n            propertyPath: [...refs.currentPath, \"properties\", propName],\n        });\n        if (parsedDef === undefined) {\n            continue;\n        }\n        result.properties[propName] = parsedDef;\n        if (!propOptional) {\n            required.push(propName);\n        }\n    }\n    if (required.length) {\n        result.required = required;\n    }\n    const additionalProperties = decideAdditionalProperties(def, refs);\n    if (additionalProperties !== undefined) {\n        result.additionalProperties = additionalProperties;\n    }\n    return result;\n}\nexports.parseObjectDef = parseObjectDef;\nfunction decideAdditionalProperties(def, refs) {\n    if (def.catchall._def.typeName !== \"ZodNever\") {\n        return (0, parseDef_js_1.parseDef)(def.catchall._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        });\n    }\n    switch (def.unknownKeys) {\n        case \"passthrough\":\n            return refs.allowedAdditionalProperties;\n        case \"strict\":\n            return refs.rejectedAdditionalProperties;\n        case \"strip\":\n            return refs.removeAdditionalStrategy === \"strict\"\n                ? refs.allowedAdditionalProperties\n                : refs.rejectedAdditionalProperties;\n    }\n}\nfunction safeIsOptional(schema) {\n    try {\n        return schema.isOptional();\n    }\n    catch {\n        return true;\n    }\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,SAAS,eAAe,GAAG,EAAE,IAAI;IAC7B,MAAM,4BAA4B,KAAK,MAAM,KAAK;IAClD,MAAM,SAAS;QACX,MAAM;QACN,YAAY,CAAC;IACjB;IACA,MAAM,WAAW,EAAE;IACnB,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAK,MAAM,YAAY,MAAO;QAC1B,IAAI,UAAU,KAAK,CAAC,SAAS;QAC7B,IAAI,YAAY,aAAa,QAAQ,IAAI,KAAK,WAAW;YACrD;QACJ;QACA,IAAI,eAAe,eAAe;QAClC,IAAI,gBAAgB,2BAA2B;YAC3C,IAAI,QAAQ,IAAI,CAAC,QAAQ,KAAK,eAAe;gBACzC,UAAU,QAAQ,IAAI,CAAC,SAAS;YACpC;YACA,IAAI,CAAC,QAAQ,UAAU,IAAI;gBACvB,UAAU,QAAQ,QAAQ;YAC9B;YACA,eAAe;QACnB;QACA,MAAM,YAAY,CAAC,GAAG,cAAc,QAAQ,EAAE,QAAQ,IAAI,EAAE;YACxD,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAc;aAAS;YAC1D,cAAc;mBAAI,KAAK,WAAW;gBAAE;gBAAc;aAAS;QAC/D;QACA,IAAI,cAAc,WAAW;YACzB;QACJ;QACA,OAAO,UAAU,CAAC,SAAS,GAAG;QAC9B,IAAI,CAAC,cAAc;YACf,SAAS,IAAI,CAAC;QAClB;IACJ;IACA,IAAI,SAAS,MAAM,EAAE;QACjB,OAAO,QAAQ,GAAG;IACtB;IACA,MAAM,uBAAuB,2BAA2B,KAAK;IAC7D,IAAI,yBAAyB,WAAW;QACpC,OAAO,oBAAoB,GAAG;IAClC;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,2BAA2B,GAAG,EAAE,IAAI;IACzC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,KAAK,YAAY;QAC3C,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE;YAClD,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAuB;QAC9D;IACJ;IACA,OAAQ,IAAI,WAAW;QACnB,KAAK;YACD,OAAO,KAAK,2BAA2B;QAC3C,KAAK;YACD,OAAO,KAAK,4BAA4B;QAC5C,KAAK;YACD,OAAO,KAAK,wBAAwB,KAAK,WACnC,KAAK,2BAA2B,GAChC,KAAK,4BAA4B;IAC/C;AACJ;AACA,SAAS,eAAe,MAAM;IAC1B,IAAI;QACA,OAAO,OAAO,UAAU;IAC5B,EACA,OAAM;QACF,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/optional.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseOptionalDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst any_js_1 = require(\"./any.js\");\nconst parseOptionalDef = (def, refs) => {\n    if (refs.currentPath.toString() === refs.propertyPath?.toString()) {\n        return (0, parseDef_js_1.parseDef)(def.innerType._def, refs);\n    }\n    const innerSchema = (0, parseDef_js_1.parseDef)(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"1\"],\n    });\n    return innerSchema\n        ? {\n            anyOf: [\n                {\n                    not: (0, any_js_1.parseAnyDef)(refs),\n                },\n                innerSchema,\n            ],\n        }\n        : (0, any_js_1.parseAnyDef)(refs);\n};\nexports.parseOptionalDef = parseOptionalDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,MAAM,mBAAmB,CAAC,KAAK;IAC3B,IAAI,KAAK,WAAW,CAAC,QAAQ,OAAO,KAAK,YAAY,EAAE,YAAY;QAC/D,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;IAC3D;IACA,MAAM,cAAc,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QAChE,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,OAAO,cACD;QACE,OAAO;YACH;gBACI,KAAK,CAAC,GAAG,SAAS,WAAW,EAAE;YACnC;YACA;SACH;IACL,IACE,CAAC,GAAG,SAAS,WAAW,EAAE;AACpC;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/pipeline.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parsePipelineDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst parsePipelineDef = (def, refs) => {\n    if (refs.pipeStrategy === \"input\") {\n        return (0, parseDef_js_1.parseDef)(def.in._def, refs);\n    }\n    else if (refs.pipeStrategy === \"output\") {\n        return (0, parseDef_js_1.parseDef)(def.out._def, refs);\n    }\n    const a = (0, parseDef_js_1.parseDef)(def.in._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n    });\n    const b = (0, parseDef_js_1.parseDef)(def.out._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", a ? \"1\" : \"0\"],\n    });\n    return {\n        allOf: [a, b].filter((x) => x !== undefined),\n    };\n};\nexports.parsePipelineDef = parsePipelineDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM,mBAAmB,CAAC,KAAK;IAC3B,IAAI,KAAK,YAAY,KAAK,SAAS;QAC/B,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE;IACpD,OACK,IAAI,KAAK,YAAY,KAAK,UAAU;QACrC,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;IACrD;IACA,MAAM,IAAI,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE;QAC/C,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,MAAM,IAAI,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;QAChD,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS,IAAI,MAAM;SAAI;IAC9D;IACA,OAAO;QACH,OAAO;YAAC;YAAG;SAAE,CAAC,MAAM,CAAC,CAAC,IAAM,MAAM;IACtC;AACJ;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/promise.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parsePromiseDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parsePromiseDef(def, refs) {\n    return (0, parseDef_js_1.parseDef)(def.type._def, refs);\n}\nexports.parsePromiseDef = parsePromiseDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,SAAS,gBAAgB,GAAG,EAAE,IAAI;IAC9B,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACtD;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/set.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseSetDef = void 0;\nconst errorMessages_js_1 = require(\"../errorMessages.js\");\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseSetDef(def, refs) {\n    const items = (0, parseDef_js_1.parseDef)(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\"],\n    });\n    const schema = {\n        type: \"array\",\n        uniqueItems: true,\n        items,\n    };\n    if (def.minSize) {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(schema, \"minItems\", def.minSize.value, def.minSize.message, refs);\n    }\n    if (def.maxSize) {\n        (0, errorMessages_js_1.setResponseValueAndErrors)(schema, \"maxItems\", def.maxSize.value, def.maxSize.message, refs);\n    }\n    return schema;\n}\nexports.parseSetDef = parseSetDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,SAAS,YAAY,GAAG,EAAE,IAAI;IAC1B,MAAM,QAAQ,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QAC1D,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;SAAQ;IAC/C;IACA,MAAM,SAAS;QACX,MAAM;QACN,aAAa;QACb;IACJ;IACA,IAAI,IAAI,OAAO,EAAE;QACb,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;IAClH;IACA,IAAI,IAAI,OAAO,EAAE;QACb,CAAC,GAAG,mBAAmB,yBAAyB,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;IAClH;IACA,OAAO;AACX;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/tuple.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseTupleDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nfunction parseTupleDef(def, refs) {\n    if (def.rest) {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            items: def.items\n                .map((x, i) => (0, parseDef_js_1.parseDef)(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n            additionalItems: (0, parseDef_js_1.parseDef)(def.rest._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"additionalItems\"],\n            }),\n        };\n    }\n    else {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            maxItems: def.items.length,\n            items: def.items\n                .map((x, i) => (0, parseDef_js_1.parseDef)(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n        };\n    }\n}\nexports.parseTupleDef = parseTupleDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,IAAI,EAAE;QACV,OAAO;YACH,MAAM;YACN,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,OAAO,IAAI,KAAK,CACX,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,cAAc,QAAQ,EAAE,EAAE,IAAI,EAAE;oBACnD,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;wBAAE;wBAAS,GAAG,GAAG;qBAAC;gBACvD,IACK,MAAM,CAAC,CAAC,KAAK,IAAO,MAAM,YAAY,MAAM;uBAAI;oBAAK;iBAAE,EAAG,EAAE;YACjE,iBAAiB,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;gBACxD,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,WAAW;oBAAE;iBAAkB;YACzD;QACJ;IACJ,OACK;QACD,OAAO;YACH,MAAM;YACN,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,OAAO,IAAI,KAAK,CACX,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,cAAc,QAAQ,EAAE,EAAE,IAAI,EAAE;oBACnD,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;wBAAE;wBAAS,GAAG,GAAG;qBAAC;gBACvD,IACK,MAAM,CAAC,CAAC,KAAK,IAAO,MAAM,YAAY,MAAM;uBAAI;oBAAK;iBAAE,EAAG,EAAE;QACrE;IACJ;AACJ;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/undefined.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseUndefinedDef = void 0;\nconst any_js_1 = require(\"./any.js\");\nfunction parseUndefinedDef(refs) {\n    return {\n        not: (0, any_js_1.parseAnyDef)(refs),\n    };\n}\nexports.parseUndefinedDef = parseUndefinedDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,KAAK;AACjC,MAAM;AACN,SAAS,kBAAkB,IAAI;IAC3B,OAAO;QACH,KAAK,CAAC,GAAG,SAAS,WAAW,EAAE;IACnC;AACJ;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/unknown.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseUnknownDef = void 0;\nconst any_js_1 = require(\"./any.js\");\nfunction parseUnknownDef(refs) {\n    return (0, any_js_1.parseAnyDef)(refs);\n}\nexports.parseUnknownDef = parseUnknownDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,SAAS,gBAAgB,IAAI;IACzB,OAAO,CAAC,GAAG,SAAS,WAAW,EAAE;AACrC;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parsers/readonly.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseReadonlyDef = void 0;\nconst parseDef_js_1 = require(\"../parseDef.js\");\nconst parseReadonlyDef = (def, refs) => {\n    return (0, parseDef_js_1.parseDef)(def.innerType._def, refs);\n};\nexports.parseReadonlyDef = parseReadonlyDef;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM,mBAAmB,CAAC,KAAK;IAC3B,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;AAC3D;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/selectParser.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.selectParser = void 0;\nconst zod_1 = require(\"zod\");\nconst any_js_1 = require(\"./parsers/any.js\");\nconst array_js_1 = require(\"./parsers/array.js\");\nconst bigint_js_1 = require(\"./parsers/bigint.js\");\nconst boolean_js_1 = require(\"./parsers/boolean.js\");\nconst branded_js_1 = require(\"./parsers/branded.js\");\nconst catch_js_1 = require(\"./parsers/catch.js\");\nconst date_js_1 = require(\"./parsers/date.js\");\nconst default_js_1 = require(\"./parsers/default.js\");\nconst effects_js_1 = require(\"./parsers/effects.js\");\nconst enum_js_1 = require(\"./parsers/enum.js\");\nconst intersection_js_1 = require(\"./parsers/intersection.js\");\nconst literal_js_1 = require(\"./parsers/literal.js\");\nconst map_js_1 = require(\"./parsers/map.js\");\nconst nativeEnum_js_1 = require(\"./parsers/nativeEnum.js\");\nconst never_js_1 = require(\"./parsers/never.js\");\nconst null_js_1 = require(\"./parsers/null.js\");\nconst nullable_js_1 = require(\"./parsers/nullable.js\");\nconst number_js_1 = require(\"./parsers/number.js\");\nconst object_js_1 = require(\"./parsers/object.js\");\nconst optional_js_1 = require(\"./parsers/optional.js\");\nconst pipeline_js_1 = require(\"./parsers/pipeline.js\");\nconst promise_js_1 = require(\"./parsers/promise.js\");\nconst record_js_1 = require(\"./parsers/record.js\");\nconst set_js_1 = require(\"./parsers/set.js\");\nconst string_js_1 = require(\"./parsers/string.js\");\nconst tuple_js_1 = require(\"./parsers/tuple.js\");\nconst undefined_js_1 = require(\"./parsers/undefined.js\");\nconst union_js_1 = require(\"./parsers/union.js\");\nconst unknown_js_1 = require(\"./parsers/unknown.js\");\nconst readonly_js_1 = require(\"./parsers/readonly.js\");\nconst selectParser = (def, typeName, refs) => {\n    switch (typeName) {\n        case zod_1.ZodFirstPartyTypeKind.ZodString:\n            return (0, string_js_1.parseStringDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodNumber:\n            return (0, number_js_1.parseNumberDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodObject:\n            return (0, object_js_1.parseObjectDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodBigInt:\n            return (0, bigint_js_1.parseBigintDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodBoolean:\n            return (0, boolean_js_1.parseBooleanDef)();\n        case zod_1.ZodFirstPartyTypeKind.ZodDate:\n            return (0, date_js_1.parseDateDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodUndefined:\n            return (0, undefined_js_1.parseUndefinedDef)(refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodNull:\n            return (0, null_js_1.parseNullDef)(refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodArray:\n            return (0, array_js_1.parseArrayDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodUnion:\n        case zod_1.ZodFirstPartyTypeKind.ZodDiscriminatedUnion:\n            return (0, union_js_1.parseUnionDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodIntersection:\n            return (0, intersection_js_1.parseIntersectionDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodTuple:\n            return (0, tuple_js_1.parseTupleDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodRecord:\n            return (0, record_js_1.parseRecordDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodLiteral:\n            return (0, literal_js_1.parseLiteralDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodEnum:\n            return (0, enum_js_1.parseEnumDef)(def);\n        case zod_1.ZodFirstPartyTypeKind.ZodNativeEnum:\n            return (0, nativeEnum_js_1.parseNativeEnumDef)(def);\n        case zod_1.ZodFirstPartyTypeKind.ZodNullable:\n            return (0, nullable_js_1.parseNullableDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodOptional:\n            return (0, optional_js_1.parseOptionalDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodMap:\n            return (0, map_js_1.parseMapDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodSet:\n            return (0, set_js_1.parseSetDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodLazy:\n            return () => def.getter()._def;\n        case zod_1.ZodFirstPartyTypeKind.ZodPromise:\n            return (0, promise_js_1.parsePromiseDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodNaN:\n        case zod_1.ZodFirstPartyTypeKind.ZodNever:\n            return (0, never_js_1.parseNeverDef)(refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodEffects:\n            return (0, effects_js_1.parseEffectsDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodAny:\n            return (0, any_js_1.parseAnyDef)(refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodUnknown:\n            return (0, unknown_js_1.parseUnknownDef)(refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodDefault:\n            return (0, default_js_1.parseDefaultDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodBranded:\n            return (0, branded_js_1.parseBrandedDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodReadonly:\n            return (0, readonly_js_1.parseReadonlyDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodCatch:\n            return (0, catch_js_1.parseCatchDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodPipeline:\n            return (0, pipeline_js_1.parsePipelineDef)(def, refs);\n        case zod_1.ZodFirstPartyTypeKind.ZodFunction:\n        case zod_1.ZodFirstPartyTypeKind.ZodVoid:\n        case zod_1.ZodFirstPartyTypeKind.ZodSymbol:\n            return undefined;\n        default:\n            /* c8 ignore next */\n            return ((_) => undefined)(typeName);\n    }\n};\nexports.selectParser = selectParser;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,KAAK;AAC5B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,eAAe,CAAC,KAAK,UAAU;IACjC,OAAQ;QACJ,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;QAChD,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;QAChD,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;QAChD,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;QAChD,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe;QAC3C,KAAK,MAAM,qBAAqB,CAAC,OAAO;YACpC,OAAO,CAAC,GAAG,UAAU,YAAY,EAAE,KAAK;QAC5C,KAAK,MAAM,qBAAqB,CAAC,YAAY;YACzC,OAAO,CAAC,GAAG,eAAe,iBAAiB,EAAE;QACjD,KAAK,MAAM,qBAAqB,CAAC,OAAO;YACpC,OAAO,CAAC,GAAG,UAAU,YAAY,EAAE;QACvC,KAAK,MAAM,qBAAqB,CAAC,QAAQ;YACrC,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK;QAC9C,KAAK,MAAM,qBAAqB,CAAC,QAAQ;QACzC,KAAK,MAAM,qBAAqB,CAAC,qBAAqB;YAClD,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK;QAC9C,KAAK,MAAM,qBAAqB,CAAC,eAAe;YAC5C,OAAO,CAAC,GAAG,kBAAkB,oBAAoB,EAAE,KAAK;QAC5D,KAAK,MAAM,qBAAqB,CAAC,QAAQ;YACrC,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK;QAC9C,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO,CAAC,GAAG,YAAY,cAAc,EAAE,KAAK;QAChD,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE,KAAK;QAClD,KAAK,MAAM,qBAAqB,CAAC,OAAO;YACpC,OAAO,CAAC,GAAG,UAAU,YAAY,EAAE;QACvC,KAAK,MAAM,qBAAqB,CAAC,aAAa;YAC1C,OAAO,CAAC,GAAG,gBAAgB,kBAAkB,EAAE;QACnD,KAAK,MAAM,qBAAqB,CAAC,WAAW;YACxC,OAAO,CAAC,GAAG,cAAc,gBAAgB,EAAE,KAAK;QACpD,KAAK,MAAM,qBAAqB,CAAC,WAAW;YACxC,OAAO,CAAC,GAAG,cAAc,gBAAgB,EAAE,KAAK;QACpD,KAAK,MAAM,qBAAqB,CAAC,MAAM;YACnC,OAAO,CAAC,GAAG,SAAS,WAAW,EAAE,KAAK;QAC1C,KAAK,MAAM,qBAAqB,CAAC,MAAM;YACnC,OAAO,CAAC,GAAG,SAAS,WAAW,EAAE,KAAK;QAC1C,KAAK,MAAM,qBAAqB,CAAC,OAAO;YACpC,OAAO,IAAM,IAAI,MAAM,GAAG,IAAI;QAClC,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE,KAAK;QAClD,KAAK,MAAM,qBAAqB,CAAC,MAAM;QACvC,KAAK,MAAM,qBAAqB,CAAC,QAAQ;YACrC,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE;QACzC,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE,KAAK;QAClD,KAAK,MAAM,qBAAqB,CAAC,MAAM;YACnC,OAAO,CAAC,GAAG,SAAS,WAAW,EAAE;QACrC,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE;QAC7C,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE,KAAK;QAClD,KAAK,MAAM,qBAAqB,CAAC,UAAU;YACvC,OAAO,CAAC,GAAG,aAAa,eAAe,EAAE,KAAK;QAClD,KAAK,MAAM,qBAAqB,CAAC,WAAW;YACxC,OAAO,CAAC,GAAG,cAAc,gBAAgB,EAAE,KAAK;QACpD,KAAK,MAAM,qBAAqB,CAAC,QAAQ;YACrC,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK;QAC9C,KAAK,MAAM,qBAAqB,CAAC,WAAW;YACxC,OAAO,CAAC,GAAG,cAAc,gBAAgB,EAAE,KAAK;QACpD,KAAK,MAAM,qBAAqB,CAAC,WAAW;QAC5C,KAAK,MAAM,qBAAqB,CAAC,OAAO;QACxC,KAAK,MAAM,qBAAqB,CAAC,SAAS;YACtC,OAAO;QACX;YACI,kBAAkB,GAClB,OAAO,CAAC,CAAC,IAAM,SAAS,EAAE;IAClC;AACJ;AACA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parseDef.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseDef = void 0;\nconst Options_js_1 = require(\"./Options.js\");\nconst selectParser_js_1 = require(\"./selectParser.js\");\nconst getRelativePath_js_1 = require(\"./getRelativePath.js\");\nconst any_js_1 = require(\"./parsers/any.js\");\nfunction parseDef(def, refs, forceResolution = false) {\n    const seenItem = refs.seen.get(def);\n    if (refs.override) {\n        const overrideResult = refs.override?.(def, refs, seenItem, forceResolution);\n        if (overrideResult !== Options_js_1.ignoreOverride) {\n            return overrideResult;\n        }\n    }\n    if (seenItem && !forceResolution) {\n        const seenSchema = get$ref(seenItem, refs);\n        if (seenSchema !== undefined) {\n            return seenSchema;\n        }\n    }\n    const newItem = { def, path: refs.currentPath, jsonSchema: undefined };\n    refs.seen.set(def, newItem);\n    const jsonSchemaOrGetter = (0, selectParser_js_1.selectParser)(def, def.typeName, refs);\n    // If the return was a function, then the inner definition needs to be extracted before a call to parseDef (recursive)\n    const jsonSchema = typeof jsonSchemaOrGetter === \"function\"\n        ? parseDef(jsonSchemaOrGetter(), refs)\n        : jsonSchemaOrGetter;\n    if (jsonSchema) {\n        addMeta(def, refs, jsonSchema);\n    }\n    if (refs.postProcess) {\n        const postProcessResult = refs.postProcess(jsonSchema, def, refs);\n        newItem.jsonSchema = jsonSchema;\n        return postProcessResult;\n    }\n    newItem.jsonSchema = jsonSchema;\n    return jsonSchema;\n}\nexports.parseDef = parseDef;\nconst get$ref = (item, refs) => {\n    switch (refs.$refStrategy) {\n        case \"root\":\n            return { $ref: item.path.join(\"/\") };\n        case \"relative\":\n            return { $ref: (0, getRelativePath_js_1.getRelativePath)(refs.currentPath, item.path) };\n        case \"none\":\n        case \"seen\": {\n            if (item.path.length < refs.currentPath.length &&\n                item.path.every((value, index) => refs.currentPath[index] === value)) {\n                console.warn(`Recursive reference detected at ${refs.currentPath.join(\"/\")}! Defaulting to any`);\n                return (0, any_js_1.parseAnyDef)(refs);\n            }\n            return refs.$refStrategy === \"seen\" ? (0, any_js_1.parseAnyDef)(refs) : undefined;\n        }\n    }\n};\nconst addMeta = (def, refs, jsonSchema) => {\n    if (def.description) {\n        jsonSchema.description = def.description;\n        if (refs.markdownDescription) {\n            jsonSchema.markdownDescription = def.description;\n        }\n    }\n    return jsonSchema;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,KAAK;AACxB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,SAAS,GAAG,EAAE,IAAI,EAAE,kBAAkB,KAAK;IAChD,MAAM,WAAW,KAAK,IAAI,CAAC,GAAG,CAAC;IAC/B,IAAI,KAAK,QAAQ,EAAE;QACf,MAAM,iBAAiB,KAAK,QAAQ,GAAG,KAAK,MAAM,UAAU;QAC5D,IAAI,mBAAmB,aAAa,cAAc,EAAE;YAChD,OAAO;QACX;IACJ;IACA,IAAI,YAAY,CAAC,iBAAiB;QAC9B,MAAM,aAAa,QAAQ,UAAU;QACrC,IAAI,eAAe,WAAW;YAC1B,OAAO;QACX;IACJ;IACA,MAAM,UAAU;QAAE;QAAK,MAAM,KAAK,WAAW;QAAE,YAAY;IAAU;IACrE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK;IACnB,MAAM,qBAAqB,CAAC,GAAG,kBAAkB,YAAY,EAAE,KAAK,IAAI,QAAQ,EAAE;IAClF,sHAAsH;IACtH,MAAM,aAAa,OAAO,uBAAuB,aAC3C,SAAS,sBAAsB,QAC/B;IACN,IAAI,YAAY;QACZ,QAAQ,KAAK,MAAM;IACvB;IACA,IAAI,KAAK,WAAW,EAAE;QAClB,MAAM,oBAAoB,KAAK,WAAW,CAAC,YAAY,KAAK;QAC5D,QAAQ,UAAU,GAAG;QACrB,OAAO;IACX;IACA,QAAQ,UAAU,GAAG;IACrB,OAAO;AACX;AACA,QAAQ,QAAQ,GAAG;AACnB,MAAM,UAAU,CAAC,MAAM;IACnB,OAAQ,KAAK,YAAY;QACrB,KAAK;YACD,OAAO;gBAAE,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC;YAAK;QACvC,KAAK;YACD,OAAO;gBAAE,MAAM,CAAC,GAAG,qBAAqB,eAAe,EAAE,KAAK,WAAW,EAAE,KAAK,IAAI;YAAE;QAC1F,KAAK;QACL,KAAK;YAAQ;gBACT,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,IAC1C,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,QAAU,KAAK,WAAW,CAAC,MAAM,KAAK,QAAQ;oBACtE,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,mBAAmB,CAAC;oBAC/F,OAAO,CAAC,GAAG,SAAS,WAAW,EAAE;gBACrC;gBACA,OAAO,KAAK,YAAY,KAAK,SAAS,CAAC,GAAG,SAAS,WAAW,EAAE,QAAQ;YAC5E;IACJ;AACJ;AACA,MAAM,UAAU,CAAC,KAAK,MAAM;IACxB,IAAI,IAAI,WAAW,EAAE;QACjB,WAAW,WAAW,GAAG,IAAI,WAAW;QACxC,IAAI,KAAK,mBAAmB,EAAE;YAC1B,WAAW,mBAAmB,GAAG,IAAI,WAAW;QACpD;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1805, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/parseTypes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/zodToJsonSchema.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.zodToJsonSchema = void 0;\nconst parseDef_js_1 = require(\"./parseDef.js\");\nconst Refs_js_1 = require(\"./Refs.js\");\nconst any_js_1 = require(\"./parsers/any.js\");\nconst zodToJsonSchema = (schema, options) => {\n    const refs = (0, Refs_js_1.getRefs)(options);\n    let definitions = typeof options === \"object\" && options.definitions\n        ? Object.entries(options.definitions).reduce((acc, [name, schema]) => ({\n            ...acc,\n            [name]: (0, parseDef_js_1.parseDef)(schema._def, {\n                ...refs,\n                currentPath: [...refs.basePath, refs.definitionPath, name],\n            }, true) ?? (0, any_js_1.parseAnyDef)(refs),\n        }), {})\n        : undefined;\n    const name = typeof options === \"string\"\n        ? options\n        : options?.nameStrategy === \"title\"\n            ? undefined\n            : options?.name;\n    const main = (0, parseDef_js_1.parseDef)(schema._def, name === undefined\n        ? refs\n        : {\n            ...refs,\n            currentPath: [...refs.basePath, refs.definitionPath, name],\n        }, false) ?? (0, any_js_1.parseAnyDef)(refs);\n    const title = typeof options === \"object\" &&\n        options.name !== undefined &&\n        options.nameStrategy === \"title\"\n        ? options.name\n        : undefined;\n    if (title !== undefined) {\n        main.title = title;\n    }\n    if (refs.flags.hasReferencedOpenAiAnyType) {\n        if (!definitions) {\n            definitions = {};\n        }\n        if (!definitions[refs.openAiAnyTypeName]) {\n            definitions[refs.openAiAnyTypeName] = {\n                // Skipping \"object\" as no properties can be defined and additionalProperties must be \"false\"\n                type: [\"string\", \"number\", \"integer\", \"boolean\", \"array\", \"null\"],\n                items: {\n                    $ref: refs.$refStrategy === \"relative\"\n                        ? \"1\"\n                        : [\n                            ...refs.basePath,\n                            refs.definitionPath,\n                            refs.openAiAnyTypeName,\n                        ].join(\"/\"),\n                },\n            };\n        }\n    }\n    const combined = name === undefined\n        ? definitions\n            ? {\n                ...main,\n                [refs.definitionPath]: definitions,\n            }\n            : main\n        : {\n            $ref: [\n                ...(refs.$refStrategy === \"relative\" ? [] : refs.basePath),\n                refs.definitionPath,\n                name,\n            ].join(\"/\"),\n            [refs.definitionPath]: {\n                ...definitions,\n                [name]: main,\n            },\n        };\n    if (refs.target === \"jsonSchema7\") {\n        combined.$schema = \"http://json-schema.org/draft-07/schema#\";\n    }\n    else if (refs.target === \"jsonSchema2019-09\" || refs.target === \"openAi\") {\n        combined.$schema = \"https://json-schema.org/draft/2019-09/schema#\";\n    }\n    if (refs.target === \"openAi\" &&\n        (\"anyOf\" in combined ||\n            \"oneOf\" in combined ||\n            \"allOf\" in combined ||\n            (\"type\" in combined && Array.isArray(combined.type)))) {\n        console.warn(\"Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.\");\n    }\n    return combined;\n};\nexports.zodToJsonSchema = zodToJsonSchema;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,kBAAkB,CAAC,QAAQ;IAC7B,MAAM,OAAO,CAAC,GAAG,UAAU,OAAO,EAAE;IACpC,IAAI,cAAc,OAAO,YAAY,YAAY,QAAQ,WAAW,GAC9D,OAAO,OAAO,CAAC,QAAQ,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,OAAO,GAAK,CAAC;YACnE,GAAG,GAAG;YACN,CAAC,KAAK,EAAE,CAAC,GAAG,cAAc,QAAQ,EAAE,OAAO,IAAI,EAAE;gBAC7C,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,QAAQ;oBAAE,KAAK,cAAc;oBAAE;iBAAK;YAC9D,GAAG,SAAS,CAAC,GAAG,SAAS,WAAW,EAAE;QAC1C,CAAC,GAAG,CAAC,KACH;IACN,MAAM,OAAO,OAAO,YAAY,WAC1B,UACA,SAAS,iBAAiB,UACtB,YACA,SAAS;IACnB,MAAM,OAAO,CAAC,GAAG,cAAc,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,YACzD,OACA;QACE,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,QAAQ;YAAE,KAAK,cAAc;YAAE;SAAK;IAC9D,GAAG,UAAU,CAAC,GAAG,SAAS,WAAW,EAAE;IAC3C,MAAM,QAAQ,OAAO,YAAY,YAC7B,QAAQ,IAAI,KAAK,aACjB,QAAQ,YAAY,KAAK,UACvB,QAAQ,IAAI,GACZ;IACN,IAAI,UAAU,WAAW;QACrB,KAAK,KAAK,GAAG;IACjB;IACA,IAAI,KAAK,KAAK,CAAC,0BAA0B,EAAE;QACvC,IAAI,CAAC,aAAa;YACd,cAAc,CAAC;QACnB;QACA,IAAI,CAAC,WAAW,CAAC,KAAK,iBAAiB,CAAC,EAAE;YACtC,WAAW,CAAC,KAAK,iBAAiB,CAAC,GAAG;gBAClC,6FAA6F;gBAC7F,MAAM;oBAAC;oBAAU;oBAAU;oBAAW;oBAAW;oBAAS;iBAAO;gBACjE,OAAO;oBACH,MAAM,KAAK,YAAY,KAAK,aACtB,MACA;2BACK,KAAK,QAAQ;wBAChB,KAAK,cAAc;wBACnB,KAAK,iBAAiB;qBACzB,CAAC,IAAI,CAAC;gBACf;YACJ;QACJ;IACJ;IACA,MAAM,WAAW,SAAS,YACpB,cACI;QACE,GAAG,IAAI;QACP,CAAC,KAAK,cAAc,CAAC,EAAE;IAC3B,IACE,OACJ;QACE,MAAM;eACE,KAAK,YAAY,KAAK,aAAa,EAAE,GAAG,KAAK,QAAQ;YACzD,KAAK,cAAc;YACnB;SACH,CAAC,IAAI,CAAC;QACP,CAAC,KAAK,cAAc,CAAC,EAAE;YACnB,GAAG,WAAW;YACd,CAAC,KAAK,EAAE;QACZ;IACJ;IACJ,IAAI,KAAK,MAAM,KAAK,eAAe;QAC/B,SAAS,OAAO,GAAG;IACvB,OACK,IAAI,KAAK,MAAM,KAAK,uBAAuB,KAAK,MAAM,KAAK,UAAU;QACtE,SAAS,OAAO,GAAG;IACvB;IACA,IAAI,KAAK,MAAM,KAAK,YAChB,CAAC,WAAW,YACR,WAAW,YACX,WAAW,YACV,UAAU,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,CAAE,GAAG;QAC3D,QAAQ,IAAI,CAAC;IACjB;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/cjs/index.js"], "sourcesContent": ["\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./Options.js\"), exports);\n__exportStar(require(\"./Refs.js\"), exports);\n__exportStar(require(\"./errorMessages.js\"), exports);\n__exportStar(require(\"./getRelativePath.js\"), exports);\n__exportStar(require(\"./parseDef.js\"), exports);\n__exportStar(require(\"./parseTypes.js\"), exports);\n__exportStar(require(\"./parsers/any.js\"), exports);\n__exportStar(require(\"./parsers/array.js\"), exports);\n__exportStar(require(\"./parsers/bigint.js\"), exports);\n__exportStar(require(\"./parsers/boolean.js\"), exports);\n__exportStar(require(\"./parsers/branded.js\"), exports);\n__exportStar(require(\"./parsers/catch.js\"), exports);\n__exportStar(require(\"./parsers/date.js\"), exports);\n__exportStar(require(\"./parsers/default.js\"), exports);\n__exportStar(require(\"./parsers/effects.js\"), exports);\n__exportStar(require(\"./parsers/enum.js\"), exports);\n__exportStar(require(\"./parsers/intersection.js\"), exports);\n__exportStar(require(\"./parsers/literal.js\"), exports);\n__exportStar(require(\"./parsers/map.js\"), exports);\n__exportStar(require(\"./parsers/nativeEnum.js\"), exports);\n__exportStar(require(\"./parsers/never.js\"), exports);\n__exportStar(require(\"./parsers/null.js\"), exports);\n__exportStar(require(\"./parsers/nullable.js\"), exports);\n__exportStar(require(\"./parsers/number.js\"), exports);\n__exportStar(require(\"./parsers/object.js\"), exports);\n__exportStar(require(\"./parsers/optional.js\"), exports);\n__exportStar(require(\"./parsers/pipeline.js\"), exports);\n__exportStar(require(\"./parsers/promise.js\"), exports);\n__exportStar(require(\"./parsers/readonly.js\"), exports);\n__exportStar(require(\"./parsers/record.js\"), exports);\n__exportStar(require(\"./parsers/set.js\"), exports);\n__exportStar(require(\"./parsers/string.js\"), exports);\n__exportStar(require(\"./parsers/tuple.js\"), exports);\n__exportStar(require(\"./parsers/undefined.js\"), exports);\n__exportStar(require(\"./parsers/union.js\"), exports);\n__exportStar(require(\"./parsers/unknown.js\"), exports);\n__exportStar(require(\"./selectParser.js\"), exports);\n__exportStar(require(\"./zodToJsonSchema.js\"), exports);\nconst zodToJsonSchema_js_1 = require(\"./zodToJsonSchema.js\");\nexports.default = zodToJsonSchema_js_1.zodToJsonSchema;\n"], "names": [], "mappings": "AAAA;AACA,IAAI,kBAAkB,AAAC,IAAI,IAAI,IAAI,CAAC,eAAe,IAAK,CAAC,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1F,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QACjF,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAC9D;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AACjC,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACtB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AAChB,CAAE;AACF,IAAI,eAAe,AAAC,IAAI,IAAI,IAAI,CAAC,YAAY,IAAK,SAAS,CAAC,EAAE,QAAO;IACjE,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAS,IAAI,gBAAgB,UAAS,GAAG;AAC3H;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,uLAAsC;AACtC,oLAAmC;AACnC,6LAA4C;AAC5C,+LAA8C;AAC9C,wLAAuC;AACvC,0LAAyC;AACzC,2LAA0C;AAC1C,6LAA4C;AAC5C,8LAA6C;AAC7C,+LAA8C;AAC9C,+LAA8C;AAC9C,6LAA4C;AAC5C,4LAA2C;AAC3C,+LAA8C;AAC9C,+LAA8C;AAC9C,4LAA2C;AAC3C,oMAAmD;AACnD,+LAA8C;AAC9C,2LAA0C;AAC1C,kMAAiD;AACjD,6LAA4C;AAC5C,4LAA2C;AAC3C,gMAA+C;AAC/C,8LAA6C;AAC7C,8LAA6C;AAC7C,gMAA+C;AAC/C,gMAA+C;AAC/C,+LAA8C;AAC9C,gMAA+C;AAC/C,8LAA6C;AAC7C,2LAA0C;AAC1C,8LAA6C;AAC7C,6LAA4C;AAC5C,iMAAgD;AAChD,6LAA4C;AAC5C,+LAA8C;AAC9C,4LAA2C;AAC3C,+LAA8C;AAC9C,MAAM;AACN,QAAQ,OAAO,GAAG,qBAAqB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/Options.js"], "sourcesContent": ["export const ignoreOverride = Symbol(\"Let zodToJsonSchema decide on which parser to use\");\nexport const jsonDescription = (jsonSchema, def) => {\n    if (def.description) {\n        try {\n            return {\n                ...jsonSchema,\n                ...JSON.parse(def.description),\n            };\n        }\n        catch { }\n    }\n    return jsonSchema;\n};\nexport const defaultOptions = {\n    name: undefined,\n    $refStrategy: \"root\",\n    basePath: [\"#\"],\n    effectStrategy: \"input\",\n    pipeStrategy: \"all\",\n    dateStrategy: \"format:date-time\",\n    mapStrategy: \"entries\",\n    removeAdditionalStrategy: \"passthrough\",\n    allowedAdditionalProperties: true,\n    rejectedAdditionalProperties: false,\n    definitionPath: \"definitions\",\n    target: \"jsonSchema7\",\n    strictUnions: false,\n    definitions: {},\n    errorMessages: false,\n    markdownDescription: false,\n    patternStrategy: \"escape\",\n    applyRegexFlags: false,\n    emailStrategy: \"format:email\",\n    base64Strategy: \"contentEncoding:base64\",\n    nameStrategy: \"ref\",\n    openAiAnyTypeName: \"OpenAiAnyType\"\n};\nexport const getDefaultOptions = (options) => (typeof options === \"string\"\n    ? {\n        ...defaultOptions,\n        name: options,\n    }\n    : {\n        ...defaultOptions,\n        ...options,\n    });\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,iBAAiB,OAAO;AAC9B,MAAM,kBAAkB,CAAC,YAAY;IACxC,IAAI,IAAI,WAAW,EAAE;QACjB,IAAI;YACA,OAAO;gBACH,GAAG,UAAU;gBACb,GAAG,KAAK,KAAK,CAAC,IAAI,WAAW,CAAC;YAClC;QACJ,EACA,OAAM,CAAE;IACZ;IACA,OAAO;AACX;AACO,MAAM,iBAAiB;IAC1B,MAAM;IACN,cAAc;IACd,UAAU;QAAC;KAAI;IACf,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,aAAa;IACb,0BAA0B;IAC1B,6BAA6B;IAC7B,8BAA8B;IAC9B,gBAAgB;IAChB,QAAQ;IACR,cAAc;IACd,aAAa,CAAC;IACd,eAAe;IACf,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,mBAAmB;AACvB;AACO,MAAM,oBAAoB,CAAC,UAAa,OAAO,YAAY,WAC5D;QACE,GAAG,cAAc;QACjB,MAAM;IACV,IACE;QACE,GAAG,cAAc;QACjB,GAAG,OAAO;IACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/Refs.js"], "sourcesContent": ["import { getDefaultOptions } from \"./Options.js\";\nexport const getRefs = (options) => {\n    const _options = getDefaultOptions(options);\n    const currentPath = _options.name !== undefined\n        ? [..._options.basePath, _options.definitionPath, _options.name]\n        : _options.basePath;\n    return {\n        ..._options,\n        flags: { hasReferencedOpenAiAnyType: false },\n        currentPath: currentPath,\n        propertyPath: undefined,\n        seen: new Map(Object.entries(_options.definitions).map(([name, def]) => [\n            def._def,\n            {\n                def: def._def,\n                path: [..._options.basePath, _options.definitionPath, name],\n                // Resolution of references will be forced even though seen, so it's ok that the schema is undefined here for now.\n                jsonSchema: undefined,\n            },\n        ])),\n    };\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAC;IACpB,MAAM,WAAW,CAAA,GAAA,mQAAA,CAAA,oBAAiB,AAAD,EAAE;IACnC,MAAM,cAAc,SAAS,IAAI,KAAK,YAChC;WAAI,SAAS,QAAQ;QAAE,SAAS,cAAc;QAAE,SAAS,IAAI;KAAC,GAC9D,SAAS,QAAQ;IACvB,OAAO;QACH,GAAG,QAAQ;QACX,OAAO;YAAE,4BAA4B;QAAM;QAC3C,aAAa;QACb,cAAc;QACd,MAAM,IAAI,IAAI,OAAO,OAAO,CAAC,SAAS,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,GAAK;gBACpE,IAAI,IAAI;gBACR;oBACI,KAAK,IAAI,IAAI;oBACb,MAAM;2BAAI,SAAS,QAAQ;wBAAE,SAAS,cAAc;wBAAE;qBAAK;oBAC3D,kHAAkH;oBAClH,YAAY;gBAChB;aACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2069, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/errorMessages.js"], "sourcesContent": ["export function addErrorMessage(res, key, errorMessage, refs) {\n    if (!refs?.errorMessages)\n        return;\n    if (errorMessage) {\n        res.errorMessage = {\n            ...res.errorMessage,\n            [key]: errorMessage,\n        };\n    }\n}\nexport function setResponseValueAndErrors(res, key, value, errorMessage, refs) {\n    res[key] = value;\n    addErrorMessage(res, key, errorMessage, refs);\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI;IACxD,IAAI,CAAC,MAAM,eACP;IACJ,IAAI,cAAc;QACd,IAAI,YAAY,GAAG;YACf,GAAG,IAAI,YAAY;YACnB,CAAC,IAAI,EAAE;QACX;IACJ;AACJ;AACO,SAAS,0BAA0B,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI;IACzE,GAAG,CAAC,IAAI,GAAG;IACX,gBAAgB,KAAK,KAAK,cAAc;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/getRelativePath.js"], "sourcesContent": ["export const getRelativePath = (pathA, pathB) => {\n    let i = 0;\n    for (; i < pathA.length && i < pathB.length; i++) {\n        if (pathA[i] !== pathB[i])\n            break;\n    }\n    return [(pathA.length - i).toString(), ...pathB.slice(i)].join(\"/\");\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,kBAAkB,CAAC,OAAO;IACnC,IAAI,IAAI;IACR,MAAO,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE,IAAK;QAC9C,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EACrB;IACR;IACA,OAAO;QAAC,CAAC,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ;WAAO,MAAM,KAAK,CAAC;KAAG,CAAC,IAAI,CAAC;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2111, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/any.js"], "sourcesContent": ["import { getRelativePath } from \"../getRelativePath.js\";\nexport function parseAnyDef(refs) {\n    if (refs.target !== \"openAi\") {\n        return {};\n    }\n    const anyDefinitionPath = [\n        ...refs.basePath,\n        refs.definitionPath,\n        refs.openAiAnyTypeName,\n    ];\n    refs.flags.hasReferencedOpenAiAnyType = true;\n    return {\n        $ref: refs.$refStrategy === \"relative\"\n            ? getRelativePath(anyDefinitionPath, refs.currentPath)\n            : anyDefinitionPath.join(\"/\"),\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,KAAK,MAAM,KAAK,UAAU;QAC1B,OAAO,CAAC;IACZ;IACA,MAAM,oBAAoB;WACnB,KAAK,QAAQ;QAChB,KAAK,cAAc;QACnB,KAAK,iBAAiB;KACzB;IACD,KAAK,KAAK,CAAC,0BAA0B,GAAG;IACxC,OAAO;QACH,MAAM,KAAK,YAAY,KAAK,aACtB,CAAA,GAAA,2QAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,KAAK,WAAW,IACnD,kBAAkB,IAAI,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/array.js"], "sourcesContent": ["import { ZodFirstPartyTypeKind } from \"zod\";\nimport { setResponseValueAndErrors } from \"../errorMessages.js\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseArrayDef(def, refs) {\n    const res = {\n        type: \"array\",\n    };\n    if (def.type?._def &&\n        def.type?._def?.typeName !== ZodFirstPartyTypeKind.ZodAny) {\n        res.items = parseDef(def.type._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"items\"],\n        });\n    }\n    if (def.minLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.minLength.value, def.minLength.message, refs);\n    }\n    if (def.maxLength) {\n        setResponseValueAndErrors(res, \"maxItems\", def.maxLength.value, def.maxLength.message, refs);\n    }\n    if (def.exactLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.exactLength.value, def.exactLength.message, refs);\n        setResponseValueAndErrors(res, \"maxItems\", def.exactLength.value, def.exactLength.message, refs);\n    }\n    return res;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,cAAc,GAAG,EAAE,IAAI;IACnC,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,IAAI,IAAI,EAAE,QACV,IAAI,IAAI,EAAE,MAAM,aAAa,oLAAA,CAAA,wBAAqB,CAAC,MAAM,EAAE;QAC3D,IAAI,KAAK,GAAG,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;YAChC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAQ;QAC/C;IACJ;IACA,IAAI,IAAI,SAAS,EAAE;QACf,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,YAAY,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;IAC3F;IACA,IAAI,IAAI,SAAS,EAAE;QACf,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,YAAY,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;IAC3F;IACA,IAAI,IAAI,WAAW,EAAE;QACjB,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,YAAY,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;QAC3F,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,YAAY,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;IAC/F;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/bigint.js"], "sourcesContent": ["import { setResponseValueAndErrors } from \"../errorMessages.js\";\nexport function parseBigintDef(def, refs) {\n    const res = {\n        type: \"integer\",\n        format: \"int64\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                setResponseValueAndErrors(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,MAAM,MAAM;QACR,MAAM;QACN,QAAQ;IACZ;IACA,IAAI,CAAC,IAAI,MAAM,EACX,OAAO;IACX,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC1E,OACK;wBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBACnF;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAC1E;gBACA;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC1E,OACK;wBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBACnF;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAC1E;gBACA;YACJ,KAAK;gBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,cAAc,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBACzE;QACR;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/boolean.js"], "sourcesContent": ["export function parseBooleanDef() {\n    return {\n        type: \"boolean\",\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,OAAO;QACH,MAAM;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/branded.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport function parseBrandedDef(_def, refs) {\n    return parseDef(_def.type._def, refs);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,gBAAgB,IAAI,EAAE,IAAI;IACtC,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/catch.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport const parseCatchDef = (def, refs) => {\n    return parseDef(def.innerType._def, refs);\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAC,KAAK;IAC/B,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/date.js"], "sourcesContent": ["import { setResponseValueAndErrors } from \"../errorMessages.js\";\nexport function parseDateDef(def, refs, overrideDateStrategy) {\n    const strategy = overrideDateStrategy ?? refs.dateStrategy;\n    if (Array.isArray(strategy)) {\n        return {\n            anyOf: strategy.map((item, i) => parseDateDef(def, refs, item)),\n        };\n    }\n    switch (strategy) {\n        case \"string\":\n        case \"format:date-time\":\n            return {\n                type: \"string\",\n                format: \"date-time\",\n            };\n        case \"format:date\":\n            return {\n                type: \"string\",\n                format: \"date\",\n            };\n        case \"integer\":\n            return integerDateParser(def, refs);\n    }\n}\nconst integerDateParser = (def, refs) => {\n    const res = {\n        type: \"integer\",\n        format: \"unix-time\",\n    };\n    if (refs.target === \"openApi3\") {\n        return res;\n    }\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                setResponseValueAndErrors(res, \"minimum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n            case \"max\":\n                setResponseValueAndErrors(res, \"maximum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n        }\n    }\n    return res;\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,aAAa,GAAG,EAAE,IAAI,EAAE,oBAAoB;IACxD,MAAM,WAAW,wBAAwB,KAAK,YAAY;IAC1D,IAAI,MAAM,OAAO,CAAC,WAAW;QACzB,OAAO;YACH,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM,IAAM,aAAa,KAAK,MAAM;QAC7D;IACJ;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;QACJ,KAAK;YACD,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;QACJ,KAAK;YACD,OAAO,kBAAkB,KAAK;IACtC;AACJ;AACA,MAAM,oBAAoB,CAAC,KAAK;IAC5B,MAAM,MAAM;QACR,MAAM;QACN,QAAQ;IACZ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;IACX;IACA,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EACrD,MAAM,OAAO,EAAE;gBACf;YACJ,KAAK;gBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EACrD,MAAM,OAAO,EAAE;gBACf;QACR;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/default.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport function parseDefaultDef(_def, refs) {\n    return {\n        ...parseDef(_def.innerType._def, refs),\n        default: _def.defaultValue(),\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,gBAAgB,IAAI,EAAE,IAAI;IACtC,OAAO;QACH,GAAG,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK;QACtC,SAAS,KAAK,YAAY;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/effects.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nimport { parseAnyDef } from \"./any.js\";\nexport function parseEffectsDef(_def, refs) {\n    return refs.effectStrategy === \"input\"\n        ? parseDef(_def.schema._def, refs)\n        : parseAnyDef(refs);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,gBAAgB,IAAI,EAAE,IAAI;IACtC,OAAO,KAAK,cAAc,KAAK,UACzB,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE,QAC3B,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/enum.js"], "sourcesContent": ["export function parseEnumDef(def) {\n    return {\n        type: \"string\",\n        enum: Array.from(def.values),\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,aAAa,GAAG;IAC5B,OAAO;QACH,MAAM;QACN,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/intersection.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nconst isJsonSchema7AllOfType = (type) => {\n    if (\"type\" in type && type.type === \"string\")\n        return false;\n    return \"allOf\" in type;\n};\nexport function parseIntersectionDef(def, refs) {\n    const allOf = [\n        parseDef(def.left._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n        }),\n        parseDef(def.right._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"1\"],\n        }),\n    ].filter((x) => !!x);\n    let unevaluatedProperties = refs.target === \"jsonSchema2019-09\"\n        ? { unevaluatedProperties: false }\n        : undefined;\n    const mergedAllOf = [];\n    // If either of the schemas is an allOf, merge them into a single allOf\n    allOf.forEach((schema) => {\n        if (isJsonSchema7AllOfType(schema)) {\n            mergedAllOf.push(...schema.allOf);\n            if (schema.unevaluatedProperties === undefined) {\n                // If one of the schemas has no unevaluatedProperties set,\n                // the merged schema should also have no unevaluatedProperties set\n                unevaluatedProperties = undefined;\n            }\n        }\n        else {\n            let nestedSchema = schema;\n            if (\"additionalProperties\" in schema &&\n                schema.additionalProperties === false) {\n                const { additionalProperties, ...rest } = schema;\n                nestedSchema = rest;\n            }\n            else {\n                // As soon as one of the schemas has additionalProperties set not to false, we allow unevaluatedProperties\n                unevaluatedProperties = undefined;\n            }\n            mergedAllOf.push(nestedSchema);\n        }\n    });\n    return mergedAllOf.length\n        ? {\n            allOf: mergedAllOf,\n            ...unevaluatedProperties,\n        }\n        : undefined;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,yBAAyB,CAAC;IAC5B,IAAI,UAAU,QAAQ,KAAK,IAAI,KAAK,UAChC,OAAO;IACX,OAAO,WAAW;AACtB;AACO,SAAS,qBAAqB,GAAG,EAAE,IAAI;IAC1C,MAAM,QAAQ;QACV,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;YACpB,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS;aAAI;QACpD;QACA,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE;YACrB,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS;aAAI;QACpD;KACH,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;IAClB,IAAI,wBAAwB,KAAK,MAAM,KAAK,sBACtC;QAAE,uBAAuB;IAAM,IAC/B;IACN,MAAM,cAAc,EAAE;IACtB,uEAAuE;IACvE,MAAM,OAAO,CAAC,CAAC;QACX,IAAI,uBAAuB,SAAS;YAChC,YAAY,IAAI,IAAI,OAAO,KAAK;YAChC,IAAI,OAAO,qBAAqB,KAAK,WAAW;gBAC5C,0DAA0D;gBAC1D,kEAAkE;gBAClE,wBAAwB;YAC5B;QACJ,OACK;YACD,IAAI,eAAe;YACnB,IAAI,0BAA0B,UAC1B,OAAO,oBAAoB,KAAK,OAAO;gBACvC,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,GAAG;gBAC1C,eAAe;YACnB,OACK;gBACD,0GAA0G;gBAC1G,wBAAwB;YAC5B;YACA,YAAY,IAAI,CAAC;QACrB;IACJ;IACA,OAAO,YAAY,MAAM,GACnB;QACE,OAAO;QACP,GAAG,qBAAqB;IAC5B,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/literal.js"], "sourcesContent": ["export function parseLiteralDef(def, refs) {\n    const parsedType = typeof def.value;\n    if (parsedType !== \"bigint\" &&\n        parsedType !== \"number\" &&\n        parsedType !== \"boolean\" &&\n        parsedType !== \"string\") {\n        return {\n            type: Array.isArray(def.value) ? \"array\" : \"object\",\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        return {\n            type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n            enum: [def.value],\n        };\n    }\n    return {\n        type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n        const: def.value,\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,gBAAgB,GAAG,EAAE,IAAI;IACrC,MAAM,aAAa,OAAO,IAAI,KAAK;IACnC,IAAI,eAAe,YACf,eAAe,YACf,eAAe,aACf,eAAe,UAAU;QACzB,OAAO;YACH,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK,IAAI,UAAU;QAC/C;IACJ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;YACH,MAAM,eAAe,WAAW,YAAY;YAC5C,MAAM;gBAAC,IAAI,KAAK;aAAC;QACrB;IACJ;IACA,OAAO;QACH,MAAM,eAAe,WAAW,YAAY;QAC5C,OAAO,IAAI,KAAK;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/string.js"], "sourcesContent": ["import { setResponseValueAndErrors } from \"../errorMessages.js\";\nlet emojiRegex = undefined;\n/**\n * Generated from the regular expressions found here as of 2024-05-22:\n * https://github.com/colinhacks/zod/blob/master/src/types.ts.\n *\n * Expressions with /i flag have been changed accordingly.\n */\nexport const zodPatterns = {\n    /**\n     * `c` was changed to `[cC]` to replicate /i flag\n     */\n    cuid: /^[cC][^\\s-]{8,}$/,\n    cuid2: /^[0-9a-z]+$/,\n    ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,\n    /**\n     * `a-z` was added to replicate /i flag\n     */\n    email: /^(?!\\.)(?!.*\\.\\.)([a-zA-Z0-9_'+\\-\\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\\-]*\\.)+[a-zA-Z]{2,}$/,\n    /**\n     * Constructed a valid Unicode RegExp\n     *\n     * Lazily instantiate since this type of regex isn't supported\n     * in all envs (e.g. React Native).\n     *\n     * See:\n     * https://github.com/colinhacks/zod/issues/2433\n     * Fix in Zod:\n     * https://github.com/colinhacks/zod/commit/9340fd51e48576a75adc919bff65dbc4a5d4c99b\n     */\n    emoji: () => {\n        if (emojiRegex === undefined) {\n            emojiRegex = RegExp(\"^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$\", \"u\");\n        }\n        return emojiRegex;\n    },\n    /**\n     * Unused\n     */\n    uuid: /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/,\n    /**\n     * Unused\n     */\n    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,\n    ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/,\n    /**\n     * Unused\n     */\n    ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,\n    ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,\n    base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,\n    base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,\n    nanoid: /^[a-zA-Z0-9_-]{21}$/,\n    jwt: /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/,\n};\nexport function parseStringDef(def, refs) {\n    const res = {\n        type: \"string\",\n    };\n    if (def.checks) {\n        for (const check of def.checks) {\n            switch (check.kind) {\n                case \"min\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"max\":\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"email\":\n                    switch (refs.emailStrategy) {\n                        case \"format:email\":\n                            addFormat(res, \"email\", check.message, refs);\n                            break;\n                        case \"format:idn-email\":\n                            addFormat(res, \"idn-email\", check.message, refs);\n                            break;\n                        case \"pattern:zod\":\n                            addPattern(res, zodPatterns.email, check.message, refs);\n                            break;\n                    }\n                    break;\n                case \"url\":\n                    addFormat(res, \"uri\", check.message, refs);\n                    break;\n                case \"uuid\":\n                    addFormat(res, \"uuid\", check.message, refs);\n                    break;\n                case \"regex\":\n                    addPattern(res, check.regex, check.message, refs);\n                    break;\n                case \"cuid\":\n                    addPattern(res, zodPatterns.cuid, check.message, refs);\n                    break;\n                case \"cuid2\":\n                    addPattern(res, zodPatterns.cuid2, check.message, refs);\n                    break;\n                case \"startsWith\":\n                    addPattern(res, RegExp(`^${escapeLiteralCheckValue(check.value, refs)}`), check.message, refs);\n                    break;\n                case \"endsWith\":\n                    addPattern(res, RegExp(`${escapeLiteralCheckValue(check.value, refs)}$`), check.message, refs);\n                    break;\n                case \"datetime\":\n                    addFormat(res, \"date-time\", check.message, refs);\n                    break;\n                case \"date\":\n                    addFormat(res, \"date\", check.message, refs);\n                    break;\n                case \"time\":\n                    addFormat(res, \"time\", check.message, refs);\n                    break;\n                case \"duration\":\n                    addFormat(res, \"duration\", check.message, refs);\n                    break;\n                case \"length\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"includes\": {\n                    addPattern(res, RegExp(escapeLiteralCheckValue(check.value, refs)), check.message, refs);\n                    break;\n                }\n                case \"ip\": {\n                    if (check.version !== \"v6\") {\n                        addFormat(res, \"ipv4\", check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addFormat(res, \"ipv6\", check.message, refs);\n                    }\n                    break;\n                }\n                case \"base64url\":\n                    addPattern(res, zodPatterns.base64url, check.message, refs);\n                    break;\n                case \"jwt\":\n                    addPattern(res, zodPatterns.jwt, check.message, refs);\n                    break;\n                case \"cidr\": {\n                    if (check.version !== \"v6\") {\n                        addPattern(res, zodPatterns.ipv4Cidr, check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addPattern(res, zodPatterns.ipv6Cidr, check.message, refs);\n                    }\n                    break;\n                }\n                case \"emoji\":\n                    addPattern(res, zodPatterns.emoji(), check.message, refs);\n                    break;\n                case \"ulid\": {\n                    addPattern(res, zodPatterns.ulid, check.message, refs);\n                    break;\n                }\n                case \"base64\": {\n                    switch (refs.base64Strategy) {\n                        case \"format:binary\": {\n                            addFormat(res, \"binary\", check.message, refs);\n                            break;\n                        }\n                        case \"contentEncoding:base64\": {\n                            setResponseValueAndErrors(res, \"contentEncoding\", \"base64\", check.message, refs);\n                            break;\n                        }\n                        case \"pattern:zod\": {\n                            addPattern(res, zodPatterns.base64, check.message, refs);\n                            break;\n                        }\n                    }\n                    break;\n                }\n                case \"nanoid\": {\n                    addPattern(res, zodPatterns.nanoid, check.message, refs);\n                }\n                case \"toLowerCase\":\n                case \"toUpperCase\":\n                case \"trim\":\n                    break;\n                default:\n                    /* c8 ignore next */\n                    ((_) => { })(check);\n            }\n        }\n    }\n    return res;\n}\nfunction escapeLiteralCheckValue(literal, refs) {\n    return refs.patternStrategy === \"escape\"\n        ? escapeNonAlphaNumeric(literal)\n        : literal;\n}\nconst ALPHA_NUMERIC = new Set(\"ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789\");\nfunction escapeNonAlphaNumeric(source) {\n    let result = \"\";\n    for (let i = 0; i < source.length; i++) {\n        if (!ALPHA_NUMERIC.has(source[i])) {\n            result += \"\\\\\";\n        }\n        result += source[i];\n    }\n    return result;\n}\n// Adds a \"format\" keyword to the schema. If a format exists, both formats will be joined in an allOf-node, along with subsequent ones.\nfunction addFormat(schema, value, message, refs) {\n    if (schema.format || schema.anyOf?.some((x) => x.format)) {\n        if (!schema.anyOf) {\n            schema.anyOf = [];\n        }\n        if (schema.format) {\n            schema.anyOf.push({\n                format: schema.format,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { format: schema.errorMessage.format },\n                }),\n            });\n            delete schema.format;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.format;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.anyOf.push({\n            format: value,\n            ...(message &&\n                refs.errorMessages && { errorMessage: { format: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"format\", value, message, refs);\n    }\n}\n// Adds a \"pattern\" keyword to the schema. If a pattern exists, both patterns will be joined in an allOf-node, along with subsequent ones.\nfunction addPattern(schema, regex, message, refs) {\n    if (schema.pattern || schema.allOf?.some((x) => x.pattern)) {\n        if (!schema.allOf) {\n            schema.allOf = [];\n        }\n        if (schema.pattern) {\n            schema.allOf.push({\n                pattern: schema.pattern,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { pattern: schema.errorMessage.pattern },\n                }),\n            });\n            delete schema.pattern;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.pattern;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.allOf.push({\n            pattern: stringifyRegExpWithFlags(regex, refs),\n            ...(message &&\n                refs.errorMessages && { errorMessage: { pattern: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"pattern\", stringifyRegExpWithFlags(regex, refs), message, refs);\n    }\n}\n// Mutate z.string.regex() in a best attempt to accommodate for regex flags when applyRegexFlags is true\nfunction stringifyRegExpWithFlags(regex, refs) {\n    if (!refs.applyRegexFlags || !regex.flags) {\n        return regex.source;\n    }\n    // Currently handled flags\n    const flags = {\n        i: regex.flags.includes(\"i\"),\n        m: regex.flags.includes(\"m\"),\n        s: regex.flags.includes(\"s\"), // `.` matches newlines\n    };\n    // The general principle here is to step through each character, one at a time, applying mutations as flags require. We keep track when the current character is escaped, and when it's inside a group /like [this]/ or (also) a range like /[a-z]/. The following is fairly brittle imperative code; edit at your peril!\n    const source = flags.i ? regex.source.toLowerCase() : regex.source;\n    let pattern = \"\";\n    let isEscaped = false;\n    let inCharGroup = false;\n    let inCharRange = false;\n    for (let i = 0; i < source.length; i++) {\n        if (isEscaped) {\n            pattern += source[i];\n            isEscaped = false;\n            continue;\n        }\n        if (flags.i) {\n            if (inCharGroup) {\n                if (source[i].match(/[a-z]/)) {\n                    if (inCharRange) {\n                        pattern += source[i];\n                        pattern += `${source[i - 2]}-${source[i]}`.toUpperCase();\n                        inCharRange = false;\n                    }\n                    else if (source[i + 1] === \"-\" && source[i + 2]?.match(/[a-z]/)) {\n                        pattern += source[i];\n                        inCharRange = true;\n                    }\n                    else {\n                        pattern += `${source[i]}${source[i].toUpperCase()}`;\n                    }\n                    continue;\n                }\n            }\n            else if (source[i].match(/[a-z]/)) {\n                pattern += `[${source[i]}${source[i].toUpperCase()}]`;\n                continue;\n            }\n        }\n        if (flags.m) {\n            if (source[i] === \"^\") {\n                pattern += `(^|(?<=[\\r\\n]))`;\n                continue;\n            }\n            else if (source[i] === \"$\") {\n                pattern += `($|(?=[\\r\\n]))`;\n                continue;\n            }\n        }\n        if (flags.s && source[i] === \".\") {\n            pattern += inCharGroup ? `${source[i]}\\r\\n` : `[${source[i]}\\r\\n]`;\n            continue;\n        }\n        pattern += source[i];\n        if (source[i] === \"\\\\\") {\n            isEscaped = true;\n        }\n        else if (inCharGroup && source[i] === \"]\") {\n            inCharGroup = false;\n        }\n        else if (!inCharGroup && source[i] === \"[\") {\n            inCharGroup = true;\n        }\n    }\n    try {\n        new RegExp(pattern);\n    }\n    catch {\n        console.warn(`Could not convert regex pattern at ${refs.currentPath.join(\"/\")} to a flag-independent form! Falling back to the flag-ignorant source`);\n        return regex.source;\n    }\n    return pattern;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,IAAI,aAAa;AAOV,MAAM,cAAc;IACvB;;KAEC,GACD,MAAM;IACN,OAAO;IACP,MAAM;IACN;;KAEC,GACD,OAAO;IACP;;;;;;;;;;KAUC,GACD,OAAO;QACH,IAAI,eAAe,WAAW;YAC1B,aAAa,OAAO,wDAAwD;QAChF;QACA,OAAO;IACX;IACA;;KAEC,GACD,MAAM;IACN;;KAEC,GACD,MAAM;IACN,UAAU;IACV;;KAEC,GACD,MAAM;IACN,UAAU;IACV,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,KAAK;AACT;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,IAAI,MAAM,EAAE;QACZ,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;YAC5B,OAAQ,MAAM,IAAI;gBACd,KAAK;oBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WAC/D,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WAC/D,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBACD,OAAQ,KAAK,aAAa;wBACtB,KAAK;4BACD,UAAU,KAAK,SAAS,MAAM,OAAO,EAAE;4BACvC;wBACJ,KAAK;4BACD,UAAU,KAAK,aAAa,MAAM,OAAO,EAAE;4BAC3C;wBACJ,KAAK;4BACD,WAAW,KAAK,YAAY,KAAK,EAAE,MAAM,OAAO,EAAE;4BAClD;oBACR;oBACA;gBACJ,KAAK;oBACD,UAAU,KAAK,OAAO,MAAM,OAAO,EAAE;oBACrC;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,WAAW,KAAK,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC5C;gBACJ,KAAK;oBACD,WAAW,KAAK,YAAY,IAAI,EAAE,MAAM,OAAO,EAAE;oBACjD;gBACJ,KAAK;oBACD,WAAW,KAAK,YAAY,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClD;gBACJ,KAAK;oBACD,WAAW,KAAK,OAAO,CAAC,CAAC,EAAE,wBAAwB,MAAM,KAAK,EAAE,OAAO,GAAG,MAAM,OAAO,EAAE;oBACzF;gBACJ,KAAK;oBACD,WAAW,KAAK,OAAO,GAAG,wBAAwB,MAAM,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,OAAO,EAAE;oBACzF;gBACJ,KAAK;oBACD,UAAU,KAAK,aAAa,MAAM,OAAO,EAAE;oBAC3C;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;oBACtC;gBACJ,KAAK;oBACD,UAAU,KAAK,YAAY,MAAM,OAAO,EAAE;oBAC1C;gBACJ,KAAK;oBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WAC/D,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,WAC/D,KAAK,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,KAAK,IACnC,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAClC;gBACJ,KAAK;oBAAY;wBACb,WAAW,KAAK,OAAO,wBAAwB,MAAM,KAAK,EAAE,QAAQ,MAAM,OAAO,EAAE;wBACnF;oBACJ;gBACA,KAAK;oBAAM;wBACP,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;wBAC1C;wBACA,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,UAAU,KAAK,QAAQ,MAAM,OAAO,EAAE;wBAC1C;wBACA;oBACJ;gBACA,KAAK;oBACD,WAAW,KAAK,YAAY,SAAS,EAAE,MAAM,OAAO,EAAE;oBACtD;gBACJ,KAAK;oBACD,WAAW,KAAK,YAAY,GAAG,EAAE,MAAM,OAAO,EAAE;oBAChD;gBACJ,KAAK;oBAAQ;wBACT,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,WAAW,KAAK,YAAY,QAAQ,EAAE,MAAM,OAAO,EAAE;wBACzD;wBACA,IAAI,MAAM,OAAO,KAAK,MAAM;4BACxB,WAAW,KAAK,YAAY,QAAQ,EAAE,MAAM,OAAO,EAAE;wBACzD;wBACA;oBACJ;gBACA,KAAK;oBACD,WAAW,KAAK,YAAY,KAAK,IAAI,MAAM,OAAO,EAAE;oBACpD;gBACJ,KAAK;oBAAQ;wBACT,WAAW,KAAK,YAAY,IAAI,EAAE,MAAM,OAAO,EAAE;wBACjD;oBACJ;gBACA,KAAK;oBAAU;wBACX,OAAQ,KAAK,cAAc;4BACvB,KAAK;gCAAiB;oCAClB,UAAU,KAAK,UAAU,MAAM,OAAO,EAAE;oCACxC;gCACJ;4BACA,KAAK;gCAA0B;oCAC3B,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,mBAAmB,UAAU,MAAM,OAAO,EAAE;oCAC3E;gCACJ;4BACA,KAAK;gCAAe;oCAChB,WAAW,KAAK,YAAY,MAAM,EAAE,MAAM,OAAO,EAAE;oCACnD;gCACJ;wBACJ;wBACA;oBACJ;gBACA,KAAK;oBAAU;wBACX,WAAW,KAAK,YAAY,MAAM,EAAE,MAAM,OAAO,EAAE;oBACvD;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD;gBACJ;oBACI,kBAAkB,GAClB,CAAC,CAAC,KAAQ,CAAC,EAAE;YACrB;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,wBAAwB,OAAO,EAAE,IAAI;IAC1C,OAAO,KAAK,eAAe,KAAK,WAC1B,sBAAsB,WACtB;AACV;AACA,MAAM,gBAAgB,IAAI,IAAI;AAC9B,SAAS,sBAAsB,MAAM;IACjC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,IAAI,CAAC,cAAc,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/B,UAAU;QACd;QACA,UAAU,MAAM,CAAC,EAAE;IACvB;IACA,OAAO;AACX;AACA,uIAAuI;AACvI,SAAS,UAAU,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;IAC3C,IAAI,OAAO,MAAM,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,IAAM,EAAE,MAAM,GAAG;QACtD,IAAI,CAAC,OAAO,KAAK,EAAE;YACf,OAAO,KAAK,GAAG,EAAE;QACrB;QACA,IAAI,OAAO,MAAM,EAAE;YACf,OAAO,KAAK,CAAC,IAAI,CAAC;gBACd,QAAQ,OAAO,MAAM;gBACrB,GAAI,OAAO,YAAY,IACnB,KAAK,aAAa,IAAI;oBACtB,cAAc;wBAAE,QAAQ,OAAO,YAAY,CAAC,MAAM;oBAAC;gBACvD,CAAC;YACL;YACA,OAAO,OAAO,MAAM;YACpB,IAAI,OAAO,YAAY,EAAE;gBACrB,OAAO,OAAO,YAAY,CAAC,MAAM;gBACjC,IAAI,OAAO,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,KAAK,GAAG;oBAC/C,OAAO,OAAO,YAAY;gBAC9B;YACJ;QACJ;QACA,OAAO,KAAK,CAAC,IAAI,CAAC;YACd,QAAQ;YACR,GAAI,WACA,KAAK,aAAa,IAAI;gBAAE,cAAc;oBAAE,QAAQ;gBAAQ;YAAE,CAAC;QACnE;IACJ,OACK;QACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,UAAU,OAAO,SAAS;IAChE;AACJ;AACA,0IAA0I;AAC1I,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;IAC5C,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE,KAAK,CAAC,IAAM,EAAE,OAAO,GAAG;QACxD,IAAI,CAAC,OAAO,KAAK,EAAE;YACf,OAAO,KAAK,GAAG,EAAE;QACrB;QACA,IAAI,OAAO,OAAO,EAAE;YAChB,OAAO,KAAK,CAAC,IAAI,CAAC;gBACd,SAAS,OAAO,OAAO;gBACvB,GAAI,OAAO,YAAY,IACnB,KAAK,aAAa,IAAI;oBACtB,cAAc;wBAAE,SAAS,OAAO,YAAY,CAAC,OAAO;oBAAC;gBACzD,CAAC;YACL;YACA,OAAO,OAAO,OAAO;YACrB,IAAI,OAAO,YAAY,EAAE;gBACrB,OAAO,OAAO,YAAY,CAAC,OAAO;gBAClC,IAAI,OAAO,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,KAAK,GAAG;oBAC/C,OAAO,OAAO,YAAY;gBAC9B;YACJ;QACJ;QACA,OAAO,KAAK,CAAC,IAAI,CAAC;YACd,SAAS,yBAAyB,OAAO;YACzC,GAAI,WACA,KAAK,aAAa,IAAI;gBAAE,cAAc;oBAAE,SAAS;gBAAQ;YAAE,CAAC;QACpE;IACJ,OACK;QACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,WAAW,yBAAyB,OAAO,OAAO,SAAS;IACjG;AACJ;AACA,wGAAwG;AACxG,SAAS,yBAAyB,KAAK,EAAE,IAAI;IACzC,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,MAAM,KAAK,EAAE;QACvC,OAAO,MAAM,MAAM;IACvB;IACA,0BAA0B;IAC1B,MAAM,QAAQ;QACV,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;QACxB,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;QACxB,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC;IAC5B;IACA,yTAAyT;IACzT,MAAM,SAAS,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,KAAK,MAAM,MAAM;IAClE,IAAI,UAAU;IACd,IAAI,YAAY;IAChB,IAAI,cAAc;IAClB,IAAI,cAAc;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,IAAI,WAAW;YACX,WAAW,MAAM,CAAC,EAAE;YACpB,YAAY;YACZ;QACJ;QACA,IAAI,MAAM,CAAC,EAAE;YACT,IAAI,aAAa;gBACb,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU;oBAC1B,IAAI,aAAa;wBACb,WAAW,MAAM,CAAC,EAAE;wBACpB,WAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,WAAW;wBACtD,cAAc;oBAClB,OACK,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,OAAO,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,UAAU;wBAC7D,WAAW,MAAM,CAAC,EAAE;wBACpB,cAAc;oBAClB,OACK;wBACD,WAAW,GAAG,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI;oBACvD;oBACA;gBACJ;YACJ,OACK,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU;gBAC/B,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,CAAC;gBACrD;YACJ;QACJ;QACA,IAAI,MAAM,CAAC,EAAE;YACT,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;gBACnB,WAAW,CAAC,eAAe,CAAC;gBAC5B;YACJ,OACK,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;gBACxB,WAAW,CAAC,cAAc,CAAC;gBAC3B;YACJ;QACJ;QACA,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,KAAK;YAC9B,WAAW,cAAc,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;YAClE;QACJ;QACA,WAAW,MAAM,CAAC,EAAE;QACpB,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;YACpB,YAAY;QAChB,OACK,IAAI,eAAe,MAAM,CAAC,EAAE,KAAK,KAAK;YACvC,cAAc;QAClB,OACK,IAAI,CAAC,eAAe,MAAM,CAAC,EAAE,KAAK,KAAK;YACxC,cAAc;QAClB;IACJ;IACA,IAAI;QACA,IAAI,OAAO;IACf,EACA,OAAM;QACF,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,qEAAqE,CAAC;QACpJ,OAAO,MAAM,MAAM;IACvB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2816, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/record.js"], "sourcesContent": ["import { ZodFirstPartyTypeKind, } from \"zod\";\nimport { parseDef } from \"../parseDef.js\";\nimport { parseStringDef } from \"./string.js\";\nimport { parseBrandedDef } from \"./branded.js\";\nimport { parseAnyDef } from \"./any.js\";\nexport function parseRecordDef(def, refs) {\n    if (refs.target === \"openAi\") {\n        console.warn(\"Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.\");\n    }\n    if (refs.target === \"openApi3\" &&\n        def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            type: \"object\",\n            required: def.keyType._def.values,\n            properties: def.keyType._def.values.reduce((acc, key) => ({\n                ...acc,\n                [key]: parseDef(def.valueType._def, {\n                    ...refs,\n                    currentPath: [...refs.currentPath, \"properties\", key],\n                }) ?? parseAnyDef(refs),\n            }), {}),\n            additionalProperties: refs.rejectedAdditionalProperties,\n        };\n    }\n    const schema = {\n        type: \"object\",\n        additionalProperties: parseDef(def.valueType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        }) ?? refs.allowedAdditionalProperties,\n    };\n    if (refs.target === \"openApi3\") {\n        return schema;\n    }\n    if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.checks?.length) {\n        const { type, ...keyType } = parseStringDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            ...schema,\n            propertyNames: {\n                enum: def.keyType._def.values,\n            },\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodBranded &&\n        def.keyType._def.type._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.type._def.checks?.length) {\n        const { type, ...keyType } = parseBrandedDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    return schema;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,IAAI,KAAK,MAAM,KAAK,UAAU;QAC1B,QAAQ,IAAI,CAAC;IACjB;IACA,IAAI,KAAK,MAAM,KAAK,cAChB,IAAI,OAAO,EAAE,KAAK,aAAa,oLAAA,CAAA,wBAAqB,CAAC,OAAO,EAAE;QAC9D,OAAO;YACH,MAAM;YACN,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,MAAQ,CAAC;oBACtD,GAAG,GAAG;oBACN,CAAC,IAAI,EAAE,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;wBAChC,GAAG,IAAI;wBACP,aAAa;+<PERSON>A<PERSON>,KAAK,WAAW;4BAAE;4BAAc;yBAAI;oBACzD,MAAM,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;gBACtB,CAAC,GAAG,CAAC;YACL,sBAAsB,KAAK,4BAA4B;QAC3D;IACJ;IACA,MAAM,SAAS;QACX,MAAM;QACN,sBAAsB,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;YAC/C,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAuB;QAC9D,MAAM,KAAK,2BAA2B;IAC1C;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,OAAO;IACX;IACA,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,oLAAA,CAAA,wBAAqB,CAAC,SAAS,IAC9D,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;QACjC,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,GAAG,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QAC9D,OAAO;YACH,GAAG,MAAM;YACT,eAAe;QACnB;IACJ,OACK,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,oLAAA,CAAA,wBAAqB,CAAC,OAAO,EAAE;QACnE,OAAO;YACH,GAAG,MAAM;YACT,eAAe;gBACX,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC;QACJ;IACJ,OACK,IAAI,IAAI,OAAO,EAAE,KAAK,aAAa,oLAAA,CAAA,wBAAqB,CAAC,UAAU,IACpE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS,IACvE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,GAAG,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QAC/D,OAAO;YACH,GAAG,MAAM;YACT,eAAe;QACnB;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2892, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/map.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nimport { parseRecordDef } from \"./record.js\";\nimport { parseAnyDef } from \"./any.js\";\nexport function parseMapDef(def, refs) {\n    if (refs.mapStrategy === \"record\") {\n        return parseRecordDef(def, refs);\n    }\n    const keys = parseDef(def.keyType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"0\"],\n    }) || parseAnyDef(refs);\n    const values = parseDef(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"1\"],\n    }) || parseAnyDef(refs);\n    return {\n        type: \"array\",\n        maxItems: 125,\n        items: {\n            type: \"array\",\n            items: [keys, values],\n            minItems: 2,\n            maxItems: 2,\n        },\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,YAAY,GAAG,EAAE,IAAI;IACjC,IAAI,KAAK,WAAW,KAAK,UAAU;QAC/B,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;IAC/B;IACA,MAAM,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE;QACpC,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;YAAS;SAAI;IAC7D,MAAM,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;IAClB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QACxC,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;YAAS;SAAI;IAC7D,MAAM,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;IAClB,OAAO;QACH,MAAM;QACN,UAAU;QACV,OAAO;YACH,MAAM;YACN,OAAO;gBAAC;gBAAM;aAAO;YACrB,UAAU;YACV,UAAU;QACd;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2943, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/nativeEnum.js"], "sourcesContent": ["export function parseNativeEnumDef(def) {\n    const object = def.values;\n    const actualKeys = Object.keys(def.values).filter((key) => {\n        return typeof object[object[key]] !== \"number\";\n    });\n    const actualValues = actualKeys.map((key) => object[key]);\n    const parsedTypes = Array.from(new Set(actualValues.map((values) => typeof values)));\n    return {\n        type: parsedTypes.length === 1\n            ? parsedTypes[0] === \"string\"\n                ? \"string\"\n                : \"number\"\n            : [\"string\", \"number\"],\n        enum: actualValues,\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,mBAAmB,GAAG;IAClC,MAAM,SAAS,IAAI,MAAM;IACzB,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/C,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;IAC1C;IACA,MAAM,eAAe,WAAW,GAAG,CAAC,CAAC,MAAQ,MAAM,CAAC,IAAI;IACxD,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,SAAW,OAAO;IAC3E,OAAO;QACH,MAAM,YAAY,MAAM,KAAK,IACvB,WAAW,CAAC,EAAE,KAAK,WACf,WACA,WACJ;YAAC;YAAU;SAAS;QAC1B,MAAM;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2967, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/never.js"], "sourcesContent": ["import { parseAnyDef } from \"./any.js\";\nexport function parseNeverDef(refs) {\n    return refs.target === \"openAi\"\n        ? undefined\n        : {\n            not: parseAnyDef({\n                ...refs,\n                currentPath: [...refs.currentPath, \"not\"],\n            }),\n        };\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,cAAc,IAAI;IAC9B,OAAO,KAAK,MAAM,KAAK,WACjB,YACA;QACE,KAAK,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;YACb,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAM;QAC7C;IACJ;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2989, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/null.js"], "sourcesContent": ["export function parseNullDef(refs) {\n    return refs.target === \"openApi3\"\n        ? {\n            enum: [\"null\"],\n            nullable: true,\n        }\n        : {\n            type: \"null\",\n        };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,aAAa,IAAI;IAC7B,OAAO,KAAK,MAAM,KAAK,aACjB;QACE,MAAM;YAAC;SAAO;QACd,UAAU;IACd,IACE;QACE,MAAM;IACV;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3008, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/union.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport const primitiveMappings = {\n    ZodString: \"string\",\n    ZodNumber: \"number\",\n    ZodBigInt: \"integer\",\n    ZodBoolean: \"boolean\",\n    ZodNull: \"null\",\n};\nexport function parseUnionDef(def, refs) {\n    if (refs.target === \"openApi3\")\n        return asAnyOf(def, refs);\n    const options = def.options instanceof Map ? Array.from(def.options.values()) : def.options;\n    // This blocks tries to look ahead a bit to produce nicer looking schemas with type array instead of anyOf.\n    if (options.every((x) => x._def.typeName in primitiveMappings &&\n        (!x._def.checks || !x._def.checks.length))) {\n        // all types in union are primitive and lack checks, so might as well squash into {type: [...]}\n        const types = options.reduce((types, x) => {\n            const type = primitiveMappings[x._def.typeName]; //Can be safely casted due to row 43\n            return type && !types.includes(type) ? [...types, type] : types;\n        }, []);\n        return {\n            type: types.length > 1 ? types : types[0],\n        };\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodLiteral\" && !x.description)) {\n        // all options literals\n        const types = options.reduce((acc, x) => {\n            const type = typeof x._def.value;\n            switch (type) {\n                case \"string\":\n                case \"number\":\n                case \"boolean\":\n                    return [...acc, type];\n                case \"bigint\":\n                    return [...acc, \"integer\"];\n                case \"object\":\n                    if (x._def.value === null)\n                        return [...acc, \"null\"];\n                case \"symbol\":\n                case \"undefined\":\n                case \"function\":\n                default:\n                    return acc;\n            }\n        }, []);\n        if (types.length === options.length) {\n            // all the literals are primitive, as far as null can be considered primitive\n            const uniqueTypes = types.filter((x, i, a) => a.indexOf(x) === i);\n            return {\n                type: uniqueTypes.length > 1 ? uniqueTypes : uniqueTypes[0],\n                enum: options.reduce((acc, x) => {\n                    return acc.includes(x._def.value) ? acc : [...acc, x._def.value];\n                }, []),\n            };\n        }\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodEnum\")) {\n        return {\n            type: \"string\",\n            enum: options.reduce((acc, x) => [\n                ...acc,\n                ...x._def.values.filter((x) => !acc.includes(x)),\n            ], []),\n        };\n    }\n    return asAnyOf(def, refs);\n}\nconst asAnyOf = (def, refs) => {\n    const anyOf = (def.options instanceof Map\n        ? Array.from(def.options.values())\n        : def.options)\n        .map((x, i) => parseDef(x._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", `${i}`],\n    }))\n        .filter((x) => !!x &&\n        (!refs.strictUnions ||\n            (typeof x === \"object\" && Object.keys(x).length > 0)));\n    return anyOf.length ? { anyOf } : undefined;\n};\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB;IAC7B,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;AACb;AACO,SAAS,cAAc,GAAG,EAAE,IAAI;IACnC,IAAI,KAAK,MAAM,KAAK,YAChB,OAAO,QAAQ,KAAK;IACxB,MAAM,UAAU,IAAI,OAAO,YAAY,MAAM,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,IAAI,OAAO;IAC3F,2GAA2G;IAC3G,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,IAAI,qBACxC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI;QAC5C,+FAA+F;QAC/F,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC,OAAO;YACjC,MAAM,OAAO,iBAAiB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,oCAAoC;YACrF,OAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,QAAQ;mBAAI;gBAAO;aAAK,GAAG;QAC9D,GAAG,EAAE;QACL,OAAO;YACH,MAAM,MAAM,MAAM,GAAG,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC7C;IACJ,OACK,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,gBAAgB,CAAC,EAAE,WAAW,GAAG;QAC/E,uBAAuB;QACvB,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC,KAAK;YAC/B,MAAM,OAAO,OAAO,EAAE,IAAI,CAAC,KAAK;YAChC,OAAQ;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,OAAO;2BAAI;wBAAK;qBAAK;gBACzB,KAAK;oBACD,OAAO;2BAAI;wBAAK;qBAAU;gBAC9B,KAAK;oBACD,IAAI,EAAE,IAAI,CAAC,KAAK,KAAK,MACjB,OAAO;2BAAI;wBAAK;qBAAO;gBAC/B,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL;oBACI,OAAO;YACf;QACJ,GAAG,EAAE;QACL,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;YACjC,6EAA6E;YAC7E,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,GAAG,GAAG,IAAM,EAAE,OAAO,CAAC,OAAO;YAC/D,OAAO;gBACH,MAAM,YAAY,MAAM,GAAG,IAAI,cAAc,WAAW,CAAC,EAAE;gBAC3D,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK;oBACvB,OAAO,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,MAAM;2BAAI;wBAAK,EAAE,IAAI,CAAC,KAAK;qBAAC;gBACpE,GAAG,EAAE;YACT;QACJ;IACJ,OACK,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,YAAY;QAC1D,OAAO;YACH,MAAM;YACN,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM;uBAC1B;uBACA,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,IAAI,QAAQ,CAAC;iBAChD,EAAE,EAAE;QACT;IACJ;IACA,OAAO,QAAQ,KAAK;AACxB;AACA,MAAM,UAAU,CAAC,KAAK;IAClB,MAAM,QAAQ,CAAC,IAAI,OAAO,YAAY,MAChC,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,MAC7B,IAAI,OAAO,EACZ,GAAG,CAAC,CAAC,GAAG,IAAM,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,IAAI,EAAE;YAChC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAS,GAAG,GAAG;aAAC;QACvD,IACK,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,KACjB,CAAC,CAAC,KAAK,YAAY,IACd,OAAO,MAAM,YAAY,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAE;IAC5D,OAAO,MAAM,MAAM,GAAG;QAAE;IAAM,IAAI;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/nullable.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nimport { primitiveMappings } from \"./union.js\";\nexport function parseNullableDef(def, refs) {\n    if ([\"ZodString\", \"ZodNumber\", \"ZodBigInt\", \"ZodBoolean\", \"ZodNull\"].includes(def.innerType._def.typeName) &&\n        (!def.innerType._def.checks || !def.innerType._def.checks.length)) {\n        if (refs.target === \"openApi3\") {\n            return {\n                type: primitiveMappings[def.innerType._def.typeName],\n                nullable: true,\n            };\n        }\n        return {\n            type: [\n                primitiveMappings[def.innerType._def.typeName],\n                \"null\",\n            ],\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        const base = parseDef(def.innerType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath],\n        });\n        if (base && \"$ref\" in base)\n            return { allOf: [base], nullable: true };\n        return base && { ...base, nullable: true };\n    }\n    const base = parseDef(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"0\"],\n    });\n    return base && { anyOf: [base, { type: \"null\" }] };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,iBAAiB,GAAG,EAAE,IAAI;IACtC,IAAI;QAAC;QAAa;QAAa;QAAa;QAAc;KAAU,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,KACrG,CAAC,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACnE,IAAI,KAAK,MAAM,KAAK,YAAY;YAC5B,OAAO;gBACH,MAAM,4QAAA,CAAA,oBAAiB,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACpD,UAAU;YACd;QACJ;QACA,OAAO;YACH,MAAM;gBACF,4QAAA,CAAA,oBAAiB,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC9C;aACH;QACL;IACJ;IACA,IAAI,KAAK,MAAM,KAAK,YAAY;QAC5B,MAAM,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;YACtC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;aAAC;QACtC;QACA,IAAI,QAAQ,UAAU,MAClB,OAAO;YAAE,OAAO;gBAAC;aAAK;YAAE,UAAU;QAAK;QAC3C,OAAO,QAAQ;YAAE,GAAG,IAAI;YAAE,UAAU;QAAK;IAC7C;IACA,MAAM,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QACtC,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,OAAO,QAAQ;QAAE,OAAO;YAAC;YAAM;gBAAE,MAAM;YAAO;SAAE;IAAC;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3178, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/number.js"], "sourcesContent": ["import { addErrorMessage, setResponseValueAndErrors, } from \"../errorMessages.js\";\nexport function parseNumberDef(def, refs) {\n    const res = {\n        type: \"number\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"int\":\n                res.type = \"integer\";\n                addErrorMessage(res, \"type\", check.message, refs);\n                break;\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                setResponseValueAndErrors(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,MAAM,MAAM;QACR,MAAM;IACV;IACA,IAAI,CAAC,IAAI,MAAM,EACX,OAAO;IACX,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC5B,OAAQ,MAAM,IAAI;YACd,KAAK;gBACD,IAAI,IAAI,GAAG;gBACX,CAAA,GAAA,yQAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,MAAM,OAAO,EAAE;gBAC5C;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC1E,OACK;wBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBACnF;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAC1E;gBACA;YACJ,KAAK;gBACD,IAAI,KAAK,MAAM,KAAK,eAAe;oBAC/B,IAAI,MAAM,SAAS,EAAE;wBACjB,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBAC1E,OACK;wBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,oBAAoB,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;oBACnF;gBACJ,OACK;oBACD,IAAI,CAAC,MAAM,SAAS,EAAE;wBAClB,IAAI,gBAAgB,GAAG;oBAC3B;oBACA,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,WAAW,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBAC1E;gBACA;YACJ,KAAK;gBACD,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,cAAc,MAAM,KAAK,EAAE,MAAM,OAAO,EAAE;gBACzE;QACR;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3235, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/object.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport function parseObjectDef(def, refs) {\n    const forceOptionalIntoNullable = refs.target === \"openAi\";\n    const result = {\n        type: \"object\",\n        properties: {},\n    };\n    const required = [];\n    const shape = def.shape();\n    for (const propName in shape) {\n        let propDef = shape[propName];\n        if (propDef === undefined || propDef._def === undefined) {\n            continue;\n        }\n        let propOptional = safeIsOptional(propDef);\n        if (propOptional && forceOptionalIntoNullable) {\n            if (propDef._def.typeName === \"ZodOptional\") {\n                propDef = propDef._def.innerType;\n            }\n            if (!propDef.isNullable()) {\n                propDef = propDef.nullable();\n            }\n            propOptional = false;\n        }\n        const parsedDef = parseDef(propDef._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"properties\", propName],\n            propertyPath: [...refs.currentPath, \"properties\", propName],\n        });\n        if (parsedDef === undefined) {\n            continue;\n        }\n        result.properties[propName] = parsedDef;\n        if (!propOptional) {\n            required.push(propName);\n        }\n    }\n    if (required.length) {\n        result.required = required;\n    }\n    const additionalProperties = decideAdditionalProperties(def, refs);\n    if (additionalProperties !== undefined) {\n        result.additionalProperties = additionalProperties;\n    }\n    return result;\n}\nfunction decideAdditionalProperties(def, refs) {\n    if (def.catchall._def.typeName !== \"ZodNever\") {\n        return parseDef(def.catchall._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        });\n    }\n    switch (def.unknownKeys) {\n        case \"passthrough\":\n            return refs.allowedAdditionalProperties;\n        case \"strict\":\n            return refs.rejectedAdditionalProperties;\n        case \"strip\":\n            return refs.removeAdditionalStrategy === \"strict\"\n                ? refs.allowedAdditionalProperties\n                : refs.rejectedAdditionalProperties;\n    }\n}\nfunction safeIsOptional(schema) {\n    try {\n        return schema.isOptional();\n    }\n    catch {\n        return true;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,GAAG,EAAE,IAAI;IACpC,MAAM,4BAA4B,KAAK,MAAM,KAAK;IAClD,MAAM,SAAS;QACX,MAAM;QACN,YAAY,CAAC;IACjB;IACA,MAAM,WAAW,EAAE;IACnB,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAK,MAAM,YAAY,MAAO;QAC1B,IAAI,UAAU,KAAK,CAAC,SAAS;QAC7B,IAAI,YAAY,aAAa,QAAQ,IAAI,KAAK,WAAW;YACrD;QACJ;QACA,IAAI,eAAe,eAAe;QAClC,IAAI,gBAAgB,2BAA2B;YAC3C,IAAI,QAAQ,IAAI,CAAC,QAAQ,KAAK,eAAe;gBACzC,UAAU,QAAQ,IAAI,CAAC,SAAS;YACpC;YACA,IAAI,CAAC,QAAQ,UAAU,IAAI;gBACvB,UAAU,QAAQ,QAAQ;YAC9B;YACA,eAAe;QACnB;QACA,MAAM,YAAY,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,EAAE;YACrC,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;gBAAc;aAAS;YAC1D,cAAc;mBAAI,KAAK,WAAW;gBAAE;gBAAc;aAAS;QAC/D;QACA,IAAI,cAAc,WAAW;YACzB;QACJ;QACA,OAAO,UAAU,CAAC,SAAS,GAAG;QAC9B,IAAI,CAAC,cAAc;YACf,SAAS,IAAI,CAAC;QAClB;IACJ;IACA,IAAI,SAAS,MAAM,EAAE;QACjB,OAAO,QAAQ,GAAG;IACtB;IACA,MAAM,uBAAuB,2BAA2B,KAAK;IAC7D,IAAI,yBAAyB,WAAW;QACpC,OAAO,oBAAoB,GAAG;IAClC;IACA,OAAO;AACX;AACA,SAAS,2BAA2B,GAAG,EAAE,IAAI;IACzC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,KAAK,YAAY;QAC3C,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE;YAC/B,GAAG,IAAI;YACP,aAAa;mBAAI,KAAK,WAAW;gBAAE;aAAuB;QAC9D;IACJ;IACA,OAAQ,IAAI,WAAW;QACnB,KAAK;YACD,OAAO,KAAK,2BAA2B;QAC3C,KAAK;YACD,OAAO,KAAK,4BAA4B;QAC5C,KAAK;YACD,OAAO,KAAK,wBAAwB,KAAK,WACnC,KAAK,2BAA2B,GAChC,KAAK,4BAA4B;IAC/C;AACJ;AACA,SAAS,eAAe,MAAM;IAC1B,IAAI;QACA,OAAO,OAAO,UAAU;IAC5B,EACA,OAAM;QACF,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3325, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/optional.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nimport { parseAnyDef } from \"./any.js\";\nexport const parseOptionalDef = (def, refs) => {\n    if (refs.currentPath.toString() === refs.propertyPath?.toString()) {\n        return parseDef(def.innerType._def, refs);\n    }\n    const innerSchema = parseDef(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"1\"],\n    });\n    return innerSchema\n        ? {\n            anyOf: [\n                {\n                    not: parseAnyDef(refs),\n                },\n                innerSchema,\n            ],\n        }\n        : parseAnyDef(refs);\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,mBAAmB,CAAC,KAAK;IAClC,IAAI,KAAK,WAAW,CAAC,QAAQ,OAAO,KAAK,YAAY,EAAE,YAAY;QAC/D,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;IACxC;IACA,MAAM,cAAc,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QAC7C,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,OAAO,cACD;QACE,OAAO;YACH;gBACI,KAAK,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;YACrB;YACA;SACH;IACL,IACE,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3359, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/pipeline.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport const parsePipelineDef = (def, refs) => {\n    if (refs.pipeStrategy === \"input\") {\n        return parseDef(def.in._def, refs);\n    }\n    else if (refs.pipeStrategy === \"output\") {\n        return parseDef(def.out._def, refs);\n    }\n    const a = parseDef(def.in._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n    });\n    const b = parseDef(def.out._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", a ? \"1\" : \"0\"],\n    });\n    return {\n        allOf: [a, b].filter((x) => x !== undefined),\n    };\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAC,KAAK;IAClC,IAAI,KAAK,YAAY,KAAK,SAAS;QAC/B,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE;IACjC,OACK,IAAI,KAAK,YAAY,KAAK,UAAU;QACrC,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;IAClC;IACA,MAAM,IAAI,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE;QAC5B,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS;SAAI;IACpD;IACA,MAAM,IAAI,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;QAC7B,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;YAAS,IAAI,MAAM;SAAI;IAC9D;IACA,OAAO;QACH,OAAO;YAAC;YAAG;SAAE,CAAC,MAAM,CAAC,CAAC,IAAM,MAAM;IACtC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3399, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/promise.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport function parsePromiseDef(def, refs) {\n    return parseDef(def.type._def, refs);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,gBAAgB,GAAG,EAAE,IAAI;IACrC,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3413, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/set.js"], "sourcesContent": ["import { setResponseValueAndErrors } from \"../errorMessages.js\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseSetDef(def, refs) {\n    const items = parseDef(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\"],\n    });\n    const schema = {\n        type: \"array\",\n        uniqueItems: true,\n        items,\n    };\n    if (def.minSize) {\n        setResponseValueAndErrors(schema, \"minItems\", def.minSize.value, def.minSize.message, refs);\n    }\n    if (def.maxSize) {\n        setResponseValueAndErrors(schema, \"maxItems\", def.maxSize.value, def.maxSize.message, refs);\n    }\n    return schema;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,YAAY,GAAG,EAAE,IAAI;IACjC,MAAM,QAAQ,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;QACvC,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,WAAW;YAAE;SAAQ;IAC/C;IACA,MAAM,SAAS;QACX,MAAM;QACN,aAAa;QACb;IACJ;IACA,IAAI,IAAI,OAAO,EAAE;QACb,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;IAC1F;IACA,IAAI,IAAI,OAAO,EAAE;QACb,CAAA,GAAA,yQAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;IAC1F;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/tuple.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport function parseTupleDef(def, refs) {\n    if (def.rest) {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            items: def.items\n                .map((x, i) => parseDef(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n            additionalItems: parseDef(def.rest._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"additionalItems\"],\n            }),\n        };\n    }\n    else {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            maxItems: def.items.length,\n            items: def.items\n                .map((x, i) => parseDef(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n        };\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,cAAc,GAAG,EAAE,IAAI;IACnC,IAAI,IAAI,IAAI,EAAE;QACV,OAAO;YACH,MAAM;YACN,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,OAAO,IAAI,KAAK,CACX,GAAG,CAAC,CAAC,GAAG,IAAM,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,IAAI,EAAE;oBAChC,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;wBAAE;wBAAS,GAAG,GAAG;qBAAC;gBACvD,IACK,MAAM,CAAC,CAAC,KAAK,IAAO,MAAM,YAAY,MAAM;uBAAI;oBAAK;iBAAE,EAAG,EAAE;YACjE,iBAAiB,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;gBACrC,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,WAAW;oBAAE;iBAAkB;YACzD;QACJ;IACJ,OACK;QACD,OAAO;YACH,MAAM;YACN,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,UAAU,IAAI,KAAK,CAAC,MAAM;YAC1B,OAAO,IAAI,KAAK,CACX,GAAG,CAAC,CAAC,GAAG,IAAM,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,IAAI,EAAE;oBAChC,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;wBAAE;wBAAS,GAAG,GAAG;qBAAC;gBACvD,IACK,MAAM,CAAC,CAAC,KAAK,IAAO,MAAM,YAAY,MAAM;uBAAI;oBAAK;iBAAE,EAAG,EAAE;QACrE;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/undefined.js"], "sourcesContent": ["import { parseAnyDef } from \"./any.js\";\nexport function parseUndefinedDef(refs) {\n    return {\n        not: parseAnyDef(refs),\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,kBAAkB,IAAI;IAClC,OAAO;QACH,KAAK,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3517, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/unknown.js"], "sourcesContent": ["import { parseAnyDef } from \"./any.js\";\nexport function parseUnknownDef(refs) {\n    return parseAnyDef(refs);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,gBAAgB,IAAI;IAChC,OAAO,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3531, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parsers/readonly.js"], "sourcesContent": ["import { parseDef } from \"../parseDef.js\";\nexport const parseReadonlyDef = (def, refs) => {\n    return parseDef(def.innerType._def, refs);\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAC,KAAK;IAClC,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3545, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/selectParser.js"], "sourcesContent": ["import { Zod<PERSON>irstPartyTypeKind } from \"zod\";\nimport { parseAnyDef } from \"./parsers/any.js\";\nimport { parseArrayDef } from \"./parsers/array.js\";\nimport { parseBigintDef } from \"./parsers/bigint.js\";\nimport { parseBooleanDef } from \"./parsers/boolean.js\";\nimport { parseBrandedDef } from \"./parsers/branded.js\";\nimport { parseCatchDef } from \"./parsers/catch.js\";\nimport { parseDateDef } from \"./parsers/date.js\";\nimport { parseDefaultDef } from \"./parsers/default.js\";\nimport { parseEffectsDef } from \"./parsers/effects.js\";\nimport { parseEnumDef } from \"./parsers/enum.js\";\nimport { parseIntersectionDef } from \"./parsers/intersection.js\";\nimport { parseLiteralDef } from \"./parsers/literal.js\";\nimport { parseMapDef } from \"./parsers/map.js\";\nimport { parseNativeEnumDef } from \"./parsers/nativeEnum.js\";\nimport { parseNeverDef } from \"./parsers/never.js\";\nimport { parseNullDef } from \"./parsers/null.js\";\nimport { parseNullableDef } from \"./parsers/nullable.js\";\nimport { parseNumberDef } from \"./parsers/number.js\";\nimport { parseObjectDef } from \"./parsers/object.js\";\nimport { parseOptionalDef } from \"./parsers/optional.js\";\nimport { parsePipelineDef } from \"./parsers/pipeline.js\";\nimport { parsePromiseDef } from \"./parsers/promise.js\";\nimport { parseRecordDef } from \"./parsers/record.js\";\nimport { parseSetDef } from \"./parsers/set.js\";\nimport { parseStringDef } from \"./parsers/string.js\";\nimport { parseTupleDef } from \"./parsers/tuple.js\";\nimport { parseUndefinedDef } from \"./parsers/undefined.js\";\nimport { parseUnionDef } from \"./parsers/union.js\";\nimport { parseUnknownDef } from \"./parsers/unknown.js\";\nimport { parseReadonlyDef } from \"./parsers/readonly.js\";\nexport const selectParser = (def, typeName, refs) => {\n    switch (typeName) {\n        case ZodFirstPartyTypeKind.ZodString:\n            return parseStringDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNumber:\n            return parseNumberDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodObject:\n            return parseObjectDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBigInt:\n            return parseBigintDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBoolean:\n            return parseBooleanDef();\n        case ZodFirstPartyTypeKind.ZodDate:\n            return parseDateDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUndefined:\n            return parseUndefinedDef(refs);\n        case ZodFirstPartyTypeKind.ZodNull:\n            return parseNullDef(refs);\n        case ZodFirstPartyTypeKind.ZodArray:\n            return parseArrayDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUnion:\n        case ZodFirstPartyTypeKind.ZodDiscriminatedUnion:\n            return parseUnionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodIntersection:\n            return parseIntersectionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodTuple:\n            return parseTupleDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodRecord:\n            return parseRecordDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLiteral:\n            return parseLiteralDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodEnum:\n            return parseEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNativeEnum:\n            return parseNativeEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNullable:\n            return parseNullableDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodOptional:\n            return parseOptionalDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodMap:\n            return parseMapDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodSet:\n            return parseSetDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLazy:\n            return () => def.getter()._def;\n        case ZodFirstPartyTypeKind.ZodPromise:\n            return parsePromiseDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNaN:\n        case ZodFirstPartyTypeKind.ZodNever:\n            return parseNeverDef(refs);\n        case ZodFirstPartyTypeKind.ZodEffects:\n            return parseEffectsDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodAny:\n            return parseAnyDef(refs);\n        case ZodFirstPartyTypeKind.ZodUnknown:\n            return parseUnknownDef(refs);\n        case ZodFirstPartyTypeKind.ZodDefault:\n            return parseDefaultDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBranded:\n            return parseBrandedDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodReadonly:\n            return parseReadonlyDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodCatch:\n            return parseCatchDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodPipeline:\n            return parsePipelineDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodFunction:\n        case ZodFirstPartyTypeKind.ZodVoid:\n        case ZodFirstPartyTypeKind.ZodSymbol:\n            return undefined;\n        default:\n            /* c8 ignore next */\n            return ((_) => undefined)(typeName);\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,MAAM,eAAe,CAAC,KAAK,UAAU;IACxC,OAAQ;QACJ,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD;QACzB,KAAK,oLAAA,CAAA,wBAAqB,CAAC,OAAO;YAC9B,OAAO,CAAA,GAAA,2QAAA,CAAA,eAAY,AAAD,EAAE,KAAK;QAC7B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,YAAY;YACnC,OAAO,CAAA,GAAA,gRAAA,CAAA,oBAAiB,AAAD,EAAE;QAC7B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,OAAO;YAC9B,OAAO,CAAA,GAAA,2QAAA,CAAA,eAAY,AAAD,EAAE;QACxB,KAAK,oLAAA,CAAA,wBAAqB,CAAC,QAAQ;YAC/B,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAC9B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,QAAQ;QACnC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,qBAAqB;YAC5C,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAC9B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,eAAe;YACtC,OAAO,CAAA,GAAA,mRAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;QACrC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,QAAQ;YAC/B,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAC9B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6QAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QAChC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,OAAO;YAC9B,OAAO,CAAA,GAAA,2QAAA,CAAA,eAAY,AAAD,EAAE;QACxB,KAAK,oLAAA,CAAA,wBAAqB,CAAC,aAAa;YACpC,OAAO,CAAA,GAAA,iRAAA,CAAA,qBAAkB,AAAD,EAAE;QAC9B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,WAAW;YAClC,OAAO,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;QACjC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,WAAW;YAClC,OAAO,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;QACjC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,MAAM;YAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE,KAAK;QAC5B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,MAAM;YAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE,KAAK;QAC5B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,OAAO;YAC9B,OAAO,IAAM,IAAI,MAAM,GAAG,IAAI;QAClC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QAChC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,MAAM;QACjC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,QAAQ;YAC/B,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE;QACzB,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QAChC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,MAAM;YAC7B,OAAO,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;QACvB,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QAChC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,UAAU;YACjC,OAAO,CAAA,GAAA,8QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;QAChC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,WAAW;YAClC,OAAO,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;QACjC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,QAAQ;YAC/B,OAAO,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QAC9B,KAAK,oLAAA,CAAA,wBAAqB,CAAC,WAAW;YAClC,OAAO,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;QACjC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,WAAW;QACtC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,OAAO;QAClC,KAAK,oLAAA,CAAA,wBAAqB,CAAC,SAAS;YAChC,OAAO;QACX;YACI,kBAAkB,GAClB,OAAO,CAAC,CAAC,IAAM,SAAS,EAAE;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3690, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/parseDef.js"], "sourcesContent": ["import { ignoreOverride } from \"./Options.js\";\nimport { selectParser } from \"./selectParser.js\";\nimport { getRelativePath } from \"./getRelativePath.js\";\nimport { parseAnyDef } from \"./parsers/any.js\";\nexport function parseDef(def, refs, forceResolution = false) {\n    const seenItem = refs.seen.get(def);\n    if (refs.override) {\n        const overrideResult = refs.override?.(def, refs, seenItem, forceResolution);\n        if (overrideResult !== ignoreOverride) {\n            return overrideResult;\n        }\n    }\n    if (seenItem && !forceResolution) {\n        const seenSchema = get$ref(seenItem, refs);\n        if (seenSchema !== undefined) {\n            return seenSchema;\n        }\n    }\n    const newItem = { def, path: refs.currentPath, jsonSchema: undefined };\n    refs.seen.set(def, newItem);\n    const jsonSchemaOrGetter = selectParser(def, def.typeName, refs);\n    // If the return was a function, then the inner definition needs to be extracted before a call to parseDef (recursive)\n    const jsonSchema = typeof jsonSchemaOrGetter === \"function\"\n        ? parseDef(jsonSchemaOrGetter(), refs)\n        : jsonSchemaOrGetter;\n    if (jsonSchema) {\n        addMeta(def, refs, jsonSchema);\n    }\n    if (refs.postProcess) {\n        const postProcessResult = refs.postProcess(jsonSchema, def, refs);\n        newItem.jsonSchema = jsonSchema;\n        return postProcessResult;\n    }\n    newItem.jsonSchema = jsonSchema;\n    return jsonSchema;\n}\nconst get$ref = (item, refs) => {\n    switch (refs.$refStrategy) {\n        case \"root\":\n            return { $ref: item.path.join(\"/\") };\n        case \"relative\":\n            return { $ref: getRelativePath(refs.currentPath, item.path) };\n        case \"none\":\n        case \"seen\": {\n            if (item.path.length < refs.currentPath.length &&\n                item.path.every((value, index) => refs.currentPath[index] === value)) {\n                console.warn(`Recursive reference detected at ${refs.currentPath.join(\"/\")}! Defaulting to any`);\n                return parseAnyDef(refs);\n            }\n            return refs.$refStrategy === \"seen\" ? parseAnyDef(refs) : undefined;\n        }\n    }\n};\nconst addMeta = (def, refs, jsonSchema) => {\n    if (def.description) {\n        jsonSchema.description = def.description;\n        if (refs.markdownDescription) {\n            jsonSchema.markdownDescription = def.description;\n        }\n    }\n    return jsonSchema;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,SAAS,GAAG,EAAE,IAAI,EAAE,kBAAkB,KAAK;IACvD,MAAM,WAAW,KAAK,IAAI,CAAC,GAAG,CAAC;IAC/B,IAAI,KAAK,QAAQ,EAAE;QACf,MAAM,iBAAiB,KAAK,QAAQ,GAAG,KAAK,MAAM,UAAU;QAC5D,IAAI,mBAAmB,mQAAA,CAAA,iBAAc,EAAE;YACnC,OAAO;QACX;IACJ;IACA,IAAI,YAAY,CAAC,iBAAiB;QAC9B,MAAM,aAAa,QAAQ,UAAU;QACrC,IAAI,eAAe,WAAW;YAC1B,OAAO;QACX;IACJ;IACA,MAAM,UAAU;QAAE;QAAK,MAAM,KAAK,WAAW;QAAE,YAAY;IAAU;IACrE,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK;IACnB,MAAM,qBAAqB,CAAA,GAAA,wQAAA,CAAA,eAAY,AAAD,EAAE,KAAK,IAAI,QAAQ,EAAE;IAC3D,sHAAsH;IACtH,MAAM,aAAa,OAAO,uBAAuB,aAC3C,SAAS,sBAAsB,QAC/B;IACN,IAAI,YAAY;QACZ,QAAQ,KAAK,MAAM;IACvB;IACA,IAAI,KAAK,WAAW,EAAE;QAClB,MAAM,oBAAoB,KAAK,WAAW,CAAC,YAAY,KAAK;QAC5D,QAAQ,UAAU,GAAG;QACrB,OAAO;IACX;IACA,QAAQ,UAAU,GAAG;IACrB,OAAO;AACX;AACA,MAAM,UAAU,CAAC,MAAM;IACnB,OAAQ,KAAK,YAAY;QACrB,KAAK;YACD,OAAO;gBAAE,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC;YAAK;QACvC,KAAK;YACD,OAAO;gBAAE,MAAM,CAAA,GAAA,2QAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,IAAI;YAAE;QAChE,KAAK;QACL,KAAK;YAAQ;gBACT,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,IAC1C,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,QAAU,KAAK,WAAW,CAAC,MAAM,KAAK,QAAQ;oBACtE,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,mBAAmB,CAAC;oBAC/F,OAAO,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;gBACvB;gBACA,OAAO,KAAK,YAAY,KAAK,SAAS,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;YAC9D;IACJ;AACJ;AACA,MAAM,UAAU,CAAC,KAAK,MAAM;IACxB,IAAI,IAAI,WAAW,EAAE;QACjB,WAAW,WAAW,GAAG,IAAI,WAAW;QACxC,IAAI,KAAK,mBAAmB,EAAE;YAC1B,WAAW,mBAAmB,GAAG,IAAI,WAAW;QACpD;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3771, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3779, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/zodToJsonSchema.js"], "sourcesContent": ["import { parseDef } from \"./parseDef.js\";\nimport { getRefs } from \"./Refs.js\";\nimport { parseAnyDef } from \"./parsers/any.js\";\nconst zodToJsonSchema = (schema, options) => {\n    const refs = getRefs(options);\n    let definitions = typeof options === \"object\" && options.definitions\n        ? Object.entries(options.definitions).reduce((acc, [name, schema]) => ({\n            ...acc,\n            [name]: parseDef(schema._def, {\n                ...refs,\n                currentPath: [...refs.basePath, refs.definitionPath, name],\n            }, true) ?? parseAnyDef(refs),\n        }), {})\n        : undefined;\n    const name = typeof options === \"string\"\n        ? options\n        : options?.nameStrategy === \"title\"\n            ? undefined\n            : options?.name;\n    const main = parseDef(schema._def, name === undefined\n        ? refs\n        : {\n            ...refs,\n            currentPath: [...refs.basePath, refs.definitionPath, name],\n        }, false) ?? parseAnyDef(refs);\n    const title = typeof options === \"object\" &&\n        options.name !== undefined &&\n        options.nameStrategy === \"title\"\n        ? options.name\n        : undefined;\n    if (title !== undefined) {\n        main.title = title;\n    }\n    if (refs.flags.hasReferencedOpenAiAnyType) {\n        if (!definitions) {\n            definitions = {};\n        }\n        if (!definitions[refs.openAiAnyTypeName]) {\n            definitions[refs.openAiAnyTypeName] = {\n                // Skipping \"object\" as no properties can be defined and additionalProperties must be \"false\"\n                type: [\"string\", \"number\", \"integer\", \"boolean\", \"array\", \"null\"],\n                items: {\n                    $ref: refs.$refStrategy === \"relative\"\n                        ? \"1\"\n                        : [\n                            ...refs.basePath,\n                            refs.definitionPath,\n                            refs.openAiAnyTypeName,\n                        ].join(\"/\"),\n                },\n            };\n        }\n    }\n    const combined = name === undefined\n        ? definitions\n            ? {\n                ...main,\n                [refs.definitionPath]: definitions,\n            }\n            : main\n        : {\n            $ref: [\n                ...(refs.$refStrategy === \"relative\" ? [] : refs.basePath),\n                refs.definitionPath,\n                name,\n            ].join(\"/\"),\n            [refs.definitionPath]: {\n                ...definitions,\n                [name]: main,\n            },\n        };\n    if (refs.target === \"jsonSchema7\") {\n        combined.$schema = \"http://json-schema.org/draft-07/schema#\";\n    }\n    else if (refs.target === \"jsonSchema2019-09\" || refs.target === \"openAi\") {\n        combined.$schema = \"https://json-schema.org/draft/2019-09/schema#\";\n    }\n    if (refs.target === \"openAi\" &&\n        (\"anyOf\" in combined ||\n            \"oneOf\" in combined ||\n            \"allOf\" in combined ||\n            (\"type\" in combined && Array.isArray(combined.type)))) {\n        console.warn(\"Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.\");\n    }\n    return combined;\n};\nexport { zodToJsonSchema };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,MAAM,kBAAkB,CAAC,QAAQ;IAC7B,MAAM,OAAO,CAAA,GAAA,gQAAA,CAAA,UAAO,AAAD,EAAE;IACrB,IAAI,cAAc,OAAO,YAAY,YAAY,QAAQ,WAAW,GAC9D,OAAO,OAAO,CAAC,QAAQ,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,OAAO,GAAK,CAAC;YACnE,GAAG,GAAG;YACN,CAAC,KAAK,EAAE,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,IAAI,EAAE;gBAC1B,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,QAAQ;oBAAE,KAAK,cAAc;oBAAE;iBAAK;YAC9D,GAAG,SAAS,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,CAAC,GAAG,CAAC,KACH;IACN,MAAM,OAAO,OAAO,YAAY,WAC1B,UACA,SAAS,iBAAiB,UACtB,YACA,SAAS;IACnB,MAAM,OAAO,CAAA,GAAA,oQAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,IAAI,EAAE,SAAS,YACtC,OACA;QACE,GAAG,IAAI;QACP,aAAa;eAAI,KAAK,QAAQ;YAAE,KAAK,cAAc;YAAE;SAAK;IAC9D,GAAG,UAAU,CAAA,GAAA,0QAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,QAAQ,OAAO,YAAY,YAC7B,QAAQ,IAAI,KAAK,aACjB,QAAQ,YAAY,KAAK,UACvB,QAAQ,IAAI,GACZ;IACN,IAAI,UAAU,WAAW;QACrB,KAAK,KAAK,GAAG;IACjB;IACA,IAAI,KAAK,KAAK,CAAC,0BAA0B,EAAE;QACvC,IAAI,CAAC,aAAa;YACd,cAAc,CAAC;QACnB;QACA,IAAI,CAAC,WAAW,CAAC,KAAK,iBAAiB,CAAC,EAAE;YACtC,WAAW,CAAC,KAAK,iBAAiB,CAAC,GAAG;gBAClC,6FAA6F;gBAC7F,MAAM;oBAAC;oBAAU;oBAAU;oBAAW;oBAAW;oBAAS;iBAAO;gBACjE,OAAO;oBACH,MAAM,KAAK,YAAY,KAAK,aACtB,MACA;2BACK,KAAK,QAAQ;wBAChB,KAAK,cAAc;wBACnB,KAAK,iBAAiB;qBACzB,CAAC,IAAI,CAAC;gBACf;YACJ;QACJ;IACJ;IACA,MAAM,WAAW,SAAS,YACpB,cACI;QACE,GAAG,IAAI;QACP,CAAC,KAAK,cAAc,CAAC,EAAE;IAC3B,IACE,OACJ;QACE,MAAM;eACE,KAAK,YAAY,KAAK,aAAa,EAAE,GAAG,KAAK,QAAQ;YACzD,KAAK,cAAc;YACnB;SACH,CAAC,IAAI,CAAC;QACP,CAAC,KAAK,cAAc,CAAC,EAAE;YACnB,GAAG,WAAW;YACd,CAAC,KAAK,EAAE;QACZ;IACJ;IACJ,IAAI,KAAK,MAAM,KAAK,eAAe;QAC/B,SAAS,OAAO,GAAG;IACvB,OACK,IAAI,KAAK,MAAM,KAAK,uBAAuB,KAAK,MAAM,KAAK,UAAU;QACtE,SAAS,OAAO,GAAG;IACvB;IACA,IAAI,KAAK,MAAM,KAAK,YAChB,CAAC,WAAW,YACR,WAAW,YACX,WAAW,YACV,UAAU,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,CAAE,GAAG;QAC3D,QAAQ,IAAI,CAAC;IACjB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Working/Projects/NovelEditor/frontend/node_modules/.pnpm/zod-to-json-schema%403.24.6_zod%403.25.76/node_modules/zod-to-json-schema/dist/esm/index.js"], "sourcesContent": ["export * from \"./Options.js\";\nexport * from \"./Refs.js\";\nexport * from \"./errorMessages.js\";\nexport * from \"./getRelativePath.js\";\nexport * from \"./parseDef.js\";\nexport * from \"./parseTypes.js\";\nexport * from \"./parsers/any.js\";\nexport * from \"./parsers/array.js\";\nexport * from \"./parsers/bigint.js\";\nexport * from \"./parsers/boolean.js\";\nexport * from \"./parsers/branded.js\";\nexport * from \"./parsers/catch.js\";\nexport * from \"./parsers/date.js\";\nexport * from \"./parsers/default.js\";\nexport * from \"./parsers/effects.js\";\nexport * from \"./parsers/enum.js\";\nexport * from \"./parsers/intersection.js\";\nexport * from \"./parsers/literal.js\";\nexport * from \"./parsers/map.js\";\nexport * from \"./parsers/nativeEnum.js\";\nexport * from \"./parsers/never.js\";\nexport * from \"./parsers/null.js\";\nexport * from \"./parsers/nullable.js\";\nexport * from \"./parsers/number.js\";\nexport * from \"./parsers/object.js\";\nexport * from \"./parsers/optional.js\";\nexport * from \"./parsers/pipeline.js\";\nexport * from \"./parsers/promise.js\";\nexport * from \"./parsers/readonly.js\";\nexport * from \"./parsers/record.js\";\nexport * from \"./parsers/set.js\";\nexport * from \"./parsers/string.js\";\nexport * from \"./parsers/tuple.js\";\nexport * from \"./parsers/undefined.js\";\nexport * from \"./parsers/union.js\";\nexport * from \"./parsers/unknown.js\";\nexport * from \"./selectParser.js\";\nexport * from \"./zodToJsonSchema.js\";\nimport { zodToJsonSchema } from \"./zodToJsonSchema.js\";\nexport default zodToJsonSchema;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAEe,2QAAA,CAAA,kBAAe", "ignoreList": [0], "debugId": null}}]}