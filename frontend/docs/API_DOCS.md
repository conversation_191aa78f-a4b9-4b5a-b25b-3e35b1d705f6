# WriteFlow API Documentation

This document provides details on the server actions available in the WriteFlow application, which act as the API for frontend components. All server actions return a Promise that resolves to an object with a `success` boolean field. If `success` is `false`, an `error` string field will be present.

## Table of Contents

1. [Authentication](#1-authentication)
2. [Data Persistence](#2-data-persistence)
3. [Writing Analysis](#3-writing-analysis)
4. [Character Generation](#4-character-generation)
5. [Novel Outline Generation](#5-novel-outline-generation)

---

### 1. Authentication

Handles user login, logout, and registration.

- **Action:** `login(credentials)`
- **File:** `src/app/actions.ts`
- **Description:** Authenticates a user.
- **Request:** `credentials` (object) with `email` and `password` strings.
- **Response (`success: true`):** `{ success: true, user: { name: string, email: string } }`
- **Response (`success: false`):** `{ success: false, error: string }`

---

- **Action:** `logout()`
- **File:** `src/app/actions.ts`
- **Description:** Logs out the current user.
- **Request:** No arguments.
- **Response:** `{ success: true }`

---

- **Action:** `register(userInfo)`
- **File:** `src/app/actions.ts`
- **Description:** Registers a new user.
- **Request:** `userInfo` (object) with `name`, `email`, and `password` strings.
- **Response (`success: true`):** `{ success: true, user: { name: string, email: string } }`
- **Response (`success: false`):** `{ success: false, error: string }`

---

### 2. Data Persistence

Handles saving project data.

- **Action:** `saveProject(project)`
- **File:** `src/app/actions.ts`
- **Description:** Saves the current state of a project for the authenticated user.
- **Request:** `project` (object) conforming to the `Project` type from `src/lib/types.ts`.
- **Response (`success: true`):** `{ success: true, message: string }`
- **Response (`success: false`):** `{ success: false, error: string }`

---

### 3. Writing Analysis

Provides AI-powered suggestions to improve a piece of text.

- **Action:** `getWritingAnalysis(input)`
- **File:** `src/app/actions.ts`

#### Request

The `input` object should conform to the `WritingImprovementSuggestionsInput` type.

- `text` (string, required): The block of text to be analyzed.

**Example Request:**

```javascript
import { getWritingAnalysis } from "@/app/actions";

const analysis = await getWritingAnalysis({
  text: "The quick brown fox jumps over the lazy dog. The dog was very lazy."
});
```

#### Response

If successful, the response object will contain a `suggestions` field.

- `suggestions` (string): A comprehensive report on the text, including suggestions for improvement. The string is formatted with markdown.

**Example Success Response (`success: true`):**

```json
{
  "success": true,
  "suggestions": "**Commonly Used Words:**\n- the (2)\n- lazy (2)\n\n**Average Sentence Length:**\n- 8.5 words\n\n**Suggestions for Improvement:**\n- Consider replacing 'very' with a stronger adjective."
}
```

---

### 4. Character Generation

Generates a unique character for a novel using AI.

- **Action:** `generateCharacter()`
- **File:** `src/app/actions.ts`

#### Request

This action takes no arguments.

#### Response

If successful, the response object will contain a `character` field.

- `character` (object): An object containing the details of the generated character.

**Example Success Response (`success: true`):**

```json
{
  "success": true,
  "character": {
    "name": "Kaelen Voss",
    "age": "28",
    "appearance": "Tall and wiry...",
    "personality": "Cynical and sarcastic...",
    "background": "An ex-mercenary..."
  }
}
```

---

### 5. Novel Outline Generation

Generates a novel outline based on a user-provided prompt.

- **Action:** `generateNovelOutline(input)`
- **File:** `src/app/actions.ts`

#### Request

The `input` object should conform to the `GenerateNovelOutlineInput` type.

- `prompt` (string, required): A brief description of the novel's plot or concept.

#### Response

If successful, the response object will contain an `outline` field.

- `outline` (object): An object containing the generated outline.

**Example Success Response (`success: true`):**

```json
{
  "success": true,
  "outline": {
      "outline": "**Part 1: The Ghost in the Machine**..."
  }
}
```
